package com.zizhiguanjia.model_core.view;

import android.app.Activity;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.graphics.Rect;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.RelativeLayout;

import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.KeyboardUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.zizhiguanjia.lib_base.decoration.GridSpaceItemDecoration;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.adapter.ExamCaseQuestionHListAdapter;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.model_core.config.ExamTypeConfig;
import com.zizhiguanjia.model_core.databinding.CoreCaseOptionLayoutBinding;
import com.zizhiguanjia.model_core.factory.CustomViewModelFactory;
import com.zizhiguanjia.model_core.listener.ExamCaseQuestionQhCall;
import com.zizhiguanjia.model_core.listener.ExamContentUserAnswerCall;
import com.zizhiguanjia.model_core.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_core.model.OriginImageBean;
import com.zizhiguanjia.model_core.navigator.ExamCaseQuestionNavigator;
import com.zizhiguanjia.model_core.viewmodel.ExamCaseOptionViewModel;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStore;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public class ExamCaseOptionView extends RelativeLayout implements ViewModelStoreOwner, View.OnClickListener, ExamCaseQuestionQhCall, ExamCaseQuestionNavigator, KeyboardUtils.OnSoftInputChangedListener,ViewTreeObserver.OnGlobalLayoutListener {
    //    private ExamCaseOptionLayoutBinding binding;
    private CoreCaseOptionLayoutBinding binding;
    private ExamCaseQuestionHListAdapter mAdapter;
    ExamCaseOptionViewModel model;
    private ExamContentUserAnswerCall caseDataCall;
    private ViewModelStore mViewModelStore;
    private List<ExamPackAccessDataBean.RecordsBean.QuestionsBean> mLists;
    private List<ExamPackAccessDataBean.RecordsBean.AnswersBean> rightAnswers;
    private List<ExamPackAccessDataBean.RecordsBean.AnswerKeyBean> analysisLists;
    private String jxjqTxt;
    private List<String> userAnswers;
    private Activity mActivity;
    private int questionState;
    private String questionIdss;
    private String questionNum;
    private ExamCoreThemeConfigThemeBean themeBean;
    private ExamCoreThemeConfigTextSizeBean textSizeBean;
    private ExamCoreStyleConfigBean styleConfigBean;
    public ExamCaseOptionView(Context context) {
        this(context, null);
    }

    public ExamCaseOptionView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ExamCaseOptionView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public ExamCaseOptionView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView();
    }

    public ExamCaseOptionView setRightListData(List<ExamPackAccessDataBean.RecordsBean.AnswersBean> rightAnswers) {
        if (rightAnswers != null) this.rightAnswers = rightAnswers;
        return this;
    }

    public ExamCaseOptionView setQuestionState(int questionStates) {
        this.questionState = questionStates;
        return this;
    }
    public ExamCaseOptionView setQuestionNum(String nums) {
        this.questionNum = nums;
        return this;
    }
    public ExamCaseOptionView setQuestionIds(String ids) {

        this.questionIdss = ids;
        return this;
    }

    public ExamCaseOptionView setAnalysisListData(List<ExamPackAccessDataBean.RecordsBean.AnswerKeyBean> analysisLists) {
        if (analysisLists != null) this.analysisLists = analysisLists;
        return this;
    }

    public ExamCaseOptionView setJtjq(String analysisLists) {
        if (analysisLists != null) this.jxjqTxt = analysisLists;
        return this;
    }

    public ExamCaseOptionView setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        if(styleConfigBean == null){
            return this;
        }
        this.styleConfigBean = styleConfigBean;
        this.themeBean = themeBean;
        this.textSizeBean = textSizeBean;
        model.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        binding.tvFaceBackMain1.setTextColor(Color.parseColor(themeBean.getAnalysisTitleTextColor()));
        binding.tvSkill.setTextColor(Color.parseColor(themeBean.getAnalysisTitleTextColor()));
        binding.tvAnswer.setTextColor(Color.parseColor(themeBean.getAnalysisTitleTextColor()));
        binding.viewLineSkill.setBackgroundColor(Color.parseColor(themeBean.getLineColor()));
        binding.viewLineSkillBottom.setBackgroundColor(Color.parseColor(themeBean.getLineColor()));
        binding.emvCaseWt.exammateriaisBinding.exmaViewTitle.setTextColor(Color.parseColor(themeBean.getAnalysisTitleTextColor()));
        binding.emvCaseWt.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        binding.viewLine.setBackgroundColor(Color.parseColor(themeBean.getLineColor()));
        binding.tvSkillContent.setTextSize(textSizeBean.getSize());
        binding.tvSkillContent.setTextColor(Color.parseColor(themeBean.getTextColor()));
        binding.emvCaseDn.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        binding.emvCaseJx.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        binding.examCasequestionTopEmlv.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        if(mAdapter != null) {
            mAdapter.setThemeConfigData(styleConfigBean, themeBean, textSizeBean);
        }
        binding.rlCaseMsg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionTabUnSelectBgColor())));
        binding.storeCaseMsg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionTabUnSelectStoreColor())));
        binding.etCaseMsg.setTextColor(Color.parseColor(themeBean.getQuestionTabUnSelectTextColor()));
        binding.tvCaseCountSum.setTextColor(Color.parseColor(themeBean.getQuestionTabUnSelectTextColor()));
        return this;
    }

    public ExamCaseOptionView setUserListDatas(List<String> userAnswers) {
        if (userAnswers != null) this.userAnswers = userAnswers;

        return this;
    }

    /**
     * 设置案例题各小题的知识点
     */
    public ExamCaseOptionView setCaseKnowledgePoints(List<String> knowledgePoints) {
        if (model != null) {
            model.setKnowledgePoints(knowledgePoints);
        }
        return this;
    }


    public ExamCaseOptionView setCaseDataCall(ExamContentUserAnswerCall caseDataCall){
        if(caseDataCall!=null)  this.caseDataCall=caseDataCall;
        return this;
    }
    public ExamCaseOptionView setDatas(List<ExamPackAccessDataBean.RecordsBean.QuestionsBean> questionsBean) {
        this.mLists = questionsBean;
        return this;
    }
    private String context,flags;
    public ExamCaseOptionView setContext(String txt, String flags) {
        this.context = txt;
        this.flags = flags;
        return this;
    }
    private List<OriginImageBean> originImageBeans;
    public ExamCaseOptionView setImage(List<OriginImageBean> objects) {
        this.originImageBeans = objects;
        return this;
    }
    public ExamCaseOptionView with(Activity mActivity) {
        this.mActivity = mActivity;
        KeyboardUtils.registerSoftInputChangedListener(mActivity.getWindow(), binding.etCaseMsg, this);
        return this;
    }

    private void initView() {
        binding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.core_case_option_layout, this, true);

        mViewModelStore = new ViewModelStore();
        model = new ViewModelProvider(this, new CustomViewModelFactory()).get(ExamCaseOptionViewModel.class);
        mAdapter = new ExamCaseQuestionHListAdapter(this);
        binding.rcyCaseQuestion.setLayoutManager(new LinearLayoutManager(getContext(), RecyclerView.HORIZONTAL, false));
        binding.rcyCaseQuestion.addItemDecoration(new GridSpaceItemDecoration(6, DpUtils.dp2px(getContext(), 0), DpUtils.dp2px(getContext(), 8)));
        binding.rcyCaseQuestion.setAdapter(mAdapter);
        binding.llFaceBackMain1.setOnClickListener(this);
        binding.llFaceBackMain.setOnClickListener(this);
        binding.llShaperMain1.setOnClickListener(this);
        binding.tvLookJq.setOnClickListener(this);
        binding.llMainCent.getViewTreeObserver().addOnGlobalLayoutListener(this);
        mAdapter.setThemeConfigData(styleConfigBean, themeBean, textSizeBean);
        setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
    }

    public void build() {

        model.initParams(caseDataCall, this);
        binding.setModel(model);
        model.initIssusListData(mLists, rightAnswers, analysisLists, userAnswers, questionState,jxjqTxt,questionIdss,questionNum);
        model.reshData(model.currentOption.get());
        model.currentLookDes.set("查看答案与解析");
        buildMastView();
    }
   private void  buildMastView(){
       binding.examCasequestionTopEmlv
               .setThemeConfigData(styleConfigBean,themeBean,textSizeBean)
               .setContext(context,
                       flags)
               .setImage(originImageBeans)
               .build();
    }
    @Override
    public void userQhQuestion(int postion) {
        model.qhSaveData(postion);
        model.reshData(model.currentOption.get());
        setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
    }

    @Override
    public void showCaseDnView(String txt) {
        binding.emvCaseDn
                .setThemeConfigData(styleConfigBean,themeBean,textSizeBean)
                .setContext(txt, "")
                .build();
    }

    @Override
    public void showCaseWtView(String txt) {
        binding.emvCaseWt
                .setThemeConfigData(styleConfigBean,themeBean,textSizeBean)
                .setContext(txt, "")
                .build();
    }

    @Override
    public void showCaseJxView(String txt) {
        binding.emvCaseJx
                .setThemeConfigData(styleConfigBean,themeBean,textSizeBean)
                .setContext(txt, "")
                .build();
        if(StringUtils.isEmpty(txt)){
//            binding.llFaceBackMain.setVisibility(VISIBLE);
            binding.llFaceBackMain1.setVisibility(GONE);
        }else {
            binding.llFaceBackMain1.setVisibility(VISIBLE);
//            binding.llFaceBackMain.setVisibility(GONE);
        }
        setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
    }

    @Override
    public void showIssusListData(List<String> list) {
        mAdapter.setDataItems(list);
    }

    @Override
    public void movingCursor(String msg) {
        binding.etCaseMsg.setText(StringUtils.isEmpty(msg) ? "" : msg);
        binding.etCaseMsg.setSelection(StringUtils.isEmpty(msg) ? 0 : msg.length());
    }

    @Override
    public void showCaseKnowledgePoint(String knowledgePoint) {
        if (StringUtils.isEmpty(knowledgePoint)) {
            binding.llCaseKnowledge.setVisibility(View.GONE);
        } else {
            binding.llCaseKnowledge.setVisibility(View.VISIBLE);
            binding.tvCaseKnowledge.setText(knowledgePoint);

            // 设置知识点区域的主题样式
            if (styleConfigBean != null && themeBean != null && textSizeBean != null) {
                binding.llCaseKnowledge.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionAnswerSelectBgColorNo())));
                binding.tvCaseKnowledgeTitle.setTextColor(Color.parseColor(themeBean.getAnalysisTitleTextColor()));
                binding.tvCaseKnowledge.setTextColor(Color.parseColor(themeBean.getQuestionAnswerSelectTextColorNo()));
                binding.tvCaseKnowledgeTitle.setTextSize(textSizeBean.getSize());
                binding.tvCaseKnowledge.setTextSize(textSizeBean.getSize() - 1);
            }
        }
    }

    @Override
    public void onSoftInputChanged(int height) {
        binding.tvLookAsy.setText(height > 0 ? "确定" : "查看答案与解析");
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.llShaperMain1||v.getId() == R.id.llShaperMain) {
            Bus.post(new MsgEvent(ExamTypeConfig.EXAM_SHAPER_TYPE));
        } else if (v.getId() == R.id.llFaceBackMain1) {
            Bus.post(new MsgEvent(ExamTypeConfig.EXAM_FACEBACK_TYPE));
        } else if (v.getId() == R.id.tvLookJq) {
            Bus.post(new MsgEvent(ExamTypeConfig.EXAM_BUYVIP_TYPE));
        }
    }

    @NonNull
    @Override
    public ViewModelStore getViewModelStore() {
        return mViewModelStore;
    }

    @Override
    protected void onDetachedFromWindow() {
        mViewModelStore.clear();
        super.onDetachedFromWindow();
    }

    @Override
    public void onGlobalLayout() {
        Rect r = new Rect();
        binding.llMainCent.getWindowVisibleDisplayFrame(r);
        int screenHeight = binding.llMainCent.getRootView().getHeight();
        int keypadHeight = screenHeight - r.bottom;
        if (keypadHeight > screenHeight * 0.15) { // 0.15 ratio is perhaps enough to determine keypad height.
            // 当键盘显示的时候走这里，最底部空出键盘占用的高度
            binding.getRoot().setPadding(0, 0, 0, keypadHeight);
            //延迟滚动到底部，为了防止焦点出现跳动
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    //将ScrollView滚动到最底部
                    binding.sllvCaseMain.fullScroll(View.FOCUS_DOWN);
                }
            }, 100);
        } else {
            // 当键盘隐藏的时候走这里，还原默认的显示
            binding.getRoot().setPadding(0, 0, 0, 0);
        }
    }
}
