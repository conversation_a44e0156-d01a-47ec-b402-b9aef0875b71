package com.zizhiguanjia.model_core.view;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.text.SpannableStringBuilder;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import com.wb.baselib.utils.SpanUtil;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_utils.utils.StringUtils;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.config.ExamTypeConfig;
import com.zizhiguanjia.model_core.databinding.CoreExamQuestionresultViewBinding;
import com.zizhiguanjia.model_core.model.OriginImageBean;
import com.zizhiguanjia.model_core.utils.ExamUtils;

import java.util.List;

public class ExamResultView extends LinearLayout implements View.OnClickListener {
    private CoreExamQuestionresultViewBinding binding;
    private List<String> mRightAnswers;
    private List<String> mUserAnswers;
    private String jx;
    private List<OriginImageBean> originImageBeans;
    private String jtjqTxt;
    private String errorTip;
    private ExamCoreStyleConfigBean styleConfigBean;
    private ExamCoreThemeConfigThemeBean themeBean;
    private ExamCoreThemeConfigTextSizeBean textSizeBean;
    private String examcodeTitle;

    public ExamResultView(Context context) {
        this(context, null);
    }

    public ExamResultView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ExamResultView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public ExamResultView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView();
    }

    /**
     * 设置错误提醒
     */
    public ExamResultView setErrorTip(String errorTip) {
        this.errorTip = errorTip;
        return this;
    }

    public ExamResultView setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
                                             ExamCoreThemeConfigTextSizeBean textSizeBean) {
        if (styleConfigBean == null) {
            return this;
        }
        this.styleConfigBean = styleConfigBean;
        this.themeBean = themeBean;
        this.textSizeBean = textSizeBean;
        binding.llJtjqVipTxtMain.setTextColor(Color.parseColor(themeBean.getTextColor()));
        binding.llJtjqVipTxtMain.setTextSize(textSizeBean.getSize());
        this.binding.examResultUserTipBottomLine.setBackgroundColor(Color.parseColor(themeBean.getLineColor()));
        this.binding.viewLineSkillBottom.setBackgroundColor(Color.parseColor(themeBean.getLineColor()));
        this.binding.examResultUserTip.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getErrorPeopleCountTipBgColor())));
        this.binding.examResultUserTip.setTextColor(Color.parseColor(themeBean.getErrorPeopleCountTipTextColor()));
        this.binding.examResultUserTip.setVisibility(styleConfigBean.isShowMemoryModel() ? View.GONE : View.VISIBLE);
        binding.examJxEmlv.exammateriaisBinding.exmaViewTitle.setTextSize(textSizeBean.getSize());
        binding.examJxEmlv.exammateriaisBinding.exmaViewTitle.setTextColor(Color.parseColor(themeBean.getTextColor()));
        binding.tvFaceBackMain1.setTextColor(Color.parseColor(themeBean.getAnalysisTitleTextColor()));
        binding.tvSkill.setTextColor(Color.parseColor(themeBean.getAnalysisTitleTextColor()));

        // 设置知识点区域的主题样式，参照选项部分的样式
        binding.llKnowledge.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getQuestionAnswerSelectBgColorNo())));
        // 知识点标题使用解析标题颜色
        binding.tvKnowledgeTitle.setTextColor(Color.parseColor(themeBean.getAnalysisTitleTextColor()));
        // 知识点内容使用选项文本颜色
        binding.tvKnowledge.setTextColor(Color.parseColor(themeBean.getQuestionAnswerSelectTextColorNo()));
        if (styleConfigBean.isShowMemoryModel()) {
            this.binding.examReslutStateTv.setVisibility(GONE);
            this.binding.examResultUserTv.setVisibility(GONE);
            this.binding.examResultUserTip.setVisibility(GONE);
        } else {
            this.binding.examReslutStateTv.setVisibility(VISIBLE);
            this.binding.examResultUserTv.setVisibility(VISIBLE);
            this.binding.examResultUserTip.setVisibility(VISIBLE);
        }
        boolean right = mUserAnswers != null && mRightAnswers != null && !mUserAnswers.isEmpty() && !mRightAnswers.isEmpty() &&
                ExamUtils.getInstance().getUserRight(mUserAnswers, mRightAnswers);
        if (!right && (mUserAnswers == null || mUserAnswers.size() == 0)) {
            binding.examReslutStateTv.setTextColor(Color.parseColor(themeBean.getAnalysisTitleTextColor()));
        }
        return this;
    }

    private void initView() {
        binding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.core_exam_questionresult_view, this, true);
        binding.llFaceBackMain.setOnClickListener(this);
        binding.llFaceBackMain1.setOnClickListener(this);
        binding.llShaperMain.setOnClickListener(this);
        binding.tvLookJq.setOnClickListener(this);


    }

    public ExamResultView setmRightAnswers(List<String> mRightAnswers) {
        this.mRightAnswers = mRightAnswers;
        return this;
    }

    public ExamResultView setJtjqTxt(String mRightAnswers) {
        this.jtjqTxt = mRightAnswers;
        return this;
    }

    public ExamResultView setmUserAnswers(List<String> mUserAnswers) {
        this.mUserAnswers = mUserAnswers;
        return this;
    }

    public ExamResultView setContext(String txt) {
        this.jx = txt;
        return this;
    }

    public ExamResultView setImage(List<OriginImageBean> objects) {
        this.originImageBeans = objects;
        return this;
    }

    public void build() {
        initData();
    }

    private void initData() {
        binding.examResultRightTv.setTextSize(textSizeBean.getSize() - 1);
        binding.examResultUserTv.setTextSize(textSizeBean.getSize() - 1);
        boolean right = ExamUtils.getInstance().getUserRight(mUserAnswers, mRightAnswers);
        SpannableStringBuilder spannableStringBuilder = new SpanUtil.SpanBuilder()
                .addForeColorSection("正确答案\t", Color.parseColor("#666666"))
                .addForeColorSection(ExamUtils.getInstance().getAnswerDesByList(mRightAnswers), Color.parseColor("#3163F6"))
                .getSpanStrBuilder();
        binding.examResultRightTv.setText(spannableStringBuilder);

        SpannableStringBuilder spannableStringBuilder1 = new SpanUtil.SpanBuilder()
                .addForeColorSection("您的答案\t", Color.parseColor("#666666"))
                .addForeColorSection(mUserAnswers == null || mUserAnswers.size() == 0 ? "暂未作答" : ExamUtils.getInstance().getAnswerDesByList(mUserAnswers), Color.parseColor(right ? "#3163F6" : "#F03F40"))
                .getSpanStrBuilder();
        binding.examResultUserTv.setText(spannableStringBuilder1);

        binding.examResultUserTip.setVisibility(right || this.errorTip == null || this.errorTip.isEmpty() || styleConfigBean != null && styleConfigBean.isShowMemoryModel() ? GONE : VISIBLE);
        binding.examResultUserTip.setText(this.errorTip);

        binding.examReslutStateTv.setText(right ? "回答正确\t" : (mUserAnswers == null || mUserAnswers.size() == 0 ? "未作答" : "回答错误"));
        binding.examReslutStateTv.setTextColor(right ? Color.parseColor("#3163F6") : (mUserAnswers == null || mUserAnswers.size() == 0 ? Color.parseColor("#333333") : Color.parseColor("#F03F40")));
        if (themeBean != null) {
            if (!right && (mUserAnswers == null || mUserAnswers.size() == 0)) {
                binding.examReslutStateTv.setTextColor(Color.parseColor(themeBean.getAnalysisTitleTextColor()));
            }
        }
        if (!(jx == null || StringUtils.isEmpty(jx)) || (styleConfigBean != null && styleConfigBean.isShowMemoryModel())) {
            binding.jxmainLL.setVisibility(VISIBLE);
            binding.llFaceBackMain.setVisibility(GONE);
            binding.llFaceBackMain1.setVisibility(VISIBLE);
            binding.examJxEmlv.setContext(jx, "").setImage(originImageBeans).build();
        } else {
            binding.jxmainLL.setVisibility(GONE);
            binding.llFaceBackMain.setVisibility(VISIBLE);
            binding.llFaceBackMain1.setVisibility(GONE);
        }
        //添加本题知识点
        if (null == examcodeTitle) {
            binding.llKnowledge.setVisibility(View.GONE);
        }else{
            binding.llKnowledge.setVisibility(View.VISIBLE);
            binding.tvKnowledge.setText(examcodeTitle);
        }
        initJtjq();
    }

    private void initJtjq() {

        if (jtjqTxt == null || jtjqTxt.isEmpty()) {
            binding.llJtjqMain.setVisibility(GONE);
        } else {
            binding.llJtjqMain.setVisibility(VISIBLE);
            if (UserHelper.isBecomeVip()) {
                binding.llJtjqNoVipMain.setVisibility(GONE);
                binding.llJtjqVipTxtMain.setVisibility(VISIBLE);
                binding.llJtjqVipTxtMain.setText(jtjqTxt);
            } else {
                binding.llJtjqNoVipMain.setVisibility(VISIBLE);
                binding.llJtjqVipTxtMain.setVisibility(GONE);
                binding.llJtjqNoVipTxtMain.setText(jtjqTxt);
            }
        }
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.llFaceBackMain || v.getId() == R.id.llFaceBackMain1) {
            Bus.post(new MsgEvent(ExamTypeConfig.EXAM_FACEBACK_TYPE));
        } else if (v.getId() == R.id.llShaperMain) {
            Bus.post(new MsgEvent(ExamTypeConfig.EXAM_SHAPER_TYPE));
        } else if (v.getId() == R.id.tvLookJq) {
            Bus.post(new MsgEvent(ExamTypeConfig.EXAM_BUYVIP_TYPE));
        }
    }

    public ExamResultView setExamcodeTitle(String examcodeTitle) {
        this.examcodeTitle = examcodeTitle;
        return this;
    }
}
