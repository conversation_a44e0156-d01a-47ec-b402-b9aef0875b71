package com.zizhiguanjia.model_core.bean;

import com.google.gson.annotations.SerializedName;

/**
 * 摸底测评开始页数据实体类
 * 匹配实际API返回的JSON格式
 */
public class BaselineAssessmentBean {
    
    @SerializedName("MajorName")
    private String majorName;              // 专业名称，如"建筑安管三类人员"
    
    @SerializedName("SubMajorName")
    private String subMajorName;           // 子专业名称，如"A证"
    
    @SerializedName("AreaName")
    private String areaName;               // 地区名称，如"北京"
    
    @SerializedName("ReviewContent")
    private String reviewContent;          // 测评内容说明
    
    @SerializedName("StudyPlan")
    private String studyPlan;              // 学习计划说明

    // Getter and Setter methods
    public String getMajorName() {
        return majorName;
    }

    public void setMajorName(String majorName) {
        this.majorName = majorName;
    }

    public String getSubMajorName() {
        return subMajorName;
    }

    public void setSubMajorName(String subMajorName) {
        this.subMajorName = subMajorName;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getReviewContent() {
        return reviewContent;
    }

    public void setReviewContent(String reviewContent) {
        this.reviewContent = reviewContent;
    }

    public String getStudyPlan() {
        return studyPlan;
    }

    public void setStudyPlan(String studyPlan) {
        this.studyPlan = studyPlan;
    }

    @Override
    public String toString() {
        return "BaselineAssessmentBean{" +
                "majorName='" + majorName + '\'' +
                ", subMajorName='" + subMajorName + '\'' +
                ", areaName='" + areaName + '\'' +
                ", reviewContent='" + reviewContent + '\'' +
                ", studyPlan='" + studyPlan + '\'' +
                '}';
    }
}
