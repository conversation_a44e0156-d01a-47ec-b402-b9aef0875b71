package com.zizhiguanjia.model_core.utils;

import android.graphics.Bitmap;
import android.graphics.Color;
import android.os.Build;
import android.text.style.ForegroundColorSpan;
import android.text.style.ImageSpan;
import android.view.Gravity;
import android.widget.TextView;

import com.binaryfork.spanny.Spanny;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_core.config.ExamQuestionTypeConfig;
import com.zizhiguanjia.model_core.config.ExamResultStyleTypeConfig;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
/**
 * 题库工具类
 */
public class ExamUtils {
    private static ExamUtils examUtils;
    public static  ExamUtils getInstance(){
        if(examUtils==null){
            synchronized (ExamUtils.class){
                return examUtils=new ExamUtils();
            }
        }
    return examUtils;
    }
    public String getQurstionTypeStr(int type){
        if(type== ExamQuestionTypeConfig.EXAM_QUESTION_DAN){
            return "单选";
        }else if(type== ExamQuestionTypeConfig.EXAM_QUESTION_DUO){
            return "多选";
        }else if(type== ExamQuestionTypeConfig.EXAM_QUESTION_AL||type==ExamQuestionTypeConfig.EXAM_QUESTIONP_AL_PAN){
            return "案例";
        }else if(type== ExamQuestionTypeConfig.EXAM_QUESTION_PAN){
            return "判断";
        }else {
            return "问答";
        }
    }
    public String getAnswerDesByList(List<String> lists){
        if(lists==null||lists.size()==0){
            return "未作答";
        }
        StringBuffer stringBuffer=new StringBuffer();
        for(String str:lists){
            String des=GetCharAscii.asciiToString(Integer.parseInt(str)+64);
            stringBuffer.append(des).append(",");
        }
        return stringBuffer.deleteCharAt(stringBuffer.length()-1).toString();
    }
    public boolean getUserRight(List<String> users, List<String> rights){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            rights.sort(Comparator.comparing(String::hashCode));
            users.sort(Comparator.comparing(String::hashCode));
        }
        return rights.toString().equals(users.toString());
    }
    public Spanny getExamSpanny(String context, String flgsTxt, boolean defaults, Float textSize){
        Spanny spanny = new Spanny();
        if(StringUtils.isEmpty(flgsTxt)){
            spanny.append(context);
        }else {
            if(defaults){
                spanny.append("\t "+context, new ImageSpan(AppUtils.getApp(),getExamQuestionFlags(flgsTxt,textSize))) ;
            }else {
                spanny.append(flgsTxt+"：",new ForegroundColorSpan(Color.parseColor("#FF3163F6")))
                        .append(context);
            }
        }
        return spanny;
    }
    private Bitmap getExamQuestionFlags(String flgsTxt, Float textSize){
        textSize = textSize != null ? textSize - 4 : 12;
        int width = DpUtils.dp2px(AppUtils.getApp(), textSize * 2.8F);
        int height = DpUtils.dp2px(AppUtils.getApp(), textSize * 1.6F);
        TextView textView = new TextView(AppUtils.getApp());
        textView.setGravity(Gravity.CENTER);
        textView.setPadding(0, DpUtils.dp2px(AppUtils.getApp(), 2), 0, DpUtils.dp2px(AppUtils.getApp(), 2));
        textView.setTextColor(AppUtils.getApp().getResources().getColor(R.color.white));
        textView.setTextSize(textSize);
        textView.setBackgroundResource(R.drawable.core_exam_tg_bg);
        textView.setText(flgsTxt);
        textView.setDrawingCacheEnabled(true);
        textView.layout(0, 0, width, height);
        Bitmap bitmap = Bitmap.createBitmap(textView.getDrawingCache());
        textView.destroyDrawingCache();
        return bitmap;
    }
    public List<String> getLeftAndRightFlags(int adressIndex){
       int postion=adressIndex-(BaseConfig.MAX_EXAM_PAGE -1)/2;
       int startPostion=postion<=0?0:postion;
       List<String> addrstList=new ArrayList<>();
       for(;startPostion<adressIndex;startPostion++){
           addrstList.add(startPostion+"");
       }
        int postion1=BaseConfig.MAX_EXAM_PAGE-(addrstList.size());
        int startPostion1=postion1+adressIndex;
        for(;adressIndex<startPostion1;adressIndex++){
            addrstList.add(adressIndex+"");
        }
       return addrstList;
    }
    public int getExamQuestionResultStyle(int type){
        if(type== ExamPaperTypeConfig.EXAM_PAPER_ZJLX||type== ExamPaperTypeConfig.EXAM_PAPER_TKLX
                ||type== ExamPaperTypeConfig.EXAM_PAPER_ERROR||type== ExamPaperTypeConfig.EXAM_PAPER_SAVE
                || type == ExamPaperTypeConfig.EXAM_PAPER_COURSE_DETAIL_QUESTION|| type == ExamPaperTypeConfig.EXAM_PAPER_ESTIONS_TO_GET_WRONG
                || type == ExamPaperTypeConfig.EXAM_INTELLIGENT_PRACTICE
                ||type == ExamPaperTypeConfig.EXAM_PAPER_HIGH_FREQUENCY
        ){
            return ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_DAN_ALSY;
        }else if(type== ExamPaperTypeConfig.EXAM_PAPER_LNZT||type== ExamPaperTypeConfig.EXAM_PAPER_MNZT||type== ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM||type== ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS
                ||type == ExamPaperTypeConfig.EXAM_EvaluationReport){
            return ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_NOR;
        }else {
            return ExamResultStyleTypeConfig.EXAM_RESULT_STYLE_SELECT_NO;
        }
    }
}
