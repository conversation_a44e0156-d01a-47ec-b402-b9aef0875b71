package com.zizhiguanjia.model_core.dialog;

import android.app.Activity;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_pop.code.BottomPopupView;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_rxtools.task.RxAsyncTask;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.decoration.GridSpaceItemDecoration;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.model_core.R;
import com.zizhiguanjia.model_core.adapter.ExamSheetChildAdapter;
import com.zizhiguanjia.model_core.adapter.ExamSheetListAdapter;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.lib_base.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_core.listener.ExamSheetCall;
import com.zizhiguanjia.model_core.model.AnswerSheetOptionBean;
import com.zizhiguanjia.model_core.model.CoreSheetBean;
import com.zizhiguanjia.model_core.model.ExamChaptersBean;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
public class SheetAnswerDialog extends BottomPopupView implements ExamSheetCall, View.OnClickListener {
    private RecyclerView mRecyclerView;
    private ExamSheetListAdapter adapter;
    private List<ExamChaptersBean> Records;
    private ExamSheetCall call;
    //    private LinearLayout mRestarExamtLl; mReStartExamTv
    private TextView mCurrentTv, mCountTv;
    private int paperType;
    private int paperId;
    private String pageTitle;
    //    private ImageView mRestImg;
    private LinearLayout llTags;
    private TextView tvTitle;
    private ExamSheetChildAdapter examSheetChildAdapter;
    private View bottom_icb;
    private ExamCoreStyleConfigBean styleConfigBean;
    private ExamCoreThemeConfigThemeBean themeBean;
    private ExamCoreThemeConfigTextSizeBean textSizeBean;
    /**
     * 列表底部操作按钮
     */
    private View mListBottomOptionsView;
    public SheetAnswerDialog(@NonNull Context context, List<ExamChaptersBean> Records, ExamSheetCall call, int paperType, int paperId, String title) {
        super(context);
        addInnerContent();
        this.Records = Records;
        this.call = call;
        this.paperId = paperId;
        this.paperType = paperType;
        this.pageTitle = title;
    }

    public ExamSheetListAdapter getAdapter() {
        return adapter;
    }

    public void setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean examCoreThemeConfigThemeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.styleConfigBean = styleConfigBean;
        this.themeBean = examCoreThemeConfigThemeBean;
        this.textSizeBean = textSizeBean;
        if(themeBean != null) {
            ((ImageView)findViewById(R.id.ivSheetLeft)).setImageTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getPageIndexSumColor())));
            ((TextView)findViewById(R.id.tvExamCount)).setTextColor(Color.parseColor(themeBean.getPageIndexSumColor()));
            ((TextView)findViewById(R.id.tvTipSure)).setTextColor(Color.parseColor(themeBean.getAnswerResultTipTextColor()));
            ((TextView)findViewById(R.id.tvTipError)).setTextColor(Color.parseColor(themeBean.getAnswerResultTipTextColor()));
            ((TextView)findViewById(R.id.tvTipNone)).setTextColor(Color.parseColor(themeBean.getAnswerResultTipTextColor()));
            findViewById(R.id.llContainerView).setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerCardDialogPageBgColor())));
            findViewById(R.id.bottom_icb).setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerCardDialogPageBgColor())));
            ((TextView)findViewById(R.id.tvTitle)).setTextColor(Color.parseColor(themeBean.getAnalysisTitleTextColor()));
            if(adapter != null){
                adapter.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
            }
            if(examSheetChildAdapter != null){
                examSheetChildAdapter.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
            }
            if(mListBottomOptionsView != null) {
                mListBottomOptionsView.setBackgroundColor(Color.parseColor(themeBean.getAnswerPageCardBgColor()));
            }

            //标记样式调整
            AppCompatImageView unDoView = findViewById(R.id.ivTagUnDo);
            unDoView.setBackgroundResource(R.drawable.solid_max);
            unDoView.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorNo())));
            unDoView.setImageResource(R.drawable.hollow_max);
            unDoView.setImageTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorNo())));

            if (!ExamPaperTypeConfig.isAnswerTopIc(paperType, pageTitle)) {
                findViewById(R.id.lnStateTip).setVisibility(VISIBLE);
            }else {
                findViewById(R.id.lnStateTip).setVisibility(GONE);
            }
        }
    }

    @Override
    protected int initLayoutResId() {
        return R.layout.core_exam_sheetanswer_layout;
    }

    @Override
    protected void initPopupContent() {
        super.initPopupContent();
        mCurrentTv = this.findViewById(R.id.tvExamCurrentCount);
        mCountTv = this.findViewById(R.id.tvExamCount);
        bottom_icb=this.findViewById(R.id.bottom_icb);
        mRecyclerView = this.findViewById(R.id.rcvExamSheetLog);
        llTags=this.findViewById(R.id.llTags);
        tvTitle=this.findViewById(R.id.tvTitle);
        if(paperType== ExamPaperTypeConfig.EXAM_PAPER_TKLX){
            initChildRecyCleData();
        }else {
            initGroupRecyCleData();
        }
        initListener();
        initData();
        initFootView();
        initListenter();

    }
    private boolean getStateExam() {
        if (paperType == ExamPaperTypeConfig.EXAM_PAPER_MNZT ||
                paperType == ExamPaperTypeConfig.EXAM_PAPER_LNZT ||
                paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM ||
                paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS||
                paperType == ExamPaperTypeConfig.EXAM_EvaluationReport) {
            return false;
        } else {
            return true;
        }
    }

    private void initFootView(){
        LinearLayout llRestartExam=this.findViewById(R.id.llRestartExam);
        LinearLayout mRestarExamtLl=this.findViewById(R.id.llBottomView);
        TextView mReStartExamTv=this.findViewById(R.id.tvReStartExam);
        ImageView mRestImg=this.findViewById(R.id.imgRest);
        if (paperType== ExamPaperTypeConfig.EXAM_PAPER_SAVE||
                paperType== ExamPaperTypeConfig.EXAM_PAPER_ERROR||
                paperType== ExamPaperTypeConfig.EXAM_PAPER_ALSY||
                paperType== ExamPaperTypeConfig.EXAM_PAPER_ALSYERROR) {
            mRestarExamtLl.setVisibility(GONE);
        } else {
            mRestarExamtLl.setVisibility(VISIBLE);
        }
        if (getStateExam()) {
            mReStartExamTv.setText("重新练习");
            mReStartExamTv.setTextColor(Color.parseColor("#3163F6"));
            mRestImg.setImageResource(R.drawable.core_exam_icon_cxlx);
            llRestartExam.setBackgroundResource(R.drawable.core_exam_sheet_post_bg);
        } else {
            mReStartExamTv.setText("交卷并查看结果");
            mReStartExamTv.setTextColor(Color.parseColor("#ffffff"));
            mRestImg.setImageResource(R.drawable.core_exam_icon_jj);
            llRestartExam.setBackgroundResource(R.drawable.core_exam_button_bg);
        }
        llRestartExam.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onUserSelectRestartExam(getStateExam());
            }
        });
        if(themeBean != null){
            mRestarExamtLl.setBackgroundColor(Color.parseColor(themeBean.getBgColor()));
        }
    }

    @Override
    public BasePopupView show() {
        BasePopupView show = super.show();
        setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        return show;
    }

    private void initGroupRecyCleData() {
        llTags.setVisibility(GONE);
        GridLayoutManager gridLayoutManager=new GridLayoutManager(getContext(),5);
        mRecyclerView.setLayoutManager(gridLayoutManager);
//        mRecyclerView.addItemDecoration(new GridSpaceItemDecoration(5, DpUtils.dp2px(getContext(),20),DpUtils.dp2px(getContext(),20)));
        adapter = new ExamSheetListAdapter(this,paperType);
        mListBottomOptionsView=LayoutInflater.from(getContext()).inflate(R.layout.layout_recy_foot,null);
        if(themeBean != null){
            mListBottomOptionsView.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerCardDialogPageBgColor())));
        }
        adapter.addFooterView(mListBottomOptionsView);
        adapter.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        mListBottomOptionsView.setOnClickListener(v -> onUserSelectRestartExam(getStateExam()));
        if (getStateExam()) {
            ((TextView)mListBottomOptionsView.findViewById(R.id.tvReStartExam)).setText("重新练习");
            ((TextView)mListBottomOptionsView.findViewById(R.id.tvReStartExam)).setTextColor(Color.parseColor("#3163F6"));
            ((ImageView)mListBottomOptionsView.findViewById(R.id.imgRest)).setImageResource(R.drawable.core_exam_icon_cxlx);
            ((LinearLayout)mListBottomOptionsView.findViewById(R.id.llRestartExam)).setBackgroundResource(R.drawable.core_exam_sheet_post_bg);
        } else {
            ((TextView)mListBottomOptionsView.findViewById(R.id.tvReStartExam)).setText("交卷并查看结果");
            ((TextView)mListBottomOptionsView.findViewById(R.id.tvReStartExam)).setTextColor(Color.parseColor("#ffffff"));
            ((ImageView)mListBottomOptionsView.findViewById(R.id.imgRest)).setImageResource(R.drawable.core_exam_icon_jj);
            ((LinearLayout)mListBottomOptionsView.findViewById(R.id.llRestartExam)).setBackgroundResource(R.drawable.core_exam_button_bg);
        }
        if(themeBean != null){
            mListBottomOptionsView.setBackgroundColor(Color.parseColor(themeBean.getBgColor()));
        }
        mRecyclerView.setHasFixedSize(true);
        mRecyclerView.setNestedScrollingEnabled(false);
        mRecyclerView.setItemViewCacheSize(200);
        RecyclerView.RecycledViewPool recycledViewPool = new RecyclerView.RecycledViewPool();
        mRecyclerView.setRecycledViewPool(recycledViewPool);
        mRecyclerView.setAdapter(adapter);
        gridLayoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
            if(adapter.getItemViewType(position)==1){
                return 5;
            }else if(adapter.getItemViewType(position)==200000){
                return 5;
            }
            return 1;
            }
        });
        initBuildData();

    }
    private void initListenter(){
        mRecyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                changeOptionsViewState(newState == RecyclerView.SCROLL_STATE_IDLE);
            }

            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                LogUtils.e("移动距离"+dy);
            }

        });
    }

    /**
     * 修改操作控件状态
     * @param scrollFinish 是否滑动完成
     */
    private void changeOptionsViewState(boolean scrollFinish){
        int lastPosition = -1;
        if(scrollFinish){
            LogUtils.e("停止滑动");
            RecyclerView.LayoutManager layoutManager = mRecyclerView.getLayoutManager();
            if(layoutManager instanceof GridLayoutManager){
                //通过LayoutManager找到当前显示的最后的item的position
                lastPosition = ((GridLayoutManager) layoutManager).findLastVisibleItemPosition();
            }else if(layoutManager instanceof LinearLayoutManager){
                lastPosition = ((LinearLayoutManager) layoutManager).findLastVisibleItemPosition();
            }else if(layoutManager instanceof StaggeredGridLayoutManager){
                //因为StaggeredGridLayoutManager的特殊性可能导致最后显示的item存在多个，所以这里取到的是一个数组
                //得到这个数组后再取到数组中position值最大的那个就是最后显示的position值了
                int[] lastPositions = new int[((StaggeredGridLayoutManager) layoutManager).getSpanCount()];
                ((StaggeredGridLayoutManager) layoutManager).findLastVisibleItemPositions(lastPositions);
                lastPosition = findMax(lastPositions);
            }
            if(lastPosition == mRecyclerView.getLayoutManager().getItemCount()-1){
                bottom_icb.setVisibility(GONE);
                if(mListBottomOptionsView != null){
                    mListBottomOptionsView.setVisibility(VISIBLE);
                }
            }else {
                bottom_icb.setVisibility(VISIBLE);
                if(mListBottomOptionsView != null){
                    mListBottomOptionsView.setVisibility(GONE);
                }
            }
        }else {
            bottom_icb.setVisibility(GONE);
            if(mListBottomOptionsView != null){
                mListBottomOptionsView.setVisibility(VISIBLE);
            }
            LogUtils.e("正在滑动");
        }
    }

    private int findMax(int[] lastPositions) {
        int max = lastPositions[0];
        for (int value : lastPositions) {
            if (value > max) {
                max = value;
            }
        }
        return max;
    }
    private void initBuildData(){

        RxJavaUtils.executeAsyncTask(new RxAsyncTask<List<ExamChaptersBean>,  List<CoreSheetBean>>(Records) {

            @Override
            public void doInUIThread(List<CoreSheetBean> examChaptersBeans) {
                adapter.setDataItems(examChaptersBeans);
                //延迟一定时间触发联动，使其可以获取最新结果数据
                MainThreadUtils.postDelayed(() -> {
                    //触发联动
                    changeOptionsViewState(true);
                },100L);
            }

            @Override
            public List<CoreSheetBean> doInIOThread(List<ExamChaptersBean> coreSheetBeans) {
                List<CoreSheetBean> coreSheetBeans1=new ArrayList<>();
                for (ExamChaptersBean examChaptersBean:coreSheetBeans){
                    CoreSheetBean coreSheetBean=new CoreSheetBean();
                    coreSheetBean.setQuestionNum(examChaptersBean.getQuestionName());
                    coreSheetBean.setChapterType(1);
                    coreSheetBeans1.add(coreSheetBean);
                    if(examChaptersBean.getItems()==null||examChaptersBean.getItems().size()==0){

                    }else {
                        for(AnswerSheetOptionBean answerSheetOptionBean:examChaptersBean.getItems()){
                            CoreSheetBean coreSheetBean2=new CoreSheetBean();
                            coreSheetBean2.setQuestionNum(answerSheetOptionBean.getQuestionNum()+"");
                            coreSheetBean2.setSee(answerSheetOptionBean.isSee());
                            coreSheetBean2.setDone(answerSheetOptionBean.isDone());
                            coreSheetBean2.setRight(answerSheetOptionBean.isRight());
                            coreSheetBean2.setChapterType(2);
                            coreSheetBeans1.add(coreSheetBean2);
                        }
                    }

                }
                return coreSheetBeans1;
            }
        });
    }
    private void initChildRecyCleData() {
        llTags.setVisibility(VISIBLE);
        GridLayoutManager gridLayoutManager=new GridLayoutManager(getContext(),5);
        mRecyclerView.setLayoutManager(gridLayoutManager);
        mRecyclerView.addItemDecoration(new GridSpaceItemDecoration(5, DpUtils.dp2px(getContext(),20),DpUtils.dp2px(getContext(),20)));
        examSheetChildAdapter = new ExamSheetChildAdapter(this,false,paperType);
        mRecyclerView.setHasFixedSize(true);
        mRecyclerView.setNestedScrollingEnabled(false);
        mRecyclerView.setItemViewCacheSize(200);
        RecyclerView.RecycledViewPool recycledViewPool = new RecyclerView.RecycledViewPool();
        mRecyclerView.setRecycledViewPool(recycledViewPool);
        mRecyclerView.setAdapter(examSheetChildAdapter);
        examSheetChildAdapter.setPaperId(paperId);
        examSheetChildAdapter.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        tvTitle.setText(Records.get(0).getQuestionName());
        examSheetChildAdapter.setDataItems(Records.get(0).getItems());
    }
    private void initData() {


        int count = 0;
        for (ExamChaptersBean examChaptersBean : Records) {
            count = count + examChaptersBean.getQuestionCount();
        }
        mCountTv.setText("/" + count);

    }

    private void initListener() {
    }

    @Override
    public void onUserSelectSheetQuestionNum(int questionNum, boolean isSee) {
        if (call == null) return;
        call.onUserSelectSheetQuestionNum(questionNum, isSee);
        this.dismiss();
    }

    @Override
    public void onUserSelectRestartExam(boolean restart) {
        if (call == null) return;
        if (restart) {
            MessageHelper.openGeneralCentDialog((Activity) getContext(), "确认重新练习",
                    "重新练习会清空当前做题记录，从第一题开始训练。是否重新开始？",
                    "取消",
                    "确定",
                    false, true, new GeneralDialogListener() {
                        @Override
                        public void onCancel() {
                        }
                        @Override
                        public void onConfim() {
                            call.onUserSelectRestartExam(restart);
                        }

                        /**
                         *
                         */
                        @Override
                        public void onDismiss() {

                        }
                    });
        } else {
            call.onUserSelectRestartExam(restart);
        }
        this.dismiss();
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.llRestartExam) {
            //重新开始考试
            onUserSelectRestartExam(getRestartExam());
        }
    }
    private boolean isShowButton(){
        if(paperType==ExamPaperTypeConfig.EXAM_PAPER_ERROR||paperType==ExamPaperTypeConfig.EXAM_PAPER_ALSY||paperType==ExamPaperTypeConfig.EXAM_PAPER_ALSYERROR||
                paperType==ExamPaperTypeConfig.EXAM_PAPER_SAVE){
            return  false;
        }
        return true;
    }
    private boolean getRestartExam() {
        if (paperType == ExamPaperTypeConfig.EXAM_PAPER_MNZT || paperType == ExamPaperTypeConfig.EXAM_PAPER_LNZT || paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM || paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS || paperType == ExamPaperTypeConfig.EXAM_EvaluationReport) {
            return false;
        } else {
            return true;
        }
    }
}