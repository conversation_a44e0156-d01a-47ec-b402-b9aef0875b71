<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp">

            <TextView
                android:id="@+id/exam_reslut_state_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="回答错误"
                android:textColor="#3163F6"
                android:textSize="18sp" />

            <LinearLayout
                android:id="@+id/llFaceBackMain"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:background="@drawable/core_exam_face_bg"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingLeft="15.5dp"
                    android:paddingTop="4dp"
                    android:paddingRight="15.5dp"
                    android:paddingBottom="4dp"
                    android:text="纠错"
                    android:textColor="#999999"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llShaperMain"
                android:layout_width="73dp"
                android:layout_height="27dp"
                android:layout_alignParentRight="true"
                android:background="@drawable/core_exam_shaper_bg"
                android:gravity="center"
                android:orientation="horizontal"
                android:visibility="gone">

                <ImageView
                    android:layout_width="13.5dp"
                    android:layout_height="13.5dp"
                    android:src="@drawable/core_exam_sharpe_img" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="3dp"
                    android:text="考朋友"
                    android:textColor="@color/white"
                    android:textSize="13sp" />

            </LinearLayout>
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="7.5dp"
            android:layout_marginRight="12dp">

            <TextView
                android:id="@+id/exam_result_right_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="回答政策"
                android:textColor="#3163F6"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/exam_result_user_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12.5dp"
                android:text="111111"
                android:textColor="#333333"
                android:textSize="16sp" />
        </LinearLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/exam_result_user_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:layout_marginTop="11dp"
            android:background="@drawable/solid_max"
            android:backgroundTint="#FFEEEE"
            android:paddingHorizontal="6dp"
            android:paddingVertical="2dp"
            android:textColor="#FF5854"
            android:textSize="13sp"
            android:visibility="gone"
            tools:text="本题有49人做过，做错率52%，请加强记"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/ll_knowledge"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="12dp"
            android:background="@drawable/core_exam_zsd_bg"
            android:orientation="vertical"
            android:padding="10dp">
            <LinearLayout
                android:gravity="center_vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <View
                    android:layout_width="5dp"
                    android:layout_height="5dp"
                    android:background="@drawable/dot_blue"
                    />
            <TextView
                android:id="@+id/tv_knowledge_title"
                android:layout_marginLeft="5dp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:textSize="14sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="本题知识点" />
            </LinearLayout>
            <TextView
                android:textColor="#333333"
                android:textSize="12sp"
                android:id="@+id/tv_knowledge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="法律法规--《安全生产法》--安全生产工作方针" />
        </LinearLayout>

        <View
            android:id="@+id/exam_result_user_tip_bottom_line"
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:layout_marginTop="12.5dp"
            android:layout_marginBottom="13.5dp"
            android:background="#F4F7F9" />

        <LinearLayout
            android:id="@+id/llJtjqMain"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:gravity="center"
                android:orientation="horizontal">

                <View
                    android:layout_width="3dp"
                    android:layout_height="12dp"
                    android:background="@drawable/core_exam_line_bg" />

                <TextView
                    android:id="@+id/tvSkill"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:text="解题技巧"
                    android:textColor="#333333"
                    android:textSize="16sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <RelativeLayout
                android:id="@+id/llJtjqNoVipMain"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginTop="14dp"
                android:layout_marginRight="12dp"
                android:layout_marginBottom="11dp"
                android:background="@drawable/core_exam_jtjq_bg"
                android:paddingLeft="9.5dp"
                android:paddingTop="8dp"
                android:paddingRight="4.5dp"
                android:paddingBottom="8.5dp">

                <TextView
                    android:id="@+id/llJtjqNoVipTxtMain"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="10dp"
                    android:layout_toLeftOf="@+id/tvLookJq"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="#A1794C"
                    android:textSize="17sp" />

                <TextView
                    android:id="@+id/tvLookJq"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:background="@drawable/core_exam_jtjq_bt_bg"
                    android:paddingLeft="11.5dp"
                    android:paddingTop="1.5dp"
                    android:paddingRight="7.5dp"
                    android:paddingBottom="1.5dp"
                    android:text="查看完整技巧"
                    android:textColor="#583218"
                    android:textSize="14sp" />
            </RelativeLayout>

            <TextView
                android:id="@+id/llJtjqVipTxtMain"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="12dp"
                android:textColor="#000000"
                android:textSize="17sp" />

            <View
                android:id="@+id/viewLineSkillBottom"
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_marginTop="7dp"
                android:layout_marginBottom="12.5dp"
                android:background="@color/translucent" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/jxmainLL"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:gravity="center_vertical">

                    <View
                        android:layout_width="3dp"
                        android:layout_height="12dp"
                        android:background="@drawable/core_exam_line_bg" />

                    <TextView
                        android:id="@+id/tvFaceBackMain1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:text="解析"
                        android:textColor="#333333"
                        android:textSize="16sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llFaceBackMain1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="5dp"
                    android:background="@drawable/core_exam_face_bg"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="15.5dp"
                        android:paddingTop="4dp"
                        android:paddingRight="15.5dp"
                        android:paddingBottom="4dp"
                        android:text="纠错"
                        android:textColor="#999999"
                        android:textSize="12sp" />

                </LinearLayout>
            </RelativeLayout>

            <com.zizhiguanjia.model_core.view.ExamMaterialsView
                android:id="@+id/exam_jx_emlv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:layout_marginBottom="14.5dp" />
        </LinearLayout>
    </LinearLayout>
</layout>