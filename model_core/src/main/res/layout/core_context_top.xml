<?xml version="1.0" encoding="utf-8" ?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <import type="android.view.View" />

        <variable
            name="model"
            type="com.zizhiguanjia.model_core.viewmodel.ExamCoreViewModel" />
    </data>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:background="@{model.bgColor}" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingLeft="15dp"
            android:paddingTop="13.5dp"
            android:paddingBottom="11.5sp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{model.numObs}"
                    android:textColor="#3163F6"
                    android:textSize="18sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text='@{"/"+model.countObs}'
                    android:textColor="@{model.themeBean.pageIndexSumColor}"
                    android:textSize="15sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llCountdownContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="15dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:visibility="@{model.downTimeObs?View.VISIBLE:View.GONE}">

                <TextView
                    android:id="@+id/tvCountdownLabel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="剩余时间"
                    android:textColor="#000000"
                    android:textSize="12sp"
                    android:layout_marginRight="8dp" />

                <TextView
                    android:id="@+id/tvDownTimes"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="01:00"
                    android:textColor="#FF6B35"
                    android:textSize="12sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#F4F4F4" />
    </LinearLayout>
</layout>