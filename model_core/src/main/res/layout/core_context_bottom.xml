<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_core.viewmodel.ExamCoreViewModel" />
    </data>
    <LinearLayout
        android:gravity="center_vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@{model.themeBean.bottomContainerBg}"
        android:orientation="horizontal"
        >
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="left"
            android:orientation="vertical">

            <LinearLayout
                android:layout_marginLeft="25.5dp"
                android:layout_marginRight="10dp"
                android:onClick="@{model::onClick}"
                android:id="@+id/examSaveLl"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/imgUserSave"
                    android:layout_width="18.5dp"
                    android:layout_height="17.5dp"
                    android:src="@drawable/core_save_no"
                    android:tint="@{model.themeBean.bottomContainerText}"/>

                <TextView
                    android:layout_marginLeft="3dp"
                    android:id="@+id/tvUserSave"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="收藏"
                    android:textColor="@{model.themeBean.bottomContainerText}"
                    android:textSize="12sp" />
                <TextView
                    android:layout_marginLeft="10dp"
                    android:id="@+id/tv_submit"
                    android:onClick="@{model::onClick}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="25dp"
                    android:paddingVertical="7.5dp"
                    android:background="@drawable/solid_max"
                    android:backgroundTint="#3163F6"
                    android:text="交卷"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:visibility="gone"
                    tools:visibility="visible"/>
            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_marginRight="14dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical|right"
            android:orientation="horizontal">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/lnResultCountTrue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="45dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="13dp"
                    android:layout_height="13dp"
                    android:src="@drawable/core_exam_result_yes"
                    android:scaleType="centerCrop"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="13sp"
                    android:textColor="@{model.themeBean.bottomContainerText}"
                    android:paddingHorizontal="3dp"
                    android:text="@{model.resultTrueCount}"/>

            </androidx.appcompat.widget.LinearLayoutCompat>
            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/lnResultCountFalse"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="45dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="13dp"
                    android:layout_height="13dp"
                    android:src="@drawable/core_exam_result_no"
                    android:scaleType="centerCrop"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="13sp"
                    android:textColor="@{model.themeBean.bottomContainerText}"
                    android:paddingHorizontal="3dp"
                    android:text="@{model.resultFalseCount}"/>

            </androidx.appcompat.widget.LinearLayoutCompat>

            <LinearLayout
                android:onClick="@{model::onClick}"
                android:id="@+id/examsheetLl"
                android:layout_width="70dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/examLookSheet"
                    android:layout_width="17dp"
                    android:layout_height="17dp"
                    android:src="@drawable/core_dtk"
                    android:tint="@{model.themeBean.bottomContainerText}"/>

                <TextView
                    android:layout_marginLeft="4.5dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="答题卡"
                    android:textColor="@{model.themeBean.bottomContainerText}"
                    android:textSize="12sp" />
            </LinearLayout>
            <LinearLayout
                android:layout_marginLeft="11dp"
                android:orientation="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:text="@{model.numObs}"
                    android:textSize="16sp"
                    android:textColor="#3163F6"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:text='@{"/"+model.countObs}'
                    android:textColor="#666666"
                    android:textSize="13sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</layout>