<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 背景 -->
    <item android:id="@android:id/background">
        <shape>
            <corners android:radius="10dp" />
            android:shape="rectangle">
            <solid android:color="#00000000"/>
            <stroke
                android:width="1dp"
                android:color="#59597B"/>
        </shape>
    </item>
    
    <!-- 进度 -->
    <item android:id="@android:id/progress">
        <scale android:scaleWidth="100%">
            <shape>
                <corners android:radius="10dp" />
                <gradient
                    android:angle="0"
                    android:endColor="#59597B"
                    android:startColor="#59597B"
                    android:type="linear" />
            </shape>
        </scale>
    </item>
</layer-list> 