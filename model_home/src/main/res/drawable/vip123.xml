<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape>
            <corners android:radius="16dp" /> <!-- 圆角半径 -->
            <gradient
                android:angle="152"
                android:endColor="#ffffd1af"
                android:startColor="#fffddfc3"
                android:type="linear"
                android:useLevel="true" />
            <corners
                android:bottomLeftRadius="40dp"
                android:bottomRightRadius="40dp"
                android:topLeftRadius="40dp"
                android:topRightRadius="40dp" />
        </shape>
    </item>
    <item
        android:bottom="1dp"
        android:left="1dp"
        android:right="1dp"
        android:top="1dp">
        <shape>
            <corners android:radius="15dp" /> <!-- 内部圆角半径稍小一些 -->
        </shape>
    </item>
</layer-list>