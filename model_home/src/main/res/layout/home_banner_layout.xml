<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_home.viewmodel.StudyViewModel" />
        <import type="android.view.View" />
    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="12dp"
        app:cardElevation="0dp">

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/banner_view_pager"
            android:layout_width="match_parent"
            android:minHeight="110dp"
            android:layout_height="120dp" />

        <!-- 指示器容器 -->
        <LinearLayout
            android:visibility="gone"
            android:id="@+id/indicator_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="8dp" />
    </androidx.cardview.widget.CardView>
</layout> 