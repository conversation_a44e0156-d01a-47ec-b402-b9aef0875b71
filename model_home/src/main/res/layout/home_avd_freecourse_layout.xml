<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data></data>
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <View
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:background="#F5F5F7"/>
        <TextView
            android:layout_marginTop="10dp"
            android:layout_marginLeft="12dp"
            android:text="免费公开课"
            android:textSize="17sp"
            android:textColor="#000000"
            android:textFontWeight="600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <RelativeLayout
            android:paddingRight="12dp"
            android:paddingLeft="12dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <androidx.recyclerview.widget.RecyclerView
                android:layout_marginTop="13dp"
                android:id="@+id/freecourseRey"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"/>
            <LinearLayout
                android:layout_marginBottom="33dp"
                android:id="@+id/freeNodata"
                android:gravity="center"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="21dp"
                android:orientation="vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <ImageView
                    android:src="@drawable/hone_backvideo_nodata"
                    android:layout_width="137.5dp"
                    android:layout_height="122dp"/>
                <TextView
                    android:textColor="#999999"
                    android:textSize="14.4sp"
                    android:text="暂无免费公开课"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>
</layout>