<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    tools:ignore="ResourceName"
    android:paddingStart="12dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_state"
        android:layout_width="15dp"
        android:layout_height="15dp"
        tools:src="@color/black"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="18.5dp"
        android:scaleType="centerCrop"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        tools:text="第1节 劳动防护用品配备及使用标准、安全带安全网"
        android:textSize="14sp"
        android:textColor="#333"
        android:layout_marginEnd="16.5dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toRightOf="@id/iv_state"
        android:layout_marginStart="12dp"
        app:layout_constraintRight_toLeftOf="@id/iv_options"
        android:layout_marginTop="15dp" />

    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/iv_options"
        android:layout_width="40dp"
        android:layout_height="30dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@null"
        android:layout_marginEnd="12dp"
        android:scaleType="center"
        tools:src="@color/black"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        android:layout_marginTop="5dp"
        android:textColor="#B3BACC"
        android:textSize="12.5sp"
        android:paddingBottom="15dp"
        tools:text="12:33" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@id/tv_time"
        app:layout_constraintTop_toTopOf="@id/tv_time"
        android:textColor="#B3BACC"
        android:textSize="12.5sp"
        android:paddingStart="13dp"
        android:visibility="gone"
        tools:visibility="visible"
        tools:text="上次看到09:11" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_question"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@id/tv_progress"
        app:layout_constraintTop_toTopOf="@id/tv_time"
        android:textColor="#007AFF"
        android:textSize="12.5sp"
        android:paddingStart="13dp"
        android:paddingEnd="2dp"
        android:visibility="gone"
        tools:visibility="visible"
        tools:text="剩余3道练习题" />
    
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_question_arrow"
        android:layout_width="8dp"
        android:layout_height="8dp"
        app:layout_constraintTop_toTopOf="@id/tv_question"
        app:layout_constraintBottom_toBottomOf="@id/tv_question"
        app:layout_constraintLeft_toRightOf="@id/tv_question"
        android:src="@drawable/common_arrow_bottom"
        android:tint="#007AFF"
        android:rotation="-90"
        android:visibility="gone"
        tools:visibility="visible"/>

    <View
        android:layout_width="0dp"
        android:layout_height="1dp"
        app:layout_constraintLeft_toLeftOf="@id/tv_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="#F3F3F3"/>

</androidx.constraintlayout.widget.ConstraintLayout>