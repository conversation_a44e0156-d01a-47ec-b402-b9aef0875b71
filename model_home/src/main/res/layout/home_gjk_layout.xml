<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data></data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingLeft="12dp"
        android:paddingTop="18.5dp"
        android:paddingRight="12dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="高阶精讲课"
                android:textColor="#000000"
                android:textFontWeight="600"
                android:textSize="17sp" />

            <ImageView
                android:layout_width="18dp"
                android:layout_height="20dp"
                android:layout_marginLeft="6.5dp"
                android:src="@drawable/home_gjjk" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/gjkMain"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp"
            android:background="@drawable/home_avd_gjk"
            android:orientation="vertical"
            android:paddingLeft="6dp"
            android:paddingTop="6dp"
            android:paddingRight="6dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/courseImg"
                    android:layout_width="114.5dp"
                    android:layout_height="139.5dp"
                    android:scaleType="center"
                    android:src="@drawable/qst_img" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="16dp"
                    android:layout_toRightOf="@+id/courseImg"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="4月北京3小时突击课"
                        android:textColor="#000000"
                        android:textFontWeight="600"
                        android:textSize="15sp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:background="@drawable/home_flags_bg">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="4dp"
                            android:layout_marginTop="1dp"
                            android:layout_marginRight="4dp"
                            android:layout_marginBottom="1dp"
                            android:text="适合考前突击的学员课程适合考前突击"
                            android:textColor="#395FCA"
                            android:textSize="10.5sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="¥"
                                android:textColor="#F42200"
                                android:textSize="15sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="3dp"
                                android:text="300"
                                android:textColor="#F42200"
                                android:textFontWeight="600"
                                android:textSize="25.5sp" />
                        </LinearLayout>

                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="7dp"
                        android:text="开课时间：4月27日 14:00-17:00"
                        android:textColor="#666A6B"
                        android:textSize="13sp" />
                </LinearLayout>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="4.5dp"
                android:layout_marginBottom="18.5dp"
                android:background="@drawable/home_xx" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16.5dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="34dp"
                    android:layout_height="34dp"
                    android:src="@drawable/home_xypj" />

                <com.stx.xmarqueeview.XMarqueeView xmlns:app="http://schemas.android.com/apk/res-auto"
                    android:id="@+id/upview2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    app:isSetAnimDuration="true"
                    app:isSingleLine="true"
                    app:marquee_count="2" />
            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_marginBottom="33dp"
            android:id="@+id/gjkNodata"
            android:gravity="center"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="21dp"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:src="@drawable/hone_backvideo_nodata"
                android:layout_width="137.5dp"
                android:layout_height="122dp"/>
            <TextView
                android:textColor="#999999"
                android:textSize="14.4sp"
                android:text="暂无高阶课"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </LinearLayout>
</layout>