<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/solid_radio_7_top"
    android:backgroundTint="@color/white"
    tools:ignore="ResourceName">

    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:background="@null"
        android:paddingHorizontal="12dp"
        android:paddingVertical="7dp"
        android:scaleType="fitCenter"
        android:src="@drawable/common_left_back_black_img"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_title"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="31dp"
        android:text="升级提醒"
        android:textColor="#ff0e152b"
        android:textSize="23sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_des"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="12dp"
        android:textColor="#ff000000"
        android:textSize="16sp"
        android:textStyle="bold"
        android:paddingTop="22.5dp"
        android:lineSpacingExtra="4.5dp"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        tools:text="账号“133***3245”已经在抖音购买了VIP会员，请确认是否开通北京A本题库。" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_change"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="12dp"
        android:text="修改地区、科目或考试类型 >"
        android:textColor="#ff007aff"
        android:textSize="14sp"
        android:minHeight="0dp"
        android:layout_marginTop="4dp"
        app:layout_constraintTop_toBottomOf="@id/tv_des"
        app:layout_constraintLeft_toLeftOf="parent" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ln_type"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="23.5dp"
        app:layout_constraintTop_toBottomOf="@id/btn_change"
        android:layout_marginHorizontal="12dp"
        android:background="@drawable/solid_radio_4"
        android:backgroundTint="#F6F7FB"
        android:gravity="center_vertical"
        android:paddingStart="13.5dp"
        android:paddingEnd="9.5dp"
        android:paddingVertical="12dp"
        android:visibility="gone"
        tools:visibility="visible">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="15sp"
            android:textColor="#666666"
            android:textStyle="bold"
            android:text="已选择考试：" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_type_des"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="15sp"
            android:textColor="#4D7FF7"
            android:textStyle="bold"
            android:layout_weight="1"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="道路运输 安全生产管理人员" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textColor="#999999"
            android:paddingHorizontal="3dp"
            android:text="更改" />

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="8dp"
            android:layout_height="13dp"
            android:src="@drawable/common_right_img"
            android:backgroundTint="#AAACAD"/>

    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ln_location"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@id/ln_type"
        android:layout_marginHorizontal="12dp"
        android:background="@drawable/solid_radio_4"
        android:backgroundTint="#F6F7FB"
        android:gravity="center_vertical"
        android:paddingStart="13.5dp"
        android:paddingEnd="9.5dp"
        android:paddingVertical="12dp"
        android:visibility="gone"
        tools:visibility="visible">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="15sp"
            android:textColor="#666666"
            android:textStyle="bold"
            android:text="已选择地区：" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_location_des"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="15sp"
            android:textColor="#4D7FF7"
            android:textStyle="bold"
            android:layout_weight="1"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="道路运输 安全生产管理人员" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textColor="#999999"
            android:paddingHorizontal="3dp"
            android:text="更改" />

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="8dp"
            android:layout_height="13dp"
            android:src="@drawable/common_right_img"
            android:backgroundTint="#AAACAD"/>

    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ln_level"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@id/ln_location"
        android:layout_marginHorizontal="12dp"
        android:background="@drawable/solid_radio_4"
        android:backgroundTint="#F6F7FB"
        android:gravity="center_vertical"
        android:paddingStart="13.5dp"
        android:paddingEnd="9.5dp"
        android:paddingVertical="12dp"
        android:visibility="gone"
        tools:visibility="visible">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="15sp"
            android:textColor="#666666"
            android:textStyle="bold"
            android:text="已选择科目：" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_level_des"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="15sp"
            android:textColor="#4D7FF7"
            android:textStyle="bold"
            android:layout_weight="1"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="道路运输 安全生产管理人员" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textColor="#999999"
            android:paddingHorizontal="3dp"
            android:text="更改" />

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="8dp"
            android:layout_height="13dp"
            android:src="@drawable/common_right_img"
            android:backgroundTint="#AAACAD"/>

    </androidx.appcompat.widget.LinearLayoutCompat>
    
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_hint"
        android:layout_width="14dp"
        android:layout_height="14dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_hint"
        app:layout_constraintBottom_toBottomOf="@+id/tv_hint"
        android:layout_marginStart="12dp"
        android:src="@drawable/home_icon_dy_vip_update_hint"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@id/iv_hint"
        app:layout_constraintTop_toBottomOf="@id/ln_level"
        android:text="注意：开通后不可修改，请谨慎选择。"
        android:textColor="#ff666666"
        android:textSize="14sp"
        android:paddingStart="3dp"
        android:layout_marginTop="22.5dp"/>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_open"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintWidth_percent="0.762"
        app:layout_constraintTop_toBottomOf="@id/tv_hint"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="41.5dp"
        android:layout_marginBottom="31dp"
        android:background="@drawable/solid_radio_4"
        android:backgroundTint="#3163F6"
        android:paddingVertical="12.5dp"
        android:text="确认开通"
        android:textColor="#fffffeff"
        android:textSize="16sp"
        android:gravity="center"
        />

</androidx.constraintlayout.widget.ConstraintLayout>