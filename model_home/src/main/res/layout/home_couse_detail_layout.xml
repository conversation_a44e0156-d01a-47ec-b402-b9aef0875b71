<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">
   <androidx.appcompat.widget.LinearLayoutCompat
       android:layout_width="match_parent"
       android:layout_height="wrap_content"
       android:orientation="vertical">

       <androidx.constraintlayout.widget.ConstraintLayout
           android:layout_width="match_parent"
           android:layout_height="wrap_content">

           <ImageView
               android:id="@+id/image1"
               android:scaleType="centerCrop"
               app:layout_constraintTop_toTopOf="parent"
               android:src="@drawable/course_detail_bg"
               android:layout_width="match_parent"
               android:layout_height="0dp"
               app:layout_constraintBottom_toBottomOf="@+id/cd_play_view"
               />

           <com.wb.lib_weiget.titlebar.TitleBar
               android:id="@+id/tbflutter"
               app:tb_leftType="imageButton"
               app:tb_centerText="安全通关精讲课"
               app:tb_centerTextColor="@color/black"
               app:tb_leftImageResource="@drawable/common_left_back_write_img"
               app:tb_fillStatusBar="true"
               app:tb_statusBarColor="@color/transparent"
               app:tb_titleBarColor="@color/transparent"
               app:tb_bottomLineColor="@color/transparent"
               android:layout_width="match_parent"
               android:layout_height="wrap_content"
               app:layout_constraintTop_toTopOf="parent"/>

           <androidx.cardview.widget.CardView
               android:id="@+id/cd_play_view"
               android:layout_width="match_parent"
               android:layout_height="wrap_content"
               app:layout_constraintTop_toBottomOf="@id/tbflutter"
               app:cardCornerRadius="5dp"
               app:cardBackgroundColor="@color/transparent"
               app:contentPaddingBottom="3dp"
               app:cardElevation="0dp"
               android:layout_marginLeft="12dp"
               android:layout_marginRight="12dp"
               android:translationY="3dp">
               <com.zizhiguanjia.model_home.view.CourseDetailPlayView
                   android:id="@+id/jz_video"
                   android:layout_width="match_parent"
                   android:layout_height="179dp"/>
           </androidx.cardview.widget.CardView>

       </androidx.constraintlayout.widget.ConstraintLayout>

       <LinearLayout
           android:orientation="vertical"
           android:layout_width="match_parent"
           android:layout_height="match_parent"
           android:background="@color/white">
           <androidx.cardview.widget.CardView
               android:layout_width="match_parent"
               android:layout_height="wrap_content">
               <com.kevin.slidingtab.SlidingTabLayout
                   android:id="@+id/slidTl"
                   xmlns:app="http://schemas.android.com/apk/res-auto"
                   app:tl_indicator_is_top="true"
                   app:stl_tabMode="fixed"
                   app:stl_leftPadding="90dp"
                   app:stl_rightPadding="90dp"
                   app:stl_tabIndicatorColor="#007AFF"
                   app:stl_tabIndicatorHeight="3dp"
                   app:stl_tabIndicatorWidth="19dp"
                   app:stl_tabPaddingEnd="8dp"
                   app:stl_tabPaddingStart="8dp"
                   app:stl_tabSelectedTextColor="#000000"
                   app:stl_tabSelectedTextSize="18sp"
                   app:stl_tabTextBold="false"
                   app:stl_tabTextSelectedBold="true"
                   app:stl_tabTextColor="#000000"
                   app:stl_tabIndicatorCornerRadius="1.5dp"
                   app:stl_tabIndicatorMarginBottom="8dp"
                   app:stl_tabTextShowScaleAnim="true"
                   app:stl_tabIndicatorCreep="true"
                   app:stl_tabTextSize="18sp"
                   android:layout_width="match_parent"
                   android:layout_height="50dp" />
           </androidx.cardview.widget.CardView>
           <com.zizhiguanjia.lib_base.view.ScrollSetViewPager
               android:id="@+id/advVp"
               android:layout_width="match_parent"
               android:layout_height="match_parent" />
       </LinearLayout>
   </androidx.appcompat.widget.LinearLayoutCompat>

   <androidx.constraintlayout.widget.ConstraintLayout
       android:id="@+id/cl_buy"
       android:layout_width="match_parent"
       android:layout_height="wrap_content"
       android:layout_alignParentBottom="true"
       android:visibility="gone"
       tools:visibility="visible">

       <androidx.appcompat.widget.AppCompatImageView
           android:layout_width="match_parent"
           android:layout_height="match_parent"
           android:layout_alignParentBottom="true"
           android:src="@drawable/course_detail_buy"/>
       <androidx.constraintlayout.widget.Guideline
           android:id="@+id/gl_top"
           android:layout_width="wrap_content"
           android:layout_height="wrap_content"
           android:orientation="horizontal"
           app:layout_constraintTop_toTopOf="parent"
           app:layout_constraintGuide_percent="0.35"/>

       <androidx.constraintlayout.widget.Guideline
           android:id="@+id/gl_left"
           android:layout_width="wrap_content"
           android:layout_height="wrap_content"
           android:orientation="vertical"
           app:layout_constraintTop_toTopOf="parent"
           app:layout_constraintGuide_percent="0.17"/>

       <androidx.appcompat.widget.AppCompatTextView
           android:id="@+id/tv_unit"
           android:layout_width="wrap_content"
           android:layout_height="wrap_content"
           app:layout_constraintTop_toTopOf="@+id/gl_top"
           app:layout_constraintLeft_toLeftOf="@id/gl_left"
           android:textSize="13sp"
           android:textStyle="bold"
           android:textColor="#303541"
           android:text="￥"/>

       <androidx.appcompat.widget.AppCompatTextView
           android:id="@+id/tv_price"
           android:layout_width="wrap_content"
           android:layout_height="wrap_content"
           app:layout_constraintLeft_toRightOf="@id/tv_unit"
           app:layout_constraintBaseline_toBaselineOf="@id/tv_unit"
           android:textSize="21sp"
           android:textStyle="bold"
           android:textColor="#303541"
           tools:text="300"/>

   </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>