<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp">

    <ImageView
        android:id="@+id/course_image"
        android:layout_width="100dp"
        android:layout_height="130dp"
        android:scaleType="centerCrop"
        android:background="#F6F6F6"
        android:src="@drawable/default_course_image" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layout_marginStart="16dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/course_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="#282929"
            android:textStyle="bold"
            android:maxLines="2"
            android:ellipsize="end"
            android:text="安全员考前冲刺直播" />

        <TextView
            android:id="@+id/teacher_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="#E9F7FE"
            android:paddingStart="6dp"
            android:paddingEnd="6dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:textColor="#395FCA"
            android:textSize="12sp"
            android:text="讲师：张老师" />

        <TextView
            android:id="@+id/live_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:textColor="#666A6B"
            android:textSize="12sp"
            android:text="开课时间：07-09 08:00 - 07-09 22:00" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/live_subscribed_count"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="12sp"
                android:textColor="#666A6B"
                android:text="已有 0 人预约" />

            <Button
                android:id="@+id/status_button"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:textSize="14sp"
                android:text="预约"
                android:textColor="#007AFF"
                android:background="@drawable/bg_button_reserve" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout> 