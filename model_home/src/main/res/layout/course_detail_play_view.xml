<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@android:color/black"
    android:descendantFocusability="afterDescendants">

    <FrameLayout
        android:id="@+id/surface_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    </FrameLayout>

    <View
        android:id="@+id/view_bottom_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentRight="true"/>

    <ImageView
        android:id="@+id/poster"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentBottom="true"
        android:adjustViewBounds="true"
        android:background="#000000"
        android:scaleType="fitXY" />

    <LinearLayout
        android:id="@+id/layout_bottom"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/jz_bottom_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="invisible">

        <TextView
            android:id="@+id/current"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:text="00:00"
            android:textColor="#ffffff" />

        <SeekBar
            android:id="@+id/bottom_seek_progress"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1.0"
            android:background="@null"
            android:max="100"
            android:maxHeight="1dp"
            android:minHeight="1dp"
            android:paddingLeft="12dp"
            android:paddingTop="8dp"
            android:paddingRight="12dp"
            android:paddingBottom="8dp"
            android:progressDrawable="@drawable/jz_bottom_seek_progress"
            android:thumb="@drawable/jz_bottom_seek_poster" />

        <TextView
            android:id="@+id/total"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:00"
            android:textColor="#ffffff" />

        <TextView
            android:id="@+id/tv_speed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:paddingVertical="2dp"
            android:paddingHorizontal="8dp"
            android:text="倍速"
            android:textSize="14sp"
            android:layout_marginHorizontal="10dp"
            android:background="@drawable/hollow_max"
            android:backgroundTint="@color/white"
            android:textAlignment="center"
            android:textColor="#ffffff" />
        <TextView
            android:id="@+id/clarity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:paddingLeft="20dp"
            android:text="clarity"
            android:textAlignment="center"
            android:textColor="#ffffff" />
        <ImageView
            android:id="@+id/fullscreen"
            android:layout_width="40dp"
            android:layout_height="fill_parent"
            android:paddingRight="14dp"
            android:scaleType="centerInside"
            android:src="@drawable/home_video_qp" />
    </LinearLayout>

    <ProgressBar
        android:id="@+id/bottom_progress"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="1.5dp"
        android:layout_alignParentBottom="true"
        android:max="100"
        android:progressDrawable="@drawable/jz_bottom_progress" />

    <ImageView
        android:id="@+id/back_tiny"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginLeft="6dp"
        android:layout_marginTop="6dp"
        android:background="@drawable/jz_click_back_tiny_selector"
        android:visibility="gone" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:background="@drawable/jz_title_bg"
        android:paddingStart="10dp"
        android:paddingLeft="10dp"
        android:visibility="gone"
        tools:visibility="visible">

        <View
            android:id="@+id/view_location"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_marginTop="12dp"
            android:layout_width="0dp"
            android:layout_height="26dp"/>

        <ImageView
            android:id="@+id/back"
            android:layout_width="26dp"
            android:layout_height="26dp"
            app:layout_constraintTop_toTopOf="@id/view_location"
            app:layout_constraintLeft_toLeftOf="parent"
            android:padding="3dp"
            android:scaleType="centerInside"
            android:src="@drawable/jz_click_back_selector" />

        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingLeft="0dp"
            android:paddingRight="12dp"
            android:minHeight="26dp"
            android:gravity="center_vertical|start"
            app:layout_constraintTop_toTopOf="@id/view_location"
            app:layout_constraintLeft_toRightOf="@id/back"
            app:layout_constraintRight_toLeftOf="@id/volume"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="#ffffff"
            android:textSize="15sp" />
        <ImageView
            android:id="@+id/volume"
            app:layout_constraintRight_toLeftOf="@id/battery_time_layout"
            app:layout_constraintTop_toTopOf="@id/view_location"
            app:layout_constraintBottom_toBottomOf="@id/view_location"
            android:layout_marginEnd="10.5dp"
            android:layout_width="24dp"
            android:layout_height="18.5dp"
            android:scaleType="fitCenter"
            android:src="@drawable/home_video_jy"
            />
        <LinearLayout
            android:id="@+id/battery_time_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/back"
            app:layout_constraintBottom_toBottomOf="@id/back"
            android:layout_marginEnd="12dp"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:id="@+id/battery_level"
                android:layout_width="23dp"
                android:layout_height="10dp"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/jz_battery_level_10" />

            <TextView
                android:id="@+id/video_current_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:textColor="#ffffffff"
                android:textSize="12.0sp" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/loading"
        android:layout_width="@dimen/jz_start_button_w_h_normal"
        android:layout_height="@dimen/jz_start_button_w_h_normal"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:indeterminateDrawable="@drawable/jz_loading"
        android:visibility="invisible" />

    <LinearLayout
        android:id="@+id/start_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:layout_gravity="center_vertical">

        <ImageView
            android:id="@+id/start"
            android:layout_width="@dimen/jz_start_button_w_h_normal"
            android:layout_height="@dimen/jz_start_button_w_h_normal"
            android:src="@drawable/jz_click_play_selector" />
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_replay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:background="#99000000"
        android:visibility="gone"
        tools:visibility="visible">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:gravity="center"
            android:textSize="15sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:text="本节已学完，继续加油!"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/replay_text"/>

        <TextView
            android:id="@+id/replay_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/question_text"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:text="重新播放"
            android:textColor="#ffffff"
            android:textSize="13sp"
            android:visibility="invisible"
            android:drawableTop="@drawable/course_detail_play_view_restart"
            android:drawablePadding="8dp"/>

        <TextView
            android:id="@+id/question_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toRightOf="@id/replay_text"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:text="本节习题"
            android:textColor="#ffffff"
            android:textSize="13sp"
            android:drawablePadding="8dp"
            android:drawableTop="@drawable/course_detail_play_view_question"/>

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/btn_next"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:background="@null"
            android:src="@drawable/course_detail_play_view_next"
            android:layout_marginEnd="14dp"
            android:layout_marginBottom="17dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_audition"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="可试听第1节"
        android:paddingVertical="2.5dp"
        android:paddingHorizontal="5dp"
        android:textSize="12sp"
        android:textColor="#fff"
        android:background="@drawable/solid_max"
        android:backgroundTint="#88000000"
        android:layout_above="@id/layout_bottom"
        android:layout_marginStart="16dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:translationY="10dp"
        />

    <LinearLayout
        android:id="@+id/retry_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="invisible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/video_loading_failed"
            android:textColor="@android:color/white"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/retry_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:background="@drawable/jz_retry"
            android:paddingLeft="9dp"
            android:paddingTop="4dp"
            android:paddingRight="9dp"
            android:paddingBottom="4dp"
            android:text="@string/click_to_restart"
            android:textColor="@android:color/white"
            android:textSize="14sp" />
    </LinearLayout>
</RelativeLayout>