<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="bean"
            type="com.zizhiguanjia.model_home.bean.ItemsBean" />
    </data>
    <www.linwg.org.lib.LCardView
        xmlns:app="http://schemas.android.com/apk/res-auto"
        app:cardBackgroundColor="#ffffff"
        app:cornerRadius="7.5dp"
        app:leftOffset="0dp"
        app:rightOffset="0.5dp"
        app:shadowColor="#000000"
        app:elevation="5dp"
        app:shadowSize="5dp"
        app:shadowStartAlpha="10"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        >
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/home_yy_bg"
            android:paddingLeft="3dp"
            android:paddingTop="3dp"
            android:paddingRight="2dp"
            android:paddingBottom="3dp"
            android:text='@{bean.liveSubscribedCount+"人预约"}'
            android:textColor="#DB4219"
            android:textSize="11.5sp" />

        <LinearLayout
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:layout_marginTop="9dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/txImage"
                android:layout_marginRight="10dp"
                android:layout_width="41dp"
                android:layout_height="41dp"
                android:scaleType="fitXY"
                android:src="@drawable/qst_img" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="41dp"
                android:orientation="vertical">

                <TextView
                    android:singleLine="true"
                    android:ellipsize="end"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{bean.title}"
                    android:textColor="#000000"
                    android:textFontWeight="600"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:text="@{bean.liveTime}"
                    android:textColor="#A7A7A7"
                    android:textSize="12sp" />
            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_marginBottom="11dp"
            android:layout_marginTop="19dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/home_goclass_bg"
                android:paddingLeft="23.5dp"
                android:paddingTop="6dp"
                android:paddingRight="23.5dp"
                android:paddingBottom="6dp"
                android:text="立即听课"
                android:textColor="@color/white"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>
    </www.linwg.org.lib.LCardView>
</layout>