<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:id="@+id/cd_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:paddingVertical="10dp"
                android:text="课程目录"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_title_sum"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="6dp"
                android:textColor="#999"
                android:textSize="14sp"
                app:layout_constraintBaseline_toBaselineOf="@id/tv_title"
                app:layout_constraintLeft_toRightOf="@id/tv_title"
                tools:text="共18节" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_comment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:background="@drawable/hollow_max"
                android:backgroundTint="#E5E5EA"
                android:drawableLeft="@drawable/common_edit_black"
                android:drawablePadding="3dp"
                android:minHeight="0dp"
                android:minWidth="0dp"
                android:paddingHorizontal="10dp"
                android:paddingVertical="5dp"
                android:text="评价课程"
                android:textColor="@color/black"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toLeftOf="@id/iv_close"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/iv_close"
                android:layout_width="17dp"
                android:layout_height="17dp"
                android:src="@drawable/common_close"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginEnd="12dp"
                android:scaleType="centerCrop"
                android:visibility="gone"
                tools:visibility="visible"
                android:background="@null"
                />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ExpandableListView
            android:id="@+id/list_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:childDivider="#ffffffff"
            android:divider="#ffffffff"
            android:groupIndicator="@null"
            android:scrollbars="none" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nl_comment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            tools:visibility="visible"
            tools:alpha="0.7"
            android:background="@color/white">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingHorizontal="12dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_desc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent"
                    android:text="如果您认为课程不错，可以给老师一点鼓励；有不足之处，老师也需要接受您的建议，帮助课程升级，非常感谢。"
                    android:paddingTop="16dp"
                    android:paddingBottom="24dp"
                    android:textSize="14sp"
                    android:lineSpacingExtra="6dp"
                    android:textColor="#333"/>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_select_current"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_desc"
                    android:background="@drawable/solid_max"
                    android:backgroundTint="#5081F7"
                    android:paddingVertical="6.5dp"
                    android:paddingHorizontal="15.5dp"
                    android:textSize="14sp"
                    android:textColor="@color/white"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:text="对本节的评价"
                    android:stateListAnimator="@null"/>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_select_all"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintLeft_toRightOf="@id/btn_select_current"
                    app:layout_constraintTop_toBottomOf="@id/tv_desc"
                    android:layout_marginStart="10dp"
                    android:background="@drawable/solid_max"
                    android:backgroundTint="#F6F7F8"
                    android:paddingVertical="6.5dp"
                    android:paddingHorizontal="15.5dp"
                    android:textSize="14sp"
                    android:textColor="#333"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:text="对课程整体评价"
                    android:stateListAnimator="@null"/>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_level_1"
                    android:layout_width="30dp"
                    android:layout_height="34dp"
                    android:background="@drawable/course_detail_comment_level_n"
                    android:textSize="14sp"
                    android:textColor="#666"
                    android:text="1"
                    android:gravity="center"
                    android:stateListAnimator="@null"
                    app:layout_constraintTop_toBottomOf="@id/btn_select_current"
                    android:layout_marginTop="16dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/btn_level_2"/>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_level_2"
                    android:layout_width="30dp"
                    android:layout_height="34dp"
                    android:background="@drawable/course_detail_comment_level_n"
                    android:textSize="14sp"
                    android:textColor="#666"
                    android:text="2"
                    android:gravity="center"
                    android:stateListAnimator="@null"
                    app:layout_constraintTop_toTopOf="@id/btn_level_1"
                    app:layout_constraintLeft_toRightOf="@id/btn_level_1"
                    app:layout_constraintRight_toLeftOf="@id/btn_level_3"/>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_level_3"
                    android:layout_width="30dp"
                    android:layout_height="34dp"
                    android:background="@drawable/course_detail_comment_level_n"
                    android:textSize="14sp"
                    android:textColor="#666"
                    android:text="3"
                    android:gravity="center"
                    android:stateListAnimator="@null"
                    app:layout_constraintTop_toTopOf="@id/btn_level_1"
                    app:layout_constraintLeft_toRightOf="@id/btn_level_2"
                    app:layout_constraintRight_toLeftOf="@id/btn_level_4"/>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_level_4"
                    android:layout_width="30dp"
                    android:layout_height="34dp"
                    android:background="@drawable/course_detail_comment_level_n"
                    android:textSize="14sp"
                    android:textColor="#666"
                    android:text="4"
                    android:gravity="center"
                    android:stateListAnimator="@null"
                    app:layout_constraintTop_toTopOf="@id/btn_level_1"
                    app:layout_constraintLeft_toRightOf="@id/btn_level_3"
                    app:layout_constraintRight_toLeftOf="@id/btn_level_5"/>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_level_5"
                    android:layout_width="30dp"
                    android:layout_height="34dp"
                    android:background="@drawable/course_detail_comment_level_n"
                    android:textSize="14sp"
                    android:textColor="#666"
                    android:text="5"
                    android:gravity="center"
                    android:stateListAnimator="@null"
                    app:layout_constraintTop_toTopOf="@id/btn_level_1"
                    app:layout_constraintLeft_toRightOf="@id/btn_level_4"
                    app:layout_constraintRight_toLeftOf="@id/btn_level_6"/>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_level_6"
                    android:layout_width="30dp"
                    android:layout_height="34dp"
                    android:background="@drawable/course_detail_comment_level_n"
                    android:textSize="14sp"
                    android:textColor="#666"
                    android:text="6"
                    android:gravity="center"
                    android:stateListAnimator="@null"
                    app:layout_constraintTop_toTopOf="@id/btn_level_1"
                    app:layout_constraintLeft_toRightOf="@id/btn_level_5"
                    app:layout_constraintRight_toLeftOf="@id/btn_level_7"/>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_level_7"
                    android:layout_width="30dp"
                    android:layout_height="34dp"
                    android:background="@drawable/course_detail_comment_level_n"
                    android:textSize="14sp"
                    android:textColor="#666"
                    android:text="7"
                    android:gravity="center"
                    android:stateListAnimator="@null"
                    app:layout_constraintTop_toTopOf="@id/btn_level_1"
                    app:layout_constraintLeft_toRightOf="@id/btn_level_6"
                    app:layout_constraintRight_toLeftOf="@id/btn_level_8"/>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_level_8"
                    android:layout_width="30dp"
                    android:layout_height="34dp"
                    android:background="@drawable/course_detail_comment_level_n"
                    android:textSize="14sp"
                    android:textColor="#666"
                    android:text="8"
                    android:gravity="center"
                    android:stateListAnimator="@null"
                    app:layout_constraintTop_toTopOf="@id/btn_level_1"
                    app:layout_constraintLeft_toRightOf="@id/btn_level_7"
                    app:layout_constraintRight_toLeftOf="@id/btn_level_9"/>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_level_9"
                    android:layout_width="30dp"
                    android:layout_height="34dp"
                    android:background="@drawable/course_detail_comment_level_n"
                    android:textSize="14sp"
                    android:textColor="#666"
                    android:text="9"
                    android:gravity="center"
                    android:stateListAnimator="@null"
                    app:layout_constraintTop_toTopOf="@id/btn_level_1"
                    app:layout_constraintLeft_toRightOf="@id/btn_level_8"
                    app:layout_constraintRight_toLeftOf="@id/btn_level_10"/>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_level_10"
                    android:layout_width="30dp"
                    android:layout_height="34dp"
                    android:background="@drawable/course_detail_comment_level_n"
                    android:textSize="14sp"
                    android:textColor="#666"
                    android:text="10"
                    android:gravity="center"
                    android:stateListAnimator="@null"
                    app:layout_constraintTop_toTopOf="@id/btn_level_1"
                    app:layout_constraintLeft_toRightOf="@id/btn_level_9"
                    app:layout_constraintRight_toRightOf="parent"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_level_left"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="非常不满意"
                    android:textColor="#ff999999"
                    android:textSize="13sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/btn_level_1"
                    android:padding="5dp"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_level_right"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="非常满意"
                    android:textColor="#ff12c39a"
                    android:textSize="13sp"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/btn_level_1"
                    android:padding="5dp"/>

                <LinearLayout
                    android:id="@+id/ln_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    app:layout_constraintTop_toBottomOf="@id/tv_level_left"
                    android:background="@drawable/course_detail_comment_input_bg"
                    android:orientation="vertical"
                    android:paddingBottom="7.5dp"
                    android:paddingRight="20dp">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="80dp">

                        <androidx.core.widget.NestedScrollView
                            android:id="@+id/nl_feesback_input"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <EditText
                                android:id="@+id/common_faceback_msg_ed"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:paddingLeft="19dp"
                                android:background="@null"
                                android:gravity="center_vertical|left"
                                android:hint="请输入您的评价内容"
                                android:minHeight="50dp"
                                android:textColor="#333333"
                                android:textColorHint="#CDCDCF"
                                android:textSize="14sp" />
                        </androidx.core.widget.NestedScrollView>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="22dp"
                            android:background="@drawable/course_detail_comment_input_shadow"/>

                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:gravity="right|center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_input_length"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="#476BFF"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="/200"
                            android:textColor="#CDCDCF"
                            android:textSize="14sp" />
                    </LinearLayout>


                </LinearLayout>

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/ln_image"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintTop_toBottomOf="@id/ln_input"
                    android:paddingTop="10dp">
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/gride_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                    <TextView
                        android:id="@+id/imageUploadTv"
                        android:layout_width="78dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5.5dp"
                        android:text="上传截图"
                        android:gravity="center"
                        android:textColor="#6C7596"
                        android:textSize="12sp" />
                </androidx.appcompat.widget.LinearLayoutCompat>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_submit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toBottomOf="@id/ln_image"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginTop="30dp"
                    android:layout_marginBottom="22dp"
                    android:layout_marginHorizontal="15dp"
                    android:paddingVertical="12dp"
                    android:text="提交"
                    android:textSize="16sp"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:background="@drawable/solid_radio_4"
                    android:backgroundTint="#5081F7"/>
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

    </FrameLayout>

</androidx.appcompat.widget.LinearLayoutCompat>