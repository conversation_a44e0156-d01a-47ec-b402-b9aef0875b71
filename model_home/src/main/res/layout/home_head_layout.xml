<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.zizhiguanjia.model_home.viewmodel.StudyViewModel" />

        <import type="android.view.View" />
    </data>

    <RelativeLayout
        android:id="@+id/relMain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <LinearLayout
            android:id="@+id/llHomeSwitch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="17dp"
            android:gravity="center_vertical"
            android:onClick="@{model::click}">

            <TextView
                android:id="@+id/titleTopic"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="北京 A证"
                android:textColor="#333333"
                android:textSize="18sp"
                android:textStyle="bold" />

            <ImageView
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_marginLeft="10dp"
                android:src="@drawable/down" />
        </LinearLayout>

        <!-- 添加用于引导提示的View -->
        <View
            android:id="@+id/vChapter2"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:visibility="invisible" />

        <RelativeLayout
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentRight="true"
            android:layout_marginRight="17dp"
            android:gravity="center">

            <androidx.cardview.widget.CardView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"

                app:cardCornerRadius="20dp"
                app:cardElevation="0dp">

                <ImageView
                    android:id="@+id/imgMainUser"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:onClick="@{model::click}"
                    android:scaleType="centerCrop"
                    android:src="@drawable/home_login_state_no"
                    tools:background="@color/black" />

            </androidx.cardview.widget.CardView>

            <ImageView
                android:id="@+id/userImageUtVip"
                android:layout_width="38dp"
                android:layout_height="16.5dp"
                android:layout_alignParentBottom="true"
                android:src="@drawable/icon_user_vip"
                android:visibility="@{model.homeBean.isVip?View.VISIBLE:View.GONE}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />
        </RelativeLayout>


    </RelativeLayout>
</layout>