<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_home.viewmodel.HomeFlutterViewModel" />

        <variable
            name="studyModel"
            type="com.zizhiguanjia.model_home.viewmodel.StudyViewModel" />

        <import type="android.view.View" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- Fragment容器 -->
        <FrameLayout
            android:id="@+id/fl_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <View
                android:id="@+id/vChapter"
                android:layout_width="100dp"
                android:layout_height="50dp"
                android:layout_gravity="start"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="37dp"
                android:layout_marginRight="12dp" />

            <!-- 原生学习页面布局 -->
            <!--            <LinearLayout-->
            <!--                android:id="@+id/ll_native_study_layout"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="match_parent"-->
            <!--                android:background="#F5F6FA"-->
            <!--                android:orientation="vertical"-->
            <!--                android:visibility="visible">-->

            <!-- 引入顶部标题栏 -->
<!--            <include-->
<!--                android:id="@+id/home_head"-->
<!--                layout="@layout/home_head_layout"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginTop="40dp" />-->

            <!-- 顶部信息区域 -->
            <!--            <LinearLayout-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:background="#F5F6FA"-->
            <!--                android:orientation="vertical">-->

            <!--                &lt;!&ndash; 通知条 &ndash;&gt;-->
            <!--                <LinearLayout-->
            <!--                    android:layout_width="match_parent"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:background="#EDF0FF"-->
            <!--                    android:paddingHorizontal="16dp"-->
            <!--                    android:paddingVertical="8dp">-->

            <!--                    <LinearLayout-->
            <!--                        android:layout_width="match_parent"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:gravity="center_vertical"-->
            <!--                        android:orientation="horizontal">-->

            <!--                        <ImageView-->
            <!--                            android:layout_width="20dp"-->
            <!--                            android:layout_height="20dp"-->
            <!--                            android:src="@drawable/ic_speaker"-->
            <!--                            android:tint="#3163F6" />-->

            <!--                        <TextView-->
            <!--                            android:layout_width="match_parent"-->
            <!--                            android:layout_height="wrap_content"-->
            <!--                            android:layout_marginStart="8dp"-->
            <!--                            android:text="北京题库为北京住建委公布的官方题库，请放心学习"-->
            <!--                            android:textColor="#333333"-->
            <!--                            android:textSize="12sp" />-->
            <!--                    </LinearLayout>-->
            <!--                </LinearLayout>-->

            <!--                <ScrollView-->
            <!--                    android:layout_width="match_parent"-->
            <!--                    android:layout_height="wrap_content">-->

            <!--                    <LinearLayout-->
            <!--                        android:layout_width="match_parent"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:orientation="vertical">-->
            <!--                        &lt;!&ndash; AI学习助手区域 &ndash;&gt;-->
            <!--                        <RelativeLayout-->
            <!--                            android:layout_width="match_parent"-->
            <!--                            android:layout_height="270dp"-->
            <!--                            android:layout_marginStart="10dp"-->
            <!--                            android:layout_marginTop="10dp"-->
            <!--                            android:layout_marginEnd="16dp"-->
            <!--                            android:background="@drawable/ai_bg"-->
            <!--                            android:paddingHorizontal="10dp"-->
            <!--                            android:paddingTop="16dp"-->
            <!--                            android:paddingBottom="20dp">-->

            <!--                            <TextView-->
            <!--                                android:id="@+id/tv_ai_helper_title"-->
            <!--                                android:layout_width="wrap_content"-->
            <!--                                android:layout_height="wrap_content"-->
            <!--                                android:layout_marginStart="0dp"-->
            <!--                                android:layout_marginTop="20dp"-->
            <!--                                android:text="AI学习助手"-->
            <!--                                android:textColor="#333333"-->
            <!--                                android:textSize="20sp"-->
            <!--                                android:textStyle="bold" />-->


            <!--                            &lt;!&ndash; 学习进度卡片 - 内嵌到AI学习助手区域 &ndash;&gt;-->
            <!--                            <LinearLayout-->
            <!--                                android:id="@+id/ll_study_time"-->
            <!--                                android:layout_width="wrap_content"-->
            <!--                                android:layout_height="wrap_content"-->
            <!--                                android:layout_below="@id/tv_ai_helper_title"-->
            <!--                                android:layout_alignTop="@id/tv_ai_helper_title"-->
            <!--                                android:layout_marginStart="40dp"-->
            <!--                                android:layout_toEndOf="@+id/tv_ai_helper_title"-->
            <!--                                android:background="@drawable/rounded_white_bg"-->
            <!--                                android:gravity="center_vertical"-->
            <!--                                android:orientation="horizontal"-->
            <!--                                android:paddingStart="10dp"-->
            <!--                                android:paddingTop="3dp"-->
            <!--                                android:paddingEnd="10dp"-->
            <!--                                android:paddingBottom="3dp">-->

            <!--                                <TextView-->
            <!--                                    android:layout_width="wrap_content"-->
            <!--                                    android:layout_height="wrap_content"-->
            <!--                                    android:text="@{`学习时长:` + String.valueOf(studyModel.studyTimeMinutes) + `分钟`}"-->
            <!--                                    android:textColor="#3163F6"-->
            <!--                                    android:textSize="12sp" />-->
            <!--                            </LinearLayout>-->

            <!--                            <LinearLayout-->
            <!--                                android:id="@+id/ll_study_progress"-->
            <!--                                android:layout_width="match_parent"-->
            <!--                                android:layout_height="wrap_content"-->
            <!--                                android:layout_below="@id/ll_study_time"-->
            <!--                                android:layout_marginTop="15dp"-->
            <!--                                android:orientation="horizontal">-->

            <!--                                &lt;!&ndash; 进度环 &ndash;&gt;-->
            <!--                                <RelativeLayout-->
            <!--                                    android:layout_width="0dp"-->
            <!--                                    android:layout_height="80dp"-->
            <!--                                    android:layout_weight="1.1"-->
            <!--                                    android:gravity="center"-->
            <!--                                    android:orientation="vertical">-->

            <!--                                    <com.zizhiguanjia.model_home.view.CustomProgressView-->
            <!--                                        android:id="@+id/progress_circular"-->
            <!--                                        android:layout_width="wrap_content"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        app:layout_constraintEnd_toEndOf="parent"-->
            <!--                                        app:layout_constraintStart_toStartOf="parent"-->
            <!--                                        app:layout_constraintTop_toTopOf="parent"-->
            <!--                                        tools:ignore="MissingClass" />-->

            <!--                                    <TextView-->
            <!--                                        android:layout_width="wrap_content"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:layout_alignParentBottom="true"-->
            <!--                                        android:layout_centerInParent="true"-->
            <!--                                        android:layout_marginBottom="10dp"-->
            <!--                                        android:text="60/100"-->
            <!--                                        android:textColor="#3163F6"-->
            <!--                                        android:textSize="12sp"-->
            <!--                                        android:textStyle="bold" />-->

            <!--                                </RelativeLayout>-->

            <!--                                &lt;!&ndash; 模考分数 &ndash;&gt;-->
            <!--                                <LinearLayout-->
            <!--                                    android:layout_width="0dp"-->
            <!--                                    android:layout_height="match_parent"-->
            <!--                                    android:layout_weight="1"-->
            <!--                                    android:gravity="center"-->
            <!--                                    android:orientation="vertical">-->

            <!--                                    <TextView-->
            <!--                                        android:layout_width="wrap_content"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:text="@{String.valueOf(studyModel.latestExamScore)}"-->
            <!--                                        android:textColor="#333333"-->
            <!--                                        android:textSize="24sp"-->
            <!--                                        android:textStyle="bold" />-->

            <!--                                    <TextView-->
            <!--                                        android:layout_width="wrap_content"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:text="最新模考"-->
            <!--                                        android:textColor="#666666"-->
            <!--                                        android:textSize="12sp" />-->
            <!--                                </LinearLayout>-->

            <!--                                &lt;!&ndash; 知识掌握度 &ndash;&gt;-->
            <!--                                <LinearLayout-->
            <!--                                    android:layout_width="0dp"-->
            <!--                                    android:layout_height="match_parent"-->
            <!--                                    android:layout_weight="1"-->
            <!--                                    android:gravity="center"-->
            <!--                                    android:orientation="vertical">-->

            <!--                                    <LinearLayout-->
            <!--                                        android:layout_width="wrap_content"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:gravity="center"-->
            <!--                                        android:orientation="horizontal">-->

            <!--                                        <TextView-->
            <!--                                            android:layout_width="wrap_content"-->
            <!--                                            android:layout_height="wrap_content"-->
            <!--                                            android:text="@{String.valueOf(studyModel.knowledgeMastery)}"-->
            <!--                                            android:textColor="#333333"-->
            <!--                                            android:textSize="24sp"-->
            <!--                                            android:textStyle="bold" />-->

            <!--                                        <TextView-->
            <!--                                            android:layout_width="wrap_content"-->
            <!--                                            android:layout_height="wrap_content"-->
            <!--                                            android:text="%"-->
            <!--                                            android:textColor="#333333"-->
            <!--                                            android:textSize="16sp"-->
            <!--                                            android:textStyle="bold" />-->
            <!--                                    </LinearLayout>-->

            <!--                                    <TextView-->
            <!--                                        android:layout_width="wrap_content"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:text="知识掌握度"-->
            <!--                                        android:textColor="#666666"-->
            <!--                                        android:textSize="12sp" />-->
            <!--                                </LinearLayout>-->

            <!--                                &lt;!&ndash; 坚持学习 &ndash;&gt;-->
            <!--                                <LinearLayout-->
            <!--                                    android:layout_width="0dp"-->
            <!--                                    android:layout_height="match_parent"-->
            <!--                                    android:layout_weight="1"-->
            <!--                                    android:gravity="center"-->
            <!--                                    android:orientation="vertical">-->

            <!--                                    <LinearLayout-->
            <!--                                        android:layout_width="wrap_content"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:gravity="center"-->
            <!--                                        android:orientation="horizontal">-->

            <!--                                        <TextView-->
            <!--                                            android:layout_width="wrap_content"-->
            <!--                                            android:layout_height="wrap_content"-->
            <!--                                            android:text="@{String.valueOf(studyModel.studyDays) + `天`}"-->
            <!--                                            android:textColor="#333333"-->
            <!--                                            android:textSize="24sp"-->
            <!--                                            android:textStyle="bold" />-->

            <!--                                        &lt;!&ndash; 移除重复的"天"字 &ndash;&gt;-->
            <!--                                    </LinearLayout>-->

            <!--                                    <TextView-->
            <!--                                        android:layout_width="wrap_content"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:text="坚持学习"-->
            <!--                                        android:textColor="#666666"-->
            <!--                                        android:textSize="12sp" />-->

            <!--                                </LinearLayout>-->
            <!--                            </LinearLayout>-->

            <!--                            &lt;!&ndash; 摸底测评卡片 - 内嵌到AI学习助手区域 &ndash;&gt;-->
            <!--                            <RelativeLayout-->
            <!--                                android:layout_width="match_parent"-->
            <!--                                android:layout_height="wrap_content"-->
            <!--                                android:layout_below="@id/ll_study_progress"-->
            <!--                                android:layout_marginTop="3dp"-->
            <!--                                android:background="@drawable/rounded_light_blue_bg"-->
            <!--                                android:padding="10dp">-->

            <!--                                <ImageView-->
            <!--                                    android:id="@+id/iv_close_test"-->
            <!--                                    android:layout_width="24dp"-->
            <!--                                    android:layout_height="24dp"-->
            <!--                                    android:layout_alignParentTop="true"-->
            <!--                                    android:layout_alignParentEnd="true"-->
            <!--                                    android:src="@drawable/ic_close"-->
            <!--                                    android:tint="#999999" />-->

            <!--                                <LinearLayout-->
            <!--                                    android:id="@+id/ll_test_content"-->
            <!--                                    android:layout_width="wrap_content"-->
            <!--                                    android:layout_height="wrap_content"-->
            <!--                                    android:orientation="vertical">-->

            <!--                                    <TextView-->
            <!--                                        android:layout_width="wrap_content"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:text="摸底测评"-->
            <!--                                        android:textColor="#333333"-->
            <!--                                        android:textSize="16sp"-->
            <!--                                        android:textStyle="bold" />-->

            <!--                                    <TextView-->
            <!--                                        android:layout_width="wrap_content"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:layout_marginTop="8dp"-->
            <!--                                        android:text="参与摸底测评 找到知识薄弱点AI\n定制学习计划 更高效更省力"-->
            <!--                                        android:textColor="#666666"-->
            <!--                                        android:textSize="12sp" />-->
            <!--                                </LinearLayout>-->

            <!--                                <Button-->
            <!--                                    android:id="@+id/btn_start_test"-->
            <!--                                    android:layout_width="wrap_content"-->
            <!--                                    android:layout_height="29dp"-->
            <!--                                    android:layout_below="@id/ll_test_content"-->
            <!--                                    android:layout_alignTop="@id/ll_test_content"-->
            <!--                                    android:layout_alignParentEnd="true"-->
            <!--                                    android:layout_marginStart="25dp"-->
            <!--                                    android:layout_marginTop="30dp"-->
            <!--                                    android:layout_marginEnd="10dp"-->
            <!--                                    android:layout_toEndOf="@+id/ll_test_content"-->
            <!--                                    android:background="@drawable/rounded_blue_button_bg"-->
            <!--                                    android:text="立即测评"-->
            <!--                                    android:textColor="#FFFFFF"-->
            <!--                                    android:textSize="14sp" />-->
            <!--                            </RelativeLayout>-->
            <!--                        </RelativeLayout>-->

            <!--                        &lt;!&ndash; 功能按钮区域 &ndash;&gt;-->
            <!--                        <LinearLayout-->
            <!--                            android:layout_width="match_parent"-->
            <!--                            android:layout_height="wrap_content"-->
            <!--                            android:layout_marginTop="16dp"-->
            <!--                            android:orientation="horizontal">-->

            <!--                            &lt;!&ndash; 高频考题 &ndash;&gt;-->
            <!--                            <androidx.cardview.widget.CardView-->
            <!--                                android:id="@+id/card_high_freq"-->
            <!--                                android:layout_width="0dp"-->
            <!--                                android:layout_height="wrap_content"-->
            <!--                                android:layout_marginStart="16dp"-->
            <!--                                android:layout_marginEnd="0dp"-->
            <!--                                android:layout_weight="1"-->
            <!--                                app:cardCornerRadius="8dp"-->
            <!--                                app:cardElevation="2dp">-->

            <!--                                <LinearLayout-->
            <!--                                    android:layout_width="match_parent"-->
            <!--                                    android:layout_height="wrap_content"-->
            <!--                                    android:background="#6A91FF"-->
            <!--                                    android:orientation="vertical"-->
            <!--                                    android:padding="14dp">-->

            <!--                                    <LinearLayout-->
            <!--                                        android:layout_width="match_parent"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:gravity="center_vertical"-->
            <!--                                        android:orientation="horizontal">-->

            <!--                                        <TextView-->
            <!--                                            android:layout_width="wrap_content"-->
            <!--                                            android:layout_height="wrap_content"-->
            <!--                                            android:background="@drawable/rounded_light_blue_bg"-->
            <!--                                            android:padding="4dp"-->
            <!--                                            android:text="100"-->
            <!--                                            android:textColor="#3163F6"-->
            <!--                                            android:textSize="18sp"-->
            <!--                                            android:textStyle="bold" />-->

            <!--                                        <LinearLayout-->
            <!--                                            android:layout_width="match_parent"-->
            <!--                                            android:layout_height="wrap_content"-->
            <!--                                            android:layout_marginLeft="10dp"-->
            <!--                                            android:orientation="vertical">-->

            <!--                                            <TextView-->
            <!--                                                android:layout_width="wrap_content"-->
            <!--                                                android:layout_height="wrap_content"-->
            <!--                                                android:text="高频考题"-->
            <!--                                                android:textColor="@color/white"-->
            <!--                                                android:textSize="14sp"-->
            <!--                                                android:textStyle="bold" />-->

            <!--                                            <TextView-->
            <!--                                                android:layout_width="match_parent"-->
            <!--                                                android:layout_height="wrap_content"-->
            <!--                                                android:layout_marginTop="8dp"-->
            <!--                                                android:text="事半功倍快速提分"-->
            <!--                                                android:textColor="@color/white"-->
            <!--                                                android:textSize="12sp" />-->
            <!--                                        </LinearLayout>-->

            <!--                                    </LinearLayout>-->


            <!--                                </LinearLayout>-->
            <!--                            </androidx.cardview.widget.CardView>-->

            <!--                            &lt;!&ndash; 智能模考 &ndash;&gt;-->
            <!--                            <androidx.cardview.widget.CardView-->
            <!--                                android:id="@+id/card_smart_exam"-->
            <!--                                android:layout_width="0dp"-->
            <!--                                android:layout_height="wrap_content"-->
            <!--                                android:layout_marginStart="16dp"-->
            <!--                                android:layout_marginEnd="16dp"-->
            <!--                                android:layout_weight="1"-->
            <!--                                app:cardCornerRadius="8dp"-->
            <!--                                app:cardElevation="2dp">-->

            <!--                                <LinearLayout-->
            <!--                                    android:layout_width="match_parent"-->
            <!--                                    android:layout_height="wrap_content"-->
            <!--                                    android:background="#6A91FF"-->
            <!--                                    android:orientation="vertical"-->
            <!--                                    android:padding="14dp">-->

            <!--                                    <LinearLayout-->
            <!--                                        android:layout_width="match_parent"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:gravity="center_vertical"-->
            <!--                                        android:orientation="horizontal">-->

            <!--                                        <TextView-->
            <!--                                            android:layout_width="wrap_content"-->
            <!--                                            android:layout_height="wrap_content"-->
            <!--                                            android:background="@drawable/rounded_light_blue_bg"-->
            <!--                                            android:padding="4dp"-->
            <!--                                            android:text="100"-->
            <!--                                            android:textColor="#3163F6"-->
            <!--                                            android:textSize="18sp"-->
            <!--                                            android:textStyle="bold" />-->

            <!--                                        <LinearLayout-->
            <!--                                            android:layout_width="match_parent"-->
            <!--                                            android:layout_height="wrap_content"-->
            <!--                                            android:layout_marginLeft="10dp"-->
            <!--                                            android:orientation="vertical">-->

            <!--                                            <TextView-->
            <!--                                                android:layout_width="wrap_content"-->
            <!--                                                android:layout_height="wrap_content"-->
            <!--                                                android:text="智能模考"-->
            <!--                                                android:textColor="@color/white"-->
            <!--                                                android:textSize="14sp"-->
            <!--                                                android:textStyle="bold" />-->

            <!--                                            <TextView-->
            <!--                                                android:layout_width="match_parent"-->
            <!--                                                android:layout_height="wrap_content"-->
            <!--                                                android:layout_marginTop="8dp"-->
            <!--                                                android:text="按重难点智能组卷"-->
            <!--                                                android:textColor="@color/white"-->
            <!--                                                android:textSize="12sp" />-->
            <!--                                        </LinearLayout>-->

            <!--                                    </LinearLayout>-->


            <!--                                </LinearLayout>-->
            <!--                            </androidx.cardview.widget.CardView>-->
            <!--                        </LinearLayout>-->

            <!--                        &lt;!&ndash; 学习功能按钮 &ndash;&gt;-->
            <!--                        <LinearLayout-->
            <!--                            android:layout_width="match_parent"-->
            <!--                            android:layout_height="wrap_content"-->
            <!--                            android:layout_marginStart="16dp"-->
            <!--                            android:layout_marginTop="16dp"-->
            <!--                            android:layout_marginEnd="16dp"-->
            <!--                            android:orientation="horizontal">-->

            <!--                            &lt;!&ndash; 知识点练习 &ndash;&gt;-->
            <!--                            <LinearLayout-->
            <!--                                android:id="@+id/layout_knowledge"-->
            <!--                                android:layout_width="0dp"-->
            <!--                                android:layout_height="match_parent"-->
            <!--                                android:layout_marginRight="3dp"-->
            <!--                                android:layout_weight="1"-->
            <!--                                android:background="@drawable/shape_white"-->
            <!--                                android:gravity="center"-->
            <!--                                android:orientation="vertical"-->
            <!--                                android:padding="10dp">-->

            <!--                                <ImageView-->
            <!--                                    android:layout_width="25dp"-->
            <!--                                    android:layout_height="25dp"-->
            <!--                                    android:background="@drawable/circle_yellow_bg"-->
            <!--                                    android:padding="12dp"-->
            <!--                                    android:src="@drawable/ic_knowledge" />-->

            <!--                                <TextView-->
            <!--                                    android:layout_width="wrap_content"-->
            <!--                                    android:layout_height="wrap_content"-->
            <!--                                    android:layout_marginTop="4dp"-->
            <!--                                    android:text="知识点练习"-->
            <!--                                    android:textColor="#333333"-->
            <!--                                    android:textSize="12sp" />-->
            <!--                            </LinearLayout>-->

            <!--                            &lt;!&ndash; 题型练习 &ndash;&gt;-->
            <!--                            <LinearLayout-->
            <!--                                android:id="@+id/layout_question_type"-->
            <!--                                android:layout_width="0dp"-->
            <!--                                android:layout_height="match_parent"-->
            <!--                                android:layout_margin="3dp"-->
            <!--                                android:layout_weight="1"-->
            <!--                                android:background="@drawable/shape_white"-->
            <!--                                android:gravity="center"-->
            <!--                                android:orientation="vertical"-->
            <!--                                android:padding="5dp">-->

            <!--                                <ImageView-->
            <!--                                    android:layout_width="22dp"-->
            <!--                                    android:layout_height="22dp"-->
            <!--                                    android:background="@drawable/circle_yellow_bg"-->
            <!--                                    android:padding="12dp"-->
            <!--                                    android:src="@drawable/ic_knowledge" />-->

            <!--                                <TextView-->
            <!--                                    android:layout_width="wrap_content"-->
            <!--                                    android:layout_height="wrap_content"-->
            <!--                                    android:layout_marginTop="4dp"-->
            <!--                                    android:text="题型练习"-->
            <!--                                    android:textColor="#333333"-->
            <!--                                    android:textSize="12sp" />-->
            <!--                            </LinearLayout>-->
            <!--                            &lt;!&ndash; 易错100题 &ndash;&gt;-->
            <!--                            <LinearLayout-->
            <!--                                android:id="@+id/layout_error_prone"-->
            <!--                                android:layout_width="0dp"-->
            <!--                                android:layout_height="match_parent"-->
            <!--                                android:layout_margin="3dp"-->
            <!--                                android:layout_weight="1"-->
            <!--                                android:background="@drawable/shape_white"-->
            <!--                                android:gravity="center"-->
            <!--                                android:orientation="vertical">-->

            <!--                                <ImageView-->
            <!--                                    android:layout_width="22dp"-->
            <!--                                    android:layout_height="22dp"-->
            <!--                                    android:background="@drawable/circle_yellow_bg"-->
            <!--                                    android:padding="12dp"-->
            <!--                                    android:src="@drawable/ic_knowledge" />-->

            <!--                                <TextView-->
            <!--                                    android:layout_width="wrap_content"-->
            <!--                                    android:layout_height="wrap_content"-->
            <!--                                    android:layout_marginTop="4dp"-->
            <!--                                    android:text="易错100题"-->
            <!--                                    android:textColor="#333333"-->
            <!--                                    android:textSize="12sp" />-->
            <!--                            </LinearLayout>-->

            <!--                            &lt;!&ndash; 通关精讲课 &ndash;&gt;-->
            <!--                            <LinearLayout-->
            <!--                                android:id="@+id/layout_lecture"-->
            <!--                                android:layout_width="0dp"-->
            <!--                                android:layout_height="match_parent"-->
            <!--                                android:layout_marginLeft="3dp"-->
            <!--                                android:layout_weight="1"-->
            <!--                                android:background="@drawable/shape_white"-->
            <!--                                android:gravity="center"-->
            <!--                                android:orientation="vertical">-->

            <!--                                <ImageView-->
            <!--                                    android:layout_width="22dp"-->
            <!--                                    android:layout_height="22dp"-->
            <!--                                    android:background="@drawable/circle_yellow_bg"-->
            <!--                                    android:padding="12dp"-->
            <!--                                    android:src="@drawable/ic_knowledge" />-->

            <!--                                <TextView-->
            <!--                                    android:layout_width="wrap_content"-->
            <!--                                    android:layout_height="wrap_content"-->
            <!--                                    android:layout_marginTop="4dp"-->
            <!--                                    android:text="通关精讲课"-->
            <!--                                    android:textColor="#333333"-->
            <!--                                    android:textSize="12sp" />-->
            <!--                            </LinearLayout>-->
            <!--                        </LinearLayout>-->

            <!--                        &lt;!&ndash; VIP权益区域 &ndash;&gt;-->
            <!--                        <androidx.cardview.widget.CardView-->
            <!--                            android:layout_width="match_parent"-->
            <!--                            android:layout_height="wrap_content"-->
            <!--                            android:layout_marginStart="16dp"-->
            <!--                            android:layout_marginTop="16dp"-->
            <!--                            android:layout_marginEnd="16dp"-->
            <!--                            app:cardBackgroundColor="#4A4A6A"-->
            <!--                            app:cardCornerRadius="8dp"-->
            <!--                            app:cardElevation="2dp">-->

            <!--                            <LinearLayout-->
            <!--                                android:layout_width="match_parent"-->
            <!--                                android:layout_height="wrap_content"-->
            <!--                                android:orientation="vertical"-->
            <!--                                android:padding="10dp">-->

            <!--                                <LinearLayout-->
            <!--                                    android:layout_width="match_parent"-->
            <!--                                    android:layout_height="wrap_content"-->
            <!--                                    android:gravity="center_vertical"-->
            <!--                                    android:orientation="horizontal">-->

            <!--                                    <ImageView-->
            <!--                                        android:layout_width="24dp"-->
            <!--                                        android:layout_height="24dp"-->
            <!--                                        android:src="@drawable/ic_crown"-->
            <!--                                        android:tint="#FFD700" />-->

            <!--                                    <TextView-->
            <!--                                        android:layout_width="0dp"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:layout_marginStart="8dp"-->
            <!--                                        android:layout_weight="1"-->
            <!--                                        android:text="VIP权益"-->
            <!--                                        android:textColor="@android:color/white"-->
            <!--                                        android:textSize="16sp"-->
            <!--                                        android:textStyle="bold" />-->

            <!--                                    <Button-->
            <!--                                        android:id="@+id/btn_become_vip"-->
            <!--                                        android:layout_width="wrap_content"-->
            <!--                                        android:layout_height="32dp"-->
            <!--                                        android:background="@drawable/rounded_gold_gradient_button_bg"-->
            <!--                                        android:text="成为VIP"-->
            <!--                                        android:textColor="#6F4E18"-->
            <!--                                        android:textSize="14sp"-->
            <!--                                        android:textStyle="bold" />-->
            <!--                                </LinearLayout>-->

            <!--                                <TextView-->
            <!--                                    android:layout_width="match_parent"-->
            <!--                                    android:layout_height="wrap_content"-->
            <!--                                    android:layout_marginTop="12dp"-->
            <!--                                    android:text="@{`试用权益：您可试用 ` + String.valueOf(studyModel.trialQuestions) + ` 道题 ` + String.valueOf(studyModel.trialPapers) + ` 套试卷`}"-->
            <!--                                    android:textColor="#E0E0E0"-->
            <!--                                    android:textSize="14sp" />-->

            <!--                                &lt;!&ndash; 进度条区域 &ndash;&gt;-->
            <!--                                <RelativeLayout-->
            <!--                                    android:layout_width="match_parent"-->
            <!--                                    android:layout_height="wrap_content"-->
            <!--                                    android:layout_marginTop="8dp">-->

            <!--                                    <ProgressBar-->
            <!--                                        android:id="@+id/progress_vip_usage"-->
            <!--                                        style="@style/CustomProgressBar"-->
            <!--                                        android:layout_width="match_parent"-->
            <!--                                        android:layout_height="30dp"-->
            <!--                                        android:layout_centerVertical="true"-->
            <!--                                        android:max="100"-->
            <!--                                        android:progress="@{studyModel.vipUsagePercent}"-->
            <!--                                        android:progressDrawable="@drawable/vip_progress_bar" />-->

            <!--                                    <TextView-->
            <!--                                        android:layout_marginLeft="10dp"-->
            <!--                                        android:layout_centerVertical="true"-->
            <!--                                        android:layout_width="wrap_content"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:layout_marginTop="4dp"-->
            <!--                                        android:text="@{`您已试用 ` + String.valueOf(studyModel.usedTrialQuestions) + ` 道题 ` + String.valueOf(studyModel.usedTrialPapers) + ` 套试卷`}"-->
            <!--                                        android:textColor="#E0E0E0"-->
            <!--                                        android:textSize="12sp" />-->
            <!--                                </RelativeLayout>-->

            <!--                                <LinearLayout-->
            <!--                                    android:layout_width="match_parent"-->
            <!--                                    android:layout_height="wrap_content"-->
            <!--                                    android:layout_marginTop="12dp"-->
            <!--                                    android:layout_marginBottom="4dp"-->
            <!--                                    android:orientation="horizontal">-->

            <!--                                    <TextView-->
            <!--                                        android:layout_width="0dp"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:layout_weight="1.1"-->
            <!--                                        android:gravity="center"-->
            <!--                                        android:text="全部题库都能刷"-->
            <!--                                        android:textColor="@android:color/white"-->
            <!--                                        android:textSize="12sp" />-->

            <!--                                    <TextView-->
            <!--                                        android:layout_width="0dp"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:layout_weight="1"-->
            <!--                                        android:gravity="center"-->
            <!--                                        android:text="AI学习助手"-->
            <!--                                        android:textColor="@android:color/white"-->
            <!--                                        android:textSize="12sp" />-->

            <!--                                    <TextView-->
            <!--                                        android:layout_width="0dp"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:layout_weight="1"-->
            <!--                                        android:gravity="center"-->
            <!--                                        android:text="高频考题"-->
            <!--                                        android:textColor="@android:color/white"-->
            <!--                                        android:textSize="12sp" />-->

            <!--                                    <TextView-->
            <!--                                        android:layout_width="0dp"-->
            <!--                                        android:layout_height="wrap_content"-->
            <!--                                        android:layout_weight="1"-->
            <!--                                        android:gravity="center"-->
            <!--                                        android:text="智能模考"-->
            <!--                                        android:textColor="@android:color/white"-->
            <!--                                        android:textSize="12sp" />-->
            <!--                                </LinearLayout>-->
            <!--                            </LinearLayout>-->
            <!--                        </androidx.cardview.widget.CardView>-->

            <!--                        &lt;!&ndash; 底部信息 &ndash;&gt;-->
            <!--                        <TextView-->
            <!--                            android:layout_width="match_parent"-->
            <!--                            android:layout_height="wrap_content"-->
            <!--                            android:layout_marginTop="24dp"-->
            <!--                            android:gravity="center"-->
            <!--                            android:text="考安管人员ABC证，用安全员考试宝典"-->
            <!--                            android:textColor="#333333"-->
            <!--                            android:textSize="14sp"-->
            <!--                            android:textStyle="bold" />-->

            <!--                        <TextView-->
            <!--                            android:layout_width="match_parent"-->
            <!--                            android:layout_height="wrap_content"-->
            <!--                            android:layout_marginTop="8dp"-->
            <!--                            android:gravity="center"-->
            <!--                            android:text="其他功能"-->
            <!--                            android:textColor="#999999"-->
            <!--                            android:textSize="12sp" />-->

            <!--                        &lt;!&ndash; 底部功能按钮 &ndash;&gt;-->
            <!--                        <LinearLayout-->
            <!--                            android:layout_width="match_parent"-->
            <!--                            android:layout_height="wrap_content"-->
            <!--                            android:layout_marginStart="16dp"-->
            <!--                            android:layout_marginTop="16dp"-->
            <!--                            android:layout_marginEnd="16dp"-->
            <!--                            android:layout_marginBottom="16dp"-->
            <!--                            android:orientation="horizontal">-->

            <!--                            &lt;!&ndash; 微信好友 &ndash;&gt;-->
            <!--                            <LinearLayout-->
            <!--                                android:id="@+id/layout_wechat"-->
            <!--                                android:layout_width="0dp"-->
            <!--                                android:layout_height="wrap_content"-->
            <!--                                android:layout_weight="1"-->
            <!--                                android:gravity="center"-->
            <!--                                android:orientation="vertical">-->

            <!--                                <ImageView-->
            <!--                                    android:layout_width="35dp"-->
            <!--                                    android:layout_height="35dp"-->
            <!--                                    android:src="@drawable/wx" />-->

            <!--                                <TextView-->
            <!--                                    android:layout_width="wrap_content"-->
            <!--                                    android:layout_height="wrap_content"-->
            <!--                                    android:layout_marginTop="4dp"-->
            <!--                                    android:text="微信好友"-->
            <!--                                    android:textColor="#333333"-->
            <!--                                    android:textSize="12sp" />-->
            <!--                            </LinearLayout>-->

            <!--                            &lt;!&ndash; 朋友圈 &ndash;&gt;-->
            <!--                            <LinearLayout-->
            <!--                                android:id="@+id/layout_moments"-->
            <!--                                android:layout_width="0dp"-->
            <!--                                android:layout_height="wrap_content"-->
            <!--                                android:layout_weight="1"-->
            <!--                                android:gravity="center"-->
            <!--                                android:orientation="vertical">-->

            <!--                                <ImageView-->
            <!--                                    android:layout_width="35dp"-->
            <!--                                    android:layout_height="35dp"-->
            <!--                                    android:src="@drawable/pyq" />-->

            <!--                                <TextView-->
            <!--                                    android:layout_width="wrap_content"-->
            <!--                                    android:layout_height="wrap_content"-->
            <!--                                    android:layout_marginTop="4dp"-->
            <!--                                    android:text="朋友圈"-->
            <!--                                    android:textColor="#333333"-->
            <!--                                    android:textSize="12sp" />-->
            <!--                            </LinearLayout>-->

            <!--                            &lt;!&ndash; 拨打电话 &ndash;&gt;-->
            <!--                            <LinearLayout-->
            <!--                                android:id="@+id/layout_phone"-->
            <!--                                android:layout_width="0dp"-->
            <!--                                android:layout_height="wrap_content"-->
            <!--                                android:layout_weight="1"-->
            <!--                                android:gravity="center"-->
            <!--                                android:orientation="vertical">-->

            <!--                                <ImageView-->
            <!--                                    android:layout_width="35dp"-->
            <!--                                    android:layout_height="35dp"-->
            <!--                                    android:src="@drawable/phone" />-->

            <!--                                <TextView-->
            <!--                                    android:layout_width="wrap_content"-->
            <!--                                    android:layout_height="wrap_content"-->
            <!--                                    android:layout_marginTop="4dp"-->
            <!--                                    android:text="拨打电话"-->
            <!--                                    android:textColor="#333333"-->
            <!--                                    android:textSize="12sp" />-->
            <!--                            </LinearLayout>-->

            <!--                            &lt;!&ndash; 在线客服 &ndash;&gt;-->
            <!--                            <LinearLayout-->
            <!--                                android:id="@+id/layout_service"-->
            <!--                                android:layout_width="0dp"-->
            <!--                                android:layout_height="wrap_content"-->
            <!--                                android:layout_weight="1"-->
            <!--                                android:gravity="center"-->
            <!--                                android:orientation="vertical">-->

            <!--                                <ImageView-->
            <!--                                    android:layout_width="35dp"-->
            <!--                                    android:layout_height="35dp"-->
            <!--                                    android:src="@drawable/call" />-->

            <!--                                <TextView-->
            <!--                                    android:layout_width="wrap_content"-->
            <!--                                    android:layout_height="wrap_content"-->
            <!--                                    android:layout_marginTop="4dp"-->
            <!--                                    android:text="在线客服"-->
            <!--                                    android:textColor="#333333"-->
            <!--                                    android:textSize="12sp" />-->
            <!--                            </LinearLayout>-->
            <!--                        </LinearLayout>-->
            <!--                    </LinearLayout>-->
            <!--                </ScrollView>-->
            <!--            </LinearLayout>-->

            <include
                android:id="@+id/homeMainFf"
                layout="@layout/home_frame_layout" />
        </FrameLayout>
    </LinearLayout>
</layout>