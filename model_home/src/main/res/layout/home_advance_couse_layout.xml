<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <ImageView
        android:visibility="gone"
        android:id="@+id/image1"
        android:scaleType="center"
        android:src="@drawable/home_avd_course"
        android:layout_width="match_parent"
        android:layout_height="258dp"/>
    <ImageView
        android:id="@+id/image2"
        android:scaleType="center"
        android:src="@drawable/home_avd_course"
        android:layout_width="match_parent"
        android:layout_height="75dp"/>
   <LinearLayout
       android:layout_marginTop="25dp"
       android:orientation="vertical"
       android:layout_width="match_parent"
       android:layout_height="match_parent">
       <com.kevin.slidingtab.SlidingTabLayout
           android:id="@+id/slidTl"
           xmlns:app="http://schemas.android.com/apk/res-auto"
           app:tl_indicator_is_top="true"
           app:stl_tabMode="fixed"
           app:stl_leftPadding="122dp"
           app:stl_rightPadding="122dp"
           app:stl_tabIndicatorColor="#007AFF"
           app:stl_tabIndicatorHeight="3dp"
           app:stl_tabIndicatorWidth="19dp"
           app:stl_tabPaddingEnd="8dp"
           app:stl_tabPaddingStart="8dp"
           app:stl_tabSelectedTextColor="#000000"
           app:stl_tabSelectedTextSize="18sp"
           app:stl_tabTextBold="false"
           app:stl_tabTextSelectedBold="true"
           app:stl_tabTextColor="#000000"
           app:stl_tabIndicatorCornerRadius="1.5dp"
           app:stl_tabIndicatorMarginBottom="8dp"
           app:stl_tabTextShowScaleAnim="true"
           app:stl_tabIndicatorCreep="true"
           app:stl_tabTextSize="18sp"
           android:layout_width="match_parent"
           android:layout_height="50dp" />
       <androidx.viewpager.widget.ViewPager
           android:id="@+id/advVp"
           android:layout_width="match_parent"
           android:layout_height="match_parent" />
   </LinearLayout>
</RelativeLayout>