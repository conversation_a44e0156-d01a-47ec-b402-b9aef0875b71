package com.zizhiguanjia.model_home.view;

import android.app.Activity;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.style.RelativeSizeSpan;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.helper.AccountHelper;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.bean.HomeBean;
import com.zizhiguanjia.model_home.databinding.HomeFlutterStyleLayoutBinding;
import com.zizhiguanjia.model_home.viewmodel.StudyViewModel;

import java.util.Objects;

/**
 * NativeStudyAIFragment的视图管理类
 * 负责管理和更新UI元素
 */
public class NativeStudyAIViewManager {
    private final HomeFlutterStyleLayoutBinding binding;
    private final StudyViewModel studyViewModel;
    private final Activity activity;
    private final LifecycleOwner lifecycleOwner;
    private SwipeRefreshLayout swipeRefreshLayout;

    public NativeStudyAIViewManager(@NonNull HomeFlutterStyleLayoutBinding binding, 
                                    @NonNull StudyViewModel studyViewModel, 
                                    @NonNull Activity activity,
                                    @NonNull LifecycleOwner lifecycleOwner) {
        this.binding = binding;
        this.studyViewModel = studyViewModel;
        this.activity = activity;
        this.lifecycleOwner = lifecycleOwner;
        setupSwipeRefresh();
    }

    /**
     * 设置下拉刷新
     */
    private void setupSwipeRefresh() {
        swipeRefreshLayout = binding.swipeRefreshLayout;
        swipeRefreshLayout.setOnRefreshListener(() -> {
            // 刷新数据
            if (studyViewModel != null) {
                studyViewModel.reshHomeData(false);
            }

            // 3秒后自动取消刷新状态
            swipeRefreshLayout.postDelayed(() -> {
                if (swipeRefreshLayout != null) {
                    swipeRefreshLayout.setRefreshing(false);
                }
            }, 3000);
        });
    }

    /**
     * 显示下拉刷新状态
     */
    public void showRefreshing() {
        if (swipeRefreshLayout != null) {
            swipeRefreshLayout.setRefreshing(true);
        }
    }

    /**
     * 隐藏下拉刷新状态
     */
    public void hideRefreshing() {
        if (swipeRefreshLayout != null) {
            swipeRefreshLayout.setRefreshing(false);
        }
    }

    /**
     * 更新顶部标题
     */
    public void updateTopTitle() {
        // 获取顶部栏视图
        View homeHead = binding.getRoot().findViewById(R.id.home_head);
        if (homeHead != null) {
            // 更新标题文本
            TextView titleTopic = homeHead.findViewById(R.id.titleTopic);
            if (titleTopic != null) {
                // 优先使用HomeBean中的DefaultNav字段作为标题
                if (studyViewModel != null && studyViewModel.getHomeBean().getValue() != null && 
                    studyViewModel.getHomeBean().getValue().getDefaultNav() != null &&
                    !studyViewModel.getHomeBean().getValue().getDefaultNav().isEmpty()) {
                    
                    String defaultNav = studyViewModel.getHomeBean().getValue().getDefaultNav();
                    titleTopic.setText(defaultNav);
                    LogUtils.e("NativeStudyAIViewManager - updateTopTitle - 使用DefaultNav更新顶部标题: " + defaultNav);
                } else {
                    // 备选方案：使用证书信息
                    String title = com.zizhiguanjia.lib_base.helper.CertificateHelper.initCertificate(AccountHelper.isUserLogin());
                    titleTopic.setText(title);
                    LogUtils.e("NativeStudyAIViewManager - updateTopTitle - 使用证书信息更新顶部标题: " + title);
                }
            }
        }
    }

    /**
     * 初始化章节练习UI
     */
    public void initChapterPracticeUI() {
        if (binding == null || studyViewModel == null) {
            LogUtils.e("NativeStudyAIViewManager - initChapterPracticeUI - binding或studyViewModel为空");
            return;
        }

        // 确保在主线程执行UI更新
        com.wb.lib_utils.utils.MainThreadUtils.post(() -> {
            try {
                LogUtils.e("NativeStudyAIViewManager - initChapterPracticeUI - 开始更新章节练习UI");
                
                // 设置学习时间
                String studyTimeText = studyViewModel.getFormattedStudyTime();
                LogUtils.e("NativeStudyAIViewManager - initChapterPracticeUI - 学习时间: " + studyTimeText);
                binding.tvStudyTime.setText(studyTimeText);

                // 安全获取已完成题目数
                String completedCount = studyViewModel.getCurrentCount().getValue();
                completedCount = completedCount != null ? completedCount : "0";
                LogUtils.e("NativeStudyAIViewManager - initChapterPracticeUI - 已完成题目数: " + completedCount);
                binding.tvCompletedCount.setText(completedCount);
                
                // 安全获取标题
                String title = studyViewModel.getPaperTitle().getValue();
                title = title != null ? title : "章节练习";
                LogUtils.e("NativeStudyAIViewManager - initChapterPracticeUI - 标题: " + title);
                binding.title.setText(title);
                
                // 安全获取总题目数
                String totalCount = studyViewModel.getQuestionCount().getValue();
                totalCount = totalCount != null ? totalCount : "0";
                LogUtils.e("NativeStudyAIViewManager - initChapterPracticeUI - 总题目数: " + totalCount);
                binding.tvTotalCount.setText("/" + totalCount + "题");

                // 安全获取进度百分比
                String progressPercent = studyViewModel.getProgressPercentText();
                LogUtils.e("NativeStudyAIViewManager - initChapterPracticeUI - 进度百分比: " + progressPercent);
                binding.tvProgressPercent.setText(progressPercent);

                // 安全获取进度值
                Integer progress = studyViewModel.getQuestionMakingProgress().getValue();
                progress = progress != null ? progress : 0;
                if (progress < 0) progress = 0; // 确保进度不为负
                LogUtils.e("NativeStudyAIViewManager - initChapterPracticeUI - 进度值: " + progress);
                binding.progressBar.setProgress(progress);

                // 安全获取正确率
                String rightStr = studyViewModel.getRightStr().getValue();
                rightStr = rightStr != null ? rightStr+"%" : "0%";
                LogUtils.e("NativeStudyAIViewManager - initChapterPracticeUI - 正确率: " + rightStr);
                setAccuracyText(binding.tvAccuracy, rightStr);

                // 设置按钮文本
                String buttonText = studyViewModel.getPracticeButtonText();
                LogUtils.e("NativeStudyAIViewManager - initChapterPracticeUI - 按钮文本: " + buttonText);
                binding.btnStartPractice.setText(buttonText);
                
                LogUtils.e("NativeStudyAIViewManager - initChapterPracticeUI - 章节练习UI已成功更新");
                //更新头像
                updateUserLoginAvatar();
            } catch (Exception e) {
                LogUtils.e("NativeStudyAIViewManager - initChapterPracticeUI - 异常: " + e.getMessage());
                e.printStackTrace();
                
                // 如果出错，使用默认值
                try {
                    binding.tvStudyTime.setText("0小时0分钟");
                    binding.tvCompletedCount.setText("0");
                    binding.tvTotalCount.setText("/0题");
                    binding.tvProgressPercent.setText("0%");
                    binding.progressBar.setProgress(0);
                    binding.tvAccuracy.setText("0%");
                    binding.btnStartPractice.setText("开始学习");
                    LogUtils.e("NativeStudyAIViewManager - initChapterPracticeUI - 使用默认值设置UI");
                } catch (Exception ex) {
                    LogUtils.e("NativeStudyAIViewManager - initChapterPracticeUI - 严重错误，无法设置默认值: " + ex.getMessage());
                }
            }
        });
    }

    /**
     * 更新章节练习数据
     *
     * @param homeBean 从API获取的首页数据
     */
    public void updateChapterPracticeData(HomeBean homeBean) {
        if (homeBean == null || studyViewModel == null) {
            LogUtils.e("NativeStudyAIViewManager - updateChapterPracticeData - homeBean或studyViewModel为空");
            return;
        }

        LogUtils.e("NativeStudyAIViewManager - updateChapterPracticeData - 开始更新章节练习数据");

        try {
            // 获取数据
            int answerNumber = homeBean.getAnswerNumber();
            int totalNumber = homeBean.getTotalNumber();
            String correctPercent = homeBean.getCorrectPercent();
            int studyMinutes = homeBean.getStudyMinutes();

            LogUtils.e("NativeStudyAIViewManager - updateChapterPracticeData - 已完成题目数: " + answerNumber + 
                    ", 总题目数: " + totalNumber + 
                    ", 正确率: " + correctPercent + 
                    ", 学习时间(分钟): " + studyMinutes);

            // 计算进度
            int progress = 0;
            String progressStr = homeBean.getQuestionMakingProgress();

            try {
                if (progressStr != null && !progressStr.isEmpty()) {
                    // 尝试解析进度字符串
                    String cleanProgress = progressStr.replace("%", "");
                    progress = (int) Float.parseFloat(cleanProgress);
                    LogUtils.e("NativeStudyAIViewManager - updateChapterPracticeData - 从进度字符串解析: " + progress + "%");
                } else if (totalNumber > 0) {
                    // 如果没有进度字符串，根据完成题目和总题目计算
                    progress = (int) (((float) answerNumber / totalNumber) * 100);
                    LogUtils.e("NativeStudyAIViewManager - updateChapterPracticeData - 根据题目数计算进度: " + progress + "%");
                }
            } catch (NumberFormatException e) {
                LogUtils.e("NativeStudyAIViewManager - updateChapterPracticeData - 解析进度字符串失败: " + e.getMessage());
                // 如果解析失败，尝试使用完成题目和总题目计算
                if (totalNumber > 0) {
                    progress = (int) (((float) answerNumber / totalNumber) * 100);
                    LogUtils.e("NativeStudyAIViewManager - updateChapterPracticeData - 解析失败，重新计算进度: " + progress + "%");
                }
            }

            // 判断是否已经开始过章节练习
            boolean hasStarted = answerNumber > 0;

            // 确保正确率有值
            if (correctPercent == null || correctPercent.isEmpty()) {
                correctPercent = answerNumber > 0 ? "0%" : "0%";
                LogUtils.e("NativeStudyAIViewManager - updateChapterPracticeData - 设置默认正确率: " + correctPercent);
            }

            // 保存最终数据到局部变量，用于lambda表达式
            final String finalCorrectPercent = correctPercent;
            final int finalProgress = progress;
            final boolean finalHasStarted = hasStarted;

            // 批量更新章节练习数据 - 确保在主线程执行
            com.wb.lib_utils.utils.MainThreadUtils.post(() -> {
                LogUtils.e("NativeStudyAIViewManager - updateChapterPracticeData - 在主线程更新ViewModel数据");
                
                // 先设置零值，强制触发观察者
                studyViewModel.updateChapterPracticeData(
                        "0",
                        "0",
                        "0%",
                        0,
                        0,
                        false
                );
                
                // 然后设置真实值
                studyViewModel.updateChapterPracticeData(
                        String.valueOf(answerNumber),
                        String.valueOf(totalNumber),
                        finalCorrectPercent,
                        studyMinutes,
                        finalProgress,
                        finalHasStarted
                );
                
                LogUtils.e("NativeStudyAIViewManager - updateChapterPracticeData - ViewModel数据已更新，强制更新UI");
                
                // 延迟100ms后强制刷新UI
                new android.os.Handler().postDelayed(() -> {
                    initChapterPracticeUI();
                }, 100);
            });

            LogUtils.e("NativeStudyAIViewManager - updateChapterPracticeData - 章节练习数据更新流程完成");
        } catch (Exception e) {
            LogUtils.e("NativeStudyAIViewManager - updateChapterPracticeData - 异常: " + e.getMessage());
            e.printStackTrace();
            
            // 发生异常时，还是尝试更新UI
            com.wb.lib_utils.utils.MainThreadUtils.post(this::initChapterPracticeUI);
        }
    }

    /**
     * 初始化通知栏
     */
    public void initNotify() {
        LogUtils.e("NativeStudyAIViewManager - initNotify - 开始初始化通知栏");

        try {
            // 获取通知栏视图
            androidx.cardview.widget.CardView cdNotify = binding.cdNotify;
            TextView tvNotify = null;
            if (cdNotify != null) {
                tvNotify = cdNotify.findViewById(R.id.tv_notify_des);
                LogUtils.e("NativeStudyAIViewManager - initNotify - 找到通知栏卡片视图");
            } else {
                LogUtils.e("NativeStudyAIViewManager - initNotify - 找不到通知栏卡片视图 R.id.cd_notify");
                return;
            }

            if (tvNotify != null) {
                LogUtils.e("NativeStudyAIViewManager - initNotify - 找到通知文本视图");

                // 启用跑马灯效果
                tvNotify.setSelected(true);
                tvNotify.setFocusable(true);
                tvNotify.setFocusableInTouchMode(true);
                LogUtils.e("NativeStudyAIViewManager - initNotify - 启用跑马灯效果");

                // 默认隐藏通知栏
                cdNotify.setVisibility(View.GONE);

                // 立即设置当前值
                if (studyViewModel != null) {
                    // 获取当前通知文本
                    String currentText = studyViewModel.getOfficialText().getValue();
                    if (currentText != null) {
                        tvNotify.setText(currentText);
                        LogUtils.e("NativeStudyAIViewManager - initNotify - 立即设置通知文本: " + currentText);
                    }

                    // 获取当前是否显示
                    Boolean isOfficial = studyViewModel.getIsOfficial().getValue();
                    if (isOfficial != null && isOfficial) {
                        cdNotify.setVisibility(View.VISIBLE);
                        tvNotify.setVisibility(View.VISIBLE);
                        LogUtils.e("NativeStudyAIViewManager - initNotify - 立即显示通知栏");
                    }

                    // 观察通知文本变化
                    final TextView finalTvNotify = tvNotify;
                    studyViewModel.getOfficialText().observe(lifecycleOwner, officialText -> {
                        if (finalTvNotify != null && officialText != null) {
                            finalTvNotify.setText(String.valueOf(officialText));
                            // 重新启用跑马灯效果（文本变化后需要重新设置）
                            finalTvNotify.setSelected(true);
                            LogUtils.e("NativeStudyAIViewManager - initNotify - 设置通知文本: " + officialText);
                        }
                    });

                    // 观察是否显示通知栏
                    studyViewModel.getIsOfficial().observe(lifecycleOwner, isOfficialValue -> {
                        // 根据isOfficial属性控制通知栏的显示
                        boolean isVisible = isOfficialValue != null && (Boolean)isOfficialValue;
                        cdNotify.setVisibility(isVisible ? View.VISIBLE : View.GONE);
                        finalTvNotify.setVisibility(isVisible ? View.VISIBLE : View.GONE);
                        // 当通知栏显示时，确保跑马灯效果启用
                        if (isVisible) {
                            finalTvNotify.setSelected(true);
                        }
                        LogUtils.e("NativeStudyAIViewManager - initNotify - 是否官方题库: " + isOfficialValue + ", 通知栏显示: " + (isVisible ? "是" : "否"));
                    });
                }
            } else {
                LogUtils.e("NativeStudyAIViewManager - initNotify - 找不到通知文本视图 R.id.tv_notify_des");
            }
        } catch (Exception e) {
            LogUtils.e("NativeStudyAIViewManager - initNotify - 异常: " + e.getMessage());
        }
    }

    /**
     * 更新用户登录状态头像
     */
    public void updateUserLoginAvatar() {
        try {
            if (binding != null && binding.homeHead != null && binding.homeHead.imgMainUser != null) {
                if (AccountHelper.isUserLogin()) {
                    // 显示已登录状态的头像
                    Glide.with(Objects.requireNonNull(activity))
                            .load(Objects.requireNonNull(studyViewModel.getHomeBean().getValue()).getHeadimg())
                            .apply(new RequestOptions())
                            .into(binding.homeHead.imgMainUser);
                    LogUtils.e("NativeStudyAIViewManager - 用户已登录，显示登录状态头像");
                } else {
                    // 显示未登录状态的头像
                    binding.homeHead.imgMainUser.setImageResource(R.drawable.home_login_state_no);
                    LogUtils.e("NativeStudyAIViewManager - 用户未登录，显示默认头像");
                }
            }
        } catch (Exception e) {
            LogUtils.e("NativeStudyAIViewManager - updateUserLoginAvatar - 异常: " + e.getMessage());
        }
    }

    /**
     * 设置正确率文本，实现小数点前大后小的效果
     * @param tvAccuracy 正确率TextView
     * @param accuracy 正确率字符串，如 "42.86%" 或 "100%"
     */
    private void setAccuracyText(TextView tvAccuracy, String accuracy) {
        try {
            if (tvAccuracy == null || accuracy == null || accuracy.isEmpty()) {
                LogUtils.e("NativeStudyAIViewManager - setAccuracyText - 参数为空");
                return;
            }

            LogUtils.e("NativeStudyAIViewManager - setAccuracyText - 设置正确率: " + accuracy);

            SpannableStringBuilder builder = new SpannableStringBuilder(accuracy);

            // 查找小数点的位置
            int dotIndex = accuracy.indexOf(".");
            if (dotIndex != -1) {
                // 有小数点的情况，如 "42.86%"
                // 小数点前面的数字设置正常大小 (1.0f)
                builder.setSpan(new RelativeSizeSpan(1.0f), 0, dotIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                // 小数点后面的数字和百分号设置小字体 (0.6f)
                builder.setSpan(new RelativeSizeSpan(0.6f), dotIndex, accuracy.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

                LogUtils.e("NativeStudyAIViewManager - setAccuracyText - 应用小数点样式，前部分: " + accuracy.substring(0, dotIndex) +
                          ", 后部分: " + accuracy.substring(dotIndex));
            } else {
                // 没有小数点的情况，如 "100%"
                // 查找百分号的位置
                int percentIndex = accuracy.indexOf("%");
                if (percentIndex != -1) {
                    // 数字部分设置正常大小
                    builder.setSpan(new RelativeSizeSpan(1.0f), 0, percentIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                    // 百分号设置小字体
                    builder.setSpan(new RelativeSizeSpan(0.7f), percentIndex, accuracy.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

                    LogUtils.e("NativeStudyAIViewManager - setAccuracyText - 应用百分号样式，数字部分: " + accuracy.substring(0, percentIndex) +
                              ", 百分号: " + accuracy.substring(percentIndex));
                }
            }

            tvAccuracy.setText(builder);
            LogUtils.e("NativeStudyAIViewManager - setAccuracyText - 正确率文本设置完成");

        } catch (Exception e) {
            LogUtils.e("NativeStudyAIViewManager - setAccuracyText - 异常: " + e.getMessage());
            e.printStackTrace();
            // 异常时使用普通文本
            if (tvAccuracy != null && accuracy != null) {
                tvAccuracy.setText(accuracy);
            }
        }
    }
}