package com.zizhiguanjia.model_home.view;

import static com.zizhiguanjia.model_home.R.color.menu_nav_icon_color;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;

import com.bumptech.glide.Glide;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_home.R;
import com.zizhiguanjia.model_home.bean.MenuNavsBean;
import com.zizhiguanjia.model_home.databinding.HomeFlutterStyleLayoutBinding;
import com.zizhiguanjia.model_home.viewmodel.StudyViewModel;

import java.util.List;
import java.util.Objects;

/**
 * 负责管理NativeStudyAIFragment的菜单导航功能
 */
public class NativeStudyAIMenuManager {
    private final HomeFlutterStyleLayoutBinding binding;
    private final StudyViewModel studyViewModel;
    private final Activity activity;
    private final LifecycleOwner lifecycleOwner;

    public NativeStudyAIMenuManager(@NonNull HomeFlutterStyleLayoutBinding binding,
                                    @NonNull StudyViewModel studyViewModel,
                                    @NonNull Activity activity,
                                    @NonNull LifecycleOwner lifecycleOwner) {
        this.binding = binding;
        this.studyViewModel = studyViewModel;
        this.activity = activity;
        this.lifecycleOwner = lifecycleOwner;

        // 直接在构造函数中设置观察者
        setupMenuNavsObserver();
    }

    /**
     * 设置菜单导航数据观察者
     */
    private void setupMenuNavsObserver() {
        LogUtils.e("NativeStudyAIMenuManager - 设置菜单导航数据观察者");

        // 观察菜单导航数据变化
        studyViewModel.getMenuNavs().observe(lifecycleOwner, menuNavs -> {
            LogUtils.e("NativeStudyAIMenuManager - 菜单导航数据变化观察者被触发 - 数量: " +
                    (menuNavs != null ? menuNavs.size() : 0));

            // 更新UI
            updateMenuNavsView(menuNavs);
        });
    }

    /**
     * 更新菜单导航视图
     *
     * @param menuNavs 菜单导航数据
     */
    @SuppressLint("MissingInflatedId")
    public void updateMenuNavsView(List<MenuNavsBean> menuNavs) {
        if (binding == null) return;

        // 获取菜单导航容器
        LinearLayout menuNavContainer = binding.getRoot().findViewById(R.id.menu_nav_container);
        if (menuNavContainer == null) {
            LogUtils.e("NativeStudyAIMenuManager - updateMenuNavsView - 找不到菜单导航容器");
            return;
        }

        // 清除旧的菜单导航按钮
        menuNavContainer.removeAllViews();

        if (menuNavs == null || menuNavs.isEmpty()) {
            LogUtils.e("NativeStudyAIMenuManager - updateMenuNavsView - 菜单导航数据为空");
            return;
        }

        LogUtils.e("NativeStudyAIMenuManager - updateMenuNavsView - 开始更新菜单导航，菜单项数量: " + menuNavs.size());

        // 如果菜单导航项超过5个，使用水平滚动视图
        if (menuNavs.size() > 5) {
            // 创建水平滚动视图
            HorizontalScrollView scrollView = new HorizontalScrollView(activity);
            scrollView.setLayoutParams(new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT));
            scrollView.setHorizontalScrollBarEnabled(false); // 隐藏滚动条

            // 创建内部的线性布局容器
            LinearLayout innerContainer = new LinearLayout(activity);
            innerContainer.setLayoutParams(new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT));
            innerContainer.setOrientation(LinearLayout.HORIZONTAL);

            // 添加菜单按钮到内部容器
            for (MenuNavsBean menuNav : menuNavs) {
                addMenuNavButton(menuNav, innerContainer);
            }

            // 将内部容器添加到滚动视图
            scrollView.addView(innerContainer);

            // 将滚动视图添加到主容器
            menuNavContainer.addView(scrollView);
            LogUtils.e("NativeStudyAIMenuManager - updateMenuNavsView - 创建了水平滚动视图，包含" + menuNavs.size() + "个按钮");
        } else {
            // 菜单导航项不超过5个，直接添加到主容器，平均分配空间
            for (MenuNavsBean menuNav : menuNavs) {
                // 使用布局文件加载菜单导航按钮
                View menuNavButtonView = LayoutInflater.from(activity).inflate(R.layout.item_menu_nav_button, menuNavContainer, false);
                menuNavButtonView.findViewById(R.id.ll_container).setBackgroundColor(Color.parseColor("#00000000"));
                // 设置按钮的布局参数
                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                        0,
                        LinearLayout.LayoutParams.WRAP_CONTENT,
                        1.0f);  // 权重为1，使按钮平均分配空间


                // 设置更小的边距，让按钮能够更紧凑地排列
                int margin = activity.getResources().getDimensionPixelSize(R.dimen.menu_nav_button_margin);
                params.setMargins(margin, 0, margin, margin);
                menuNavButtonView.setLayoutParams(params);

                setupMenuNavButton(menuNav, menuNavButtonView);

                // 添加到容器
                menuNavContainer.addView(menuNavButtonView);
            }
            LogUtils.e("NativeStudyAIMenuManager - updateMenuNavsView - 创建了" + menuNavs.size() + "个平均分配空间的按钮");
        }
    }

    /**
     * 添加菜单导航按钮到容器
     *
     * @param menuNav   菜单导航数据
     * @param container 容器
     */
    private void addMenuNavButton(MenuNavsBean menuNav, LinearLayout container) {
        // 使用布局文件加载菜单导航按钮
        View menuNavButtonView = LayoutInflater.from(activity).inflate(R.layout.item_menu_nav_button, container, false);

        // 设置按钮的布局参数
        int buttonWidth = activity.getResources().getDimensionPixelSize(R.dimen.menu_nav_button_width);
        int margin = activity.getResources().getDimensionPixelSize(R.dimen.menu_nav_button_margin);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                buttonWidth,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        params.setMargins(margin, 0, margin, margin);
        menuNavButtonView.setLayoutParams(params);

        setupMenuNavButton(menuNav, menuNavButtonView);

        // 添加到容器
        container.addView(menuNavButtonView);
    }

    /**
     * 设置菜单导航按钮的内容和点击事件
     *
     * @param menuNav           菜单导航数据
     * @param menuNavButtonView 按钮视图
     */
    private void setupMenuNavButton(MenuNavsBean menuNav, View menuNavButtonView) {
        // 获取图标和文本控件
        ImageView iconImageView = menuNavButtonView.findViewById(R.id.icon_image_view);
        TextView textView = menuNavButtonView.findViewById(R.id.text_view);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        layoutParams.setMargins(0,0,0,0);
        textView.setLayoutParams(layoutParams);
        int margin = activity.getResources().getDimensionPixelSize(R.dimen.course_catalog_width);
        iconImageView.setLayoutParams(new LinearLayout.LayoutParams(
                margin,
                margin));
        // 优先使用API返回的图片URL
        if (!TextUtils.isEmpty(menuNav.getIconUrl())) {
            try {
                // 使用Glide加载网络图片
                Glide.with(Objects.requireNonNull(activity))
                        .load(menuNav.getIconUrl())
                        .into(iconImageView);
            } catch (Exception e) {
                e.printStackTrace();
            }

        } else {
            // 如果URL为空，则尝试使用imgSrc或设置默认图标
            if (menuNav.getImgSrc() != null && !menuNav.getImgSrc().isEmpty()) {
                try {
                    // 尝试将imgSrc解析为资源ID
                    int resourceId = Integer.parseInt(menuNav.getImgSrc());
                    iconImageView.setImageResource(resourceId);
                    iconImageView.setColorFilter(activity.getResources().getColor(menu_nav_icon_color));
                } catch (NumberFormatException e) {
                    // 解析失败，设置默认图标
                    setDefaultIcon(iconImageView, menuNav.getNavType());
                }
            } else {
                // 没有imgSrc，设置默认图标
                setDefaultIcon(iconImageView, menuNav.getNavType());
            }
        }

        // 设置文本
        textView.setText(menuNav.getTitle());

        // 添加日志输出
        LogUtils.e("NativeStudyAIMenuManager - setupMenuNavButton - 按钮类型: " + menuNav.getType() +
                ", NavType: " + menuNav.getNavType() + ", 标题: " + menuNav.getTitle());

        // 设置点击事件
        final MenuNavsBean finalMenuNav = menuNav; // 捕获menuNav
        menuNavButtonView.setOnClickListener(v -> {
//            if (!NoDoubleClickUtils.isDoubleClick()) {
            // 添加日志输出，记录点击事件
            LogUtils.e("NativeStudyAIMenuManager - 按钮点击 - 类型: " + finalMenuNav.getType() +
                    ", NavType: " + finalMenuNav.getNavType() + ", 标题: " + finalMenuNav.getTitle());

            // 使用StudyViewModel处理菜单按钮点击事件
            if (studyViewModel != null) {
                // 创建一个副本，确保数据不会被意外修改
                final MenuNavsBean safeCopy = new MenuNavsBean();
                safeCopy.setNavType(finalMenuNav.getNavType());
                safeCopy.setTitle(finalMenuNav.getTitle());
                safeCopy.setImgSrc(finalMenuNav.getImgSrc());
                safeCopy.setUrl(finalMenuNav.getUrl());
                safeCopy.setDo(finalMenuNav.isDo());
                safeCopy.setDateBoxStatus(finalMenuNav.getDateBoxStatus());
                safeCopy.setRowNum(finalMenuNav.getRowNum());
                studyViewModel.onMainNavFunctionClick(safeCopy);
                // 添加一个小延迟，模拟断点的效果
//                    v.postDelayed(() -> {
//                        LogUtils.e("NativeStudyAIMenuManager - 延迟后处理按钮点击 - 类型: " + safeCopy.getType());
//                        studyViewModel.onMainNavFunctionClick(safeCopy);
//                    }, 100); // 100毫秒的延迟，通常足够了
            } else {
                LogUtils.e("NativeStudyAIMenuManager - 按钮点击 - studyViewModel为空");
                com.wb.lib_utils.utils.ToastUtils.normal("系统繁忙，请稍后再试");
            }
//            }
        });
    }

    /**
     * 设置默认图标
     *
     * @param imageView 图标视图
     * @param navType   导航类型
     */
    private void setDefaultIcon(ImageView imageView, int navType) {
        // 默认图标
        int iconResId = R.drawable.icon_bell;

        // 设置适当的颜色过滤器
        imageView.setColorFilter(activity.getResources().getColor(menu_nav_icon_color));

        // 根据导航类型选择合适的图标
        switch (navType) {
            case 1: // 错题集
                iconResId = R.drawable.icon_bell;
                break;
            case 2: // 收藏夹
                iconResId = R.drawable.icon_bell;
                break;
            case 3: // 题型练习
                iconResId = R.drawable.icon_bell;
                break;
            case 4: // 模拟考试
            case 10:
            case 11:
                iconResId = R.drawable.icon_bell;
                break;
            case 5: // 历年真题
                iconResId = R.drawable.icon_bell;
                break;
            case 6: // 二建模考
                iconResId = R.drawable.icon_bell;
                break;
            case 16: // 易错100题
                iconResId = R.drawable.icon_bell;
                break;
            case 17: // 学习资料
                iconResId = R.drawable.icon_bell;
                break;
            case 8: // 直播课程
                iconResId = R.drawable.icon_bell;
                break;
        }

        try {
            // 设置图标
            imageView.setImageResource(iconResId);
        } catch (Exception e) {
            // 如果资源不存在，使用应用图标作为备选
            try {
                imageView.setImageResource(R.drawable.icon_bell);
            } catch (Exception ex) {
                // 如果应用图标也不存在，不设置图片
                LogUtils.e("NativeStudyAIMenuManager - 设置图标失败: " + e.getMessage());
            }
        }
    }
} 