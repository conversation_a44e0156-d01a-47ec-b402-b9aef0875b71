package com.zizhiguanjia.model_certificate.fragment;

import android.os.Bundle;
import android.view.Gravity;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.constants.CertificateRouterPath;
import com.zizhiguanjia.lib_base.helper.AddressHelper;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.model_certificate.R;
import com.zizhiguanjia.model_certificate.adapter.CertificateGroupAdapter;
import com.zizhiguanjia.model_certificate.adapter.CertificaterRightAdapter;
import com.zizhiguanjia.model_certificate.config.CertificateTypeConfig;
import com.zizhiguanjia.model_certificate.databinding.CerCertificatebyaddressLayoutBinding;
import com.zizhiguanjia.model_certificate.listener.CertificateUpdateListener;
import com.zizhiguanjia.model_certificate.manager.CertificateManager;
import com.zizhiguanjia.model_certificate.model.CertificateByAddressBean;
import com.zizhiguanjia.model_certificate.model.CertificateConfigBean;
import com.zizhiguanjia.model_certificate.model.SecondConstructionBean;
import com.zizhiguanjia.model_certificate.navigator.CertificateByAddressNavigator;
import com.zizhiguanjia.model_certificate.viewmodel.CertificateByAddressViewModel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.recyclerview.widget.LinearLayoutManager;

import static androidx.recyclerview.widget.RecyclerView.HORIZONTAL;

@Route(path = CertificateRouterPath.CHOSEADDRESSBYADDRESS_FRAGMENT)
@BindRes(statusBarStyle = BarStyle.DARK_CONTENT, swipeBack = SwipeStyle.NONE)
public class CertificateByAddressFragment extends BaseFragment implements CertificateByAddressNavigator, TitleBar.OnLeftBarListener,
        CertificateUpdateListener {
    private CerCertificatebyaddressLayoutBinding binding;
    private String addressName, addressId;
    private boolean tgAddress, tgSave;
    private CertificaterRightAdapter mSecondContructionAdapter;
    private CertificateGroupAdapter mGroupSelectAdapter;

    /**
     * 显示的记录数据
     */
    private List<SecondConstructionBean.RecordsBean> showRecords;

    /**
     * 显示子列表
     */
    private boolean showMoreChild = false;

    public static CertificateByAddressFragment getInstance(String addressName, String addressId, boolean tgAddress) {
        Bundle bundle = new Bundle();
        bundle.putString("addressName", addressName);
        bundle.putString("addressId", addressId);
        bundle.putBoolean("tgAddress", tgAddress);
        CertificateByAddressFragment certificateByAddressFragment = new CertificateByAddressFragment();
        certificateByAddressFragment.setArguments(bundle);
        return certificateByAddressFragment;
    }

    @BindViewModel
    CertificateByAddressViewModel certificateByAddressViewModel;

    @Override
    public int initLayoutResId() {
        return R.layout.cer_certificatebyaddress_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        binding = getBinding();
        binding.setModel(certificateByAddressViewModel);
        showLoading();
        initData();
        initView();
        initListener();
        certificateByAddressViewModel.initParams(this, addressId);
    }

    @Override
    public void showCertificateAddressView(List<CertificateByAddressBean.RecordsDTO> recordsDTOS) {
        binding.msvExamChapter.showContent();
        //        addressAdapter.setDataItems(recordsDTOS);
    }

    @Override
    public void initData() {
        String addressNameTapis = getArguments().getString("addressName");
        if (addressNameTapis == null || addressNameTapis.isEmpty()) {
            addressName = initArguments().getString("addressName");
            addressId = initArguments().getString("addressId");
            tgAddress = initArguments().getBoolean("tgAddress", false);
        } else {
            addressName = getArguments().getString("addressName");
            addressId = getArguments().getString("addressId");
            tgAddress = getArguments().getBoolean("tgAddress", false);
        }
        LogUtils.e("------>>>tgAddress" + tgAddress);
        tgSave = initArguments().getBoolean("tgSave", true);
    }

    @Override
    public void initView() {
        //        binding.rcyCertificate.setLayoutManager(new GridLayoutManager(getContext(), 3));
        //        binding.rcyCertificate.addItemDecoration(new GridDividerItemDecoration(DpUtils.dp2px(getContext(), 12), DpUtils.dp2px(getContext(), 12), 0));
        //        addressAdapter = new CertificateByAddressAdapter(addressId, addressName);
        //        binding.rcyCertificate.setAdapter(addressAdapter);
        binding.tvCertificateName.setText(addressName);
        binding.rcyCertificate.setLayoutManager(new LinearLayoutManager(getContext()));
        if (mSecondContructionAdapter == null) {
            mSecondContructionAdapter = new CertificaterRightAdapter(addressId, addressName, tgAddress);
            binding.rcyCertificate.setAdapter(mSecondContructionAdapter);
        }
        binding.rvGroupList.setLayoutManager(new LinearLayoutManager(getContext(), HORIZONTAL,false));
        if (mGroupSelectAdapter == null) {
            mGroupSelectAdapter = new CertificateGroupAdapter();
            binding.rvGroupList.setAdapter(mGroupSelectAdapter);
        }
    }

    @Override
    public void initListener() {
        binding.tlbCertificates.setBackListener(this);
        mSecondContructionAdapter.setCertificateUpdateListener(this);
        mGroupSelectAdapter.setCertificateUpdateListener(this);

        //        addressAdapter.setCertificateUpdateListener(this);
    }

    @Override
    public void checkToPage() {
        PointHelper.joinPointData(PointerMsgType.POINTER_A_SWITCHSUBJECT_CHAGGEREGION, false);
        if (tgAddress) {
            initArguments().putBoolean("isUpdate", true);
            startFragment(AddressHelper.mainPage(getContext()));
        }
        finish();
    }

    @Override
    public void showErrorToast(String msg) {
        ToastUtils.normal(msg, Gravity.CENTER);
    }

    @Override
    public void showLoading() {
        binding.msvExamChapter.showLoading();
    }

    @Override
    public void showEmpty() {
        binding.msvExamChapter.showEmpty();
    }

    @Override
    public void showNetWork() {
        binding.msvExamChapter.showNoNetwork();
    }

    @Override
    public void showSecondContructions(List<SecondConstructionBean.GroupBean> groups, List<SecondConstructionBean.RecordsBean> recordsBeans) {
        binding.msvExamChapter.showContent();
        mSecondContructionAdapter.setDataItems(recordsBeans);
        showRecords = recordsBeans;
        if (groups.isEmpty()) {
            binding.rvGroupList.setVisibility(View.GONE);
        } else {
            binding.rvGroupList.setVisibility(View.VISIBLE);
            if (mGroupSelectAdapter.getCurrentSelectBean() == null && !recordsBeans.isEmpty()) {
                SecondConstructionBean.RecordsBean bean = recordsBeans.get(0);
                SecondConstructionBean.GroupBean targetGroup = null;

                // 首先尝试通过ParentId匹配
                for (SecondConstructionBean.GroupBean group : groups) {
                    if (group != null && group.getId() != null && bean != null && bean.getParentId() != null && group.getId().toString().equals(
                            bean.getParentId())) {
                        targetGroup = group;
                        break;
                    }
                }

                // 如果通过ParentId没有匹配到，则根据科目ID判断分类
                if (targetGroup == null && bean != null) {
                    boolean isAqySubject = isAqySubject(bean);
                    for (SecondConstructionBean.GroupBean group : groups) {
                        if (group != null && group.getName() != null) {
                            // 如果是安全员科目，选择安全员分类
                            if (isAqySubject && group.getName().contains("安全员")) {
                                targetGroup = group;
                                break;
                            }
                            // 如果是二建科目，选择二建分类
                            else if (!isAqySubject && (group.getName().contains("二建") || group.getName().contains("建造师"))) {
                                targetGroup = group;
                                break;
                            }
                        }
                    }
                }

                if (targetGroup != null) {
                    mGroupSelectAdapter.setCurrentSelectBean(targetGroup);
                }
            }
            mGroupSelectAdapter.setDataItems(groups);
        }
    }

    /**
     * 判断是否为安全员科目
     * @param bean 科目数据
     * @return true表示安全员科目，false表示二建科目
     */
    private boolean isAqySubject(SecondConstructionBean.RecordsBean bean) {
        if (bean == null) return false;

        // 根据科目ID判断：以"2"开头的是二建，其他的是安全员
        String idStr = String.valueOf(bean.getId());
        if (idStr.startsWith("2")) {
            return false; // 二建
        }

        // 根据科目名称进一步判断
        String name = bean.getName();
        if (name != null) {
            // 包含安全员相关关键词的都归类为安全员
            if (name.contains("安全员") || name.contains("安全生产") || name.contains("道路运输") ||
                name.contains("水利水电") || name.contains("主要负责人") || name.contains("管理人员")) {
                return true;
            }
            // 包含二建相关关键词的归类为二建
            if (name.contains("二建") || name.contains("建造师") || name.contains("施工管理") ||
                name.contains("工程法规") || name.contains("建筑工程") || name.contains("市政工程") ||
                name.contains("机电工程")) {
                return false;
            }
        }

        // 默认情况下，以"1"开头的ID归类为安全员
        return !idStr.startsWith("2");
    }

    @Override
    public void onClicked(View v) {
        if (v.getId() == R.id.tvUpdateName) {
            checkToPage();
        } else if (showMoreChild) {
            showMoreChild = false;
            mSecondContructionAdapter.setDataItems(showRecords);
            mSecondContructionAdapter.setUseParentId(false);
        } else {
            finish();
        }
    }

    @Override
    public void onUpdateCertificate(CertificateConfigBean mainCertificateBean) {
        LogUtils.e("证书更新------>>>>" + tgSave + "****" + mainCertificateBean.getGroupId());
        //        if(String.valueOf(mainCertificateBean.getSubjectId()).startsWith("1")){
        //            BaseConfig.MAJOR_PID=1100;
        //        }else {
        BaseConfig.MAJOR_PID = Integer.parseInt(mainCertificateBean.getGroupId());
        KvUtils.save("mjPid", BaseConfig.MAJOR_PID + "");
        //        }
        if (tgSave) {
            CertificateManager.getInstance().updataCertificate(mainCertificateBean);
            Bus.post(new MsgEvent(CertificateTypeConfig.MSG_TYPE_FINSH_ADDRESS_MAJOR));
        } else {
            Map<String, String> map = new HashMap<>();
            map.put("cityCode", String.valueOf(mainCertificateBean.getCityCode()));
            map.put("majId", String.valueOf(mainCertificateBean.getSubjectId()));
            map.put("addressName", mainCertificateBean.getCityName());
            map.put("certificateName", String.valueOf(mainCertificateBean.getSubjectName()));
            Bus.post(new MsgEvent(CertificateTypeConfig.MSG_TYPE_FINSH_ADDRESS_MAJOR, GsonUtils.gsonString(map)));
        }
        finish();
    }

    /**
     * 组改变
     *
     * @param bean 改变后的组
     */
    @Override
    public void onChangeGroup(SecondConstructionBean.GroupBean bean) {
        certificateByAddressViewModel.getCertificatetByAddressData(addressId, bean);
    }

    @Override
    public void showMoreChild(String groupId, String groupName, SecondConstructionBean.RecordsBean.ChildsBean majorChildBean) {
        showMoreChild = true;
        SecondConstructionBean.RecordsBean recordsBean = new SecondConstructionBean.RecordsBean();
        recordsBean.setChilds(majorChildBean.getChilds());
        recordsBean.setIcon(majorChildBean.getIcon());
        recordsBean.setName(groupName + " " + majorChildBean.getName());
        recordsBean.setId(Integer.parseInt(groupId));
        recordsBean.setBindOrderType(majorChildBean.getBindOrderType());
        recordsBean.setSelectType(majorChildBean.getSelectType());
        List<SecondConstructionBean.RecordsBean> list = new ArrayList<>();
        list.add(recordsBean);
        mSecondContructionAdapter.setDataItems(list);
        mSecondContructionAdapter.setUseParentId(true);

    }
}
