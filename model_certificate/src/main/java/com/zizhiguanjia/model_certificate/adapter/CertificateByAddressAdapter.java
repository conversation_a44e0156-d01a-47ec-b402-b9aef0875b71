package com.zizhiguanjia.model_certificate.adapter;

import android.view.View;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.zizhiguanjia.model_certificate.R;
import com.zizhiguanjia.model_certificate.config.ClassFLyTypeConfig;
import com.zizhiguanjia.model_certificate.databinding.CerTificatebyaddressItemBinding;
import com.zizhiguanjia.model_certificate.listener.CertificateUpdateListener;
import com.zizhiguanjia.model_certificate.model.CertificateBean;
import com.zizhiguanjia.model_certificate.model.CertificateByAddressBean;
import com.zizhiguanjia.model_certificate.model.CertificateConfigBean;

public class CertificateByAddressAdapter extends BaseAdapter<CertificateByAddressBean.RecordsDTO> {
    private CerTificatebyaddressItemBinding binding;
    private String addressId,addressName;
    private CertificateUpdateListener certificateUpdateListener;

    public void setCertificateUpdateListener(CertificateUpdateListener certificateUpdateListener) {
        this.certificateUpdateListener = certificateUpdateListener;
    }

    public CertificateByAddressAdapter(String addressId,String addressName) {
        super(R.layout.cer_tificatebyaddress_item);
        this.addressId=addressId;
        this.addressName=addressName;
    }

    @Override
    protected void bind(BaseViewHolder holder, CertificateByAddressBean.RecordsDTO item, int position) {
        binding=holder.getBinding();
        binding.tvCertificateName.setText(item.getName());
        binding.tvCertificateName.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CertificateByAddressAdapter.this.onClick(item);
            }
        });
    }
    public void onClick(CertificateByAddressBean.RecordsDTO majorChildBean) {
        CertificateConfigBean mainCertificateBean = new CertificateConfigBean();
        mainCertificateBean.setCityCode(addressId);
        mainCertificateBean.setCityName(addressName);
        mainCertificateBean.setSubjectId(majorChildBean.getId());
        mainCertificateBean.setSubjectName(majorChildBean.getName());
        mainCertificateBean.setDes(addressName+"·"+majorChildBean.getName());
        mainCertificateBean.setClassflyId(getClassflyIdBySubject(majorChildBean));
        if (certificateUpdateListener == null) return;
        certificateUpdateListener.onUpdateCertificate(mainCertificateBean);
    }

    /**
     * 根据科目信息判断分类ID
     * @param majorChildBean 科目信息
     * @return 分类ID
     */
    private int getClassflyIdBySubject(CertificateByAddressBean.RecordsDTO majorChildBean) {
        if (majorChildBean == null) {
            return ClassFLyTypeConfig.CLASS_FLY_AQY;
        }

        String idStr = String.valueOf(majorChildBean.getId());
        String name = majorChildBean.getName();

        // 根据科目ID判断：以"2"开头的是二建
        if (idStr.startsWith("2")) {
            return ClassFLyTypeConfig.CLASS_FLY_RJ;
        }

        // 根据科目名称进一步判断
        if (name != null) {
            // 包含二建相关关键词的归类为二建
            if (name.contains("二建") || name.contains("建造师") || name.contains("施工管理") ||
                name.contains("工程法规") || name.contains("建筑工程") || name.contains("市政工程") ||
                name.contains("机电工程")) {
                return ClassFLyTypeConfig.CLASS_FLY_RJ;
            }
            // 包含安全员相关关键词的都归类为安全员
            if (name.contains("安全员") || name.contains("安全生产") || name.contains("道路运输") ||
                name.contains("水利水电") || name.contains("主要负责人") || name.contains("管理人员") ||
                name.contains("A证") || name.contains("B证") || name.contains("C证") || name.contains("C1证") ||
                name.contains("C2证") || name.contains("C3证")) {
                return ClassFLyTypeConfig.CLASS_FLY_AQY;
            }
        }

        // 默认情况下，非"2"开头的ID归类为安全员
        return ClassFLyTypeConfig.CLASS_FLY_AQY;
    }
}
