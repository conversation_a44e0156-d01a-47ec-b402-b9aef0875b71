package com.zizhiguanjia.model_certificate.adapter;

import android.view.View;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_utils.utils.GsonUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_certificate.R;
import com.zizhiguanjia.model_certificate.config.ClassFLyTypeConfig;
import com.zizhiguanjia.model_certificate.databinding.CerTificatebyaddressItemBinding;
import com.zizhiguanjia.model_certificate.listener.CertificateUpdateListener;
import com.zizhiguanjia.model_certificate.manager.CertificateManager;
import com.zizhiguanjia.model_certificate.model.CertificateConfigBean;
import com.zizhiguanjia.model_certificate.model.SecondConstructionBean;

import java.util.Objects;

public class CertificateAqyAdapter extends BaseAdapter<SecondConstructionBean.RecordsBean.ChildsBean> {
    private String addressId;
    private String addressName;
    private CertificateUpdateListener certificateUpdateListener;
    private CerTificatebyaddressItemBinding binding;
    private String groupId;
    private String groupName;
    private boolean isUpdate;
    /**
     * 是否使用当前的父级id字段
     */
    private boolean useParentId = false;

    public void setCertificateUpdateListener(CertificateUpdateListener certificateUpdateListener) {
        this.certificateUpdateListener = certificateUpdateListener;
    }

    public CertificateAqyAdapter(String addressId, String addressName, String groupId, String groupName, boolean isUpdate) {
        super(R.layout.cer_tificatebyaddress_item);
        this.addressId = addressId;
        this.addressName = addressName;
        this.groupId = groupId;
        this.groupName = groupName;
        this.isUpdate = isUpdate;
        LogUtils.e("看看--->>>isUpdate" + isUpdate);
    }

    @Override
    protected void bind(BaseViewHolder holder, SecondConstructionBean.RecordsBean.ChildsBean item, int position) {
        binding = holder.getBinding();
        binding.tvCertificateName.setText(item.getName());
        binding.setData(item);
        binding.tvCertificateName.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onClicks(item);
            }
        });
        String certificateId = CertificateManager.getInstance().getCurrentCitificateName();
        LogUtils.e("存储的Id" + certificateId);
        if (!isUpdate) {
            binding.tvCertificateName.setBackgroundResource(R.drawable.cer_major_child_text_bg);
        } else {
            if (StringUtils.isEmpty(certificateId)) {
                binding.tvCertificateName.setBackgroundResource(R.drawable.cer_major_child_text_bg);
            } else {
                if (certificateId.equals(String.valueOf(item.getId()))) {
                    binding.tvCertificateName.setBackgroundResource(R.drawable.certificate_mr_bg);
                } else {
                    if (item.getChilds() != null && !item.getChilds().isEmpty()) {
                        boolean have = false;
                        for (SecondConstructionBean.RecordsBean.ChildsBean child : item.getChilds()) {
                            if (Objects.equals(String.valueOf(child.getId()), certificateId)) {
                                have = true;
                                binding.tvCertificateName.setBackgroundResource(R.drawable.certificate_mr_bg);
                            }
                        }
                        if (!have) {
                            binding.tvCertificateName.setBackgroundResource(R.drawable.cer_major_child_text_bg);
                        }
                    } else {
                        binding.tvCertificateName.setBackgroundResource(R.drawable.cer_major_child_text_bg);
                    }
                }
            }
        }

    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public void setUseParentId(boolean useParentId) {
        this.useParentId = useParentId;
    }

    public void onClicks(SecondConstructionBean.RecordsBean.ChildsBean majorChildBean) {
        if (majorChildBean.getChilds() != null && !majorChildBean.getChilds().isEmpty()) {
            if (certificateUpdateListener == null) {
                return;
            }
            certificateUpdateListener.showMoreChild(groupId, groupName, majorChildBean);
            return;
        }
        CertificateConfigBean mainCertificateBean = new CertificateConfigBean();
        mainCertificateBean.setCityCode(addressId);
        mainCertificateBean.setCityName(addressName);
        mainCertificateBean.setSubjectId(majorChildBean.getId());
        mainCertificateBean.setSubjectName(majorChildBean.getName());
        mainCertificateBean.setDes(addressName + "·" + majorChildBean.getName());
        mainCertificateBean.setGroupId(useParentId ? majorChildBean.getParentId() + "" : groupId);
        mainCertificateBean.setClassflyId(getClassflyIdBySubject(majorChildBean));
        LogUtils.e("看看------>>>>" + GsonUtils.newInstance().GsonToString(mainCertificateBean));
        if (certificateUpdateListener == null) {
            return;
        }
        certificateUpdateListener.onUpdateCertificate(mainCertificateBean);
    }

    /**
     * 根据科目信息判断分类ID
     * @param majorChildBean 科目信息
     * @return 分类ID
     */
    private int getClassflyIdBySubject(SecondConstructionBean.RecordsBean.ChildsBean majorChildBean) {
        if (majorChildBean == null) {
            return ClassFLyTypeConfig.CLASS_FLY_AQY;
        }

        String idStr = String.valueOf(majorChildBean.getId());
        String name = majorChildBean.getName();

        // 根据科目ID判断：以"2"开头的是二建
        if (idStr.startsWith("2")) {
            return ClassFLyTypeConfig.CLASS_FLY_RJ;
        }

        // 根据科目名称进一步判断
        if (name != null) {
            // 包含二建相关关键词的归类为二建
            if (name.contains("二建") || name.contains("建造师") || name.contains("施工管理") ||
                name.contains("工程法规") || name.contains("建筑工程") || name.contains("市政工程") ||
                name.contains("机电工程")) {
                return ClassFLyTypeConfig.CLASS_FLY_RJ;
            }
            // 包含安全员相关关键词的都归类为安全员
            if (name.contains("安全员") || name.contains("安全生产") || name.contains("道路运输") ||
                name.contains("水利水电") || name.contains("主要负责人") || name.contains("管理人员") ||
                name.contains("A证") || name.contains("B证") || name.contains("C证") || name.contains("C1证") ||
                name.contains("C2证") || name.contains("C3证")) {
                return ClassFLyTypeConfig.CLASS_FLY_AQY;
            }
        }

        // 默认情况下，非"2"开头的ID归类为安全员
        return ClassFLyTypeConfig.CLASS_FLY_AQY;
    }
}
