package com.zizhiguanjia.lib_base.config;

public interface ExamPaperTypeConfig {
    //历年真题
    int EXAM_PAPER_LNZT = 1;
    //模拟真题
    int EXAM_PAPER_MNZT = 2;
    //章节练习
    int EXAM_PAPER_ZJLX = 3;
    //题库练习
    int EXAM_PAPER_TKLX = 4;
    //收藏
    int EXAM_PAPER_SAVE = 6;
    //错题
    int EXAM_PAPER_ERROR = 5;
    //自动组件
    int EXAM_PAPER_AUTOEXAM = 7;
    //模拟考核，后端使用的type，首页的navtype会返回10，对应就是这个type11
    int EXAM_PAPER_AUTOEXAM_MNKS = 11;
    //全部解析
    int EXAM_PAPER_ALSY = 8;
    //错题解析
    int EXAM_PAPER_ALSYERROR = 9;
    //通关精讲课答题
    int EXAM_PAPER_COURSE_DETAIL_QUESTION = 15;
    //易错100题
    int EXAM_PAPER_ESTIONS_TO_GET_WRONG = 16;
    //智能练习
    int EXAM_INTELLIGENT_PRACTICE = 21;
    //摸底测评
    int EXAM_EvaluationReport = 20;
    //高频考题
    int EXAM_PAPER_HIGH_FREQUENCY = 22;

    /**
     * 是否是考试类型
     *
     * @param type paperType
     * @return 是-true
     */
    static boolean isExamination(int type, String title) {
        return EXAM_PAPER_LNZT == type || EXAM_PAPER_MNZT == type || EXAM_PAPER_AUTOEXAM == type || EXAM_PAPER_AUTOEXAM_MNKS == type || "模拟考试".equals(title) &&
                EXAM_PAPER_AUTOEXAM == type;
    }

    /**
     * 是否是考试类型以及解析类型
     *
     * @param type paperType
     * @return 是-true
     */
    static boolean isExaminationAndAnalysis(int type, String title) {
        return isExamination(type,title) || EXAM_PAPER_ALSY == type || EXAM_PAPER_ALSYERROR == type;
    }

    /**
     * 是否是答题
     */
    static boolean isAnswerTopIc(int type, String title) {
        return EXAM_PAPER_LNZT == type || EXAM_PAPER_MNZT == type || EXAM_PAPER_AUTOEXAM_MNKS == type || EXAM_EvaluationReport == type || "模拟考试".equals(title) &&
                EXAM_PAPER_AUTOEXAM == type;
    }
}
