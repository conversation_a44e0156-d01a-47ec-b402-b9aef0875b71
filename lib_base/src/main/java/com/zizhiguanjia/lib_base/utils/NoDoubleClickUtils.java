package com.zizhiguanjia.lib_base.utils;
 
import com.wb.lib_utils.utils.log.LogUtils;
 
public class NoDoubleClickUtils {
    private final static int SPACE_TIME = 500;//2次点击的间隔时间，单位ms
 
    private static long lastClickTime;
 
    public synchronized static boolean isDoubleClick() {
        long currentTime = System.currentTimeMillis();
 
        boolean isClick;
        if (currentTime - lastClickTime > SPACE_TIME) {
            isClick = false;
 
        } else {
            isClick = true;
 
        }
        lastClickTime = currentTime;
        return isClick;
 
    }
    public synchronized static boolean isDoubleClick(int spaceTime) {
        long currentTime = System.currentTimeMillis();

        boolean isClick;
        if (currentTime - lastClickTime > spaceTime) {
            isClick = false;

        } else {
            isClick = true;

        }
        lastClickTime = currentTime;
        return isClick;

    }

}