package com.zizhiguanjia.lib_base.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * 防抖动工具类
 * 用于防止方法在短时间内被重复调用
 */
public class DebounceUtils {
    
    // 存储不同方法的上次调用时间
    private static final Map<String, Long> lastCallTimeMap = new HashMap<>();
    
    // 默认防抖动时间间隔（毫秒）
    private static final long DEFAULT_DEBOUNCE_TIME = 2000;
    
    /**
     * 检查是否应该执行方法（防抖动检查）
     * 
     * @param methodKey 方法的唯一标识符
     * @param debounceTime 防抖动时间间隔（毫秒）
     * @return true表示可以执行，false表示应该被防抖动拦截
     */
    public static boolean shouldExecute(String methodKey, long debounceTime) {
        long currentTime = System.currentTimeMillis();
        Long lastCallTime = lastCallTimeMap.get(methodKey);
        
        if (lastCallTime == null || (currentTime - lastCallTime) >= debounceTime) {
            lastCallTimeMap.put(methodKey, currentTime);
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查是否应该执行方法（使用默认防抖动时间）
     * 
     * @param methodKey 方法的唯一标识符
     * @return true表示可以执行，false表示应该被防抖动拦截
     */
    public static boolean shouldExecute(String methodKey) {
        return shouldExecute(methodKey, DEFAULT_DEBOUNCE_TIME);
    }
    
    /**
     * 清除指定方法的防抖动记录
     * 
     * @param methodKey 方法的唯一标识符
     */
    public static void clearDebounce(String methodKey) {
        lastCallTimeMap.remove(methodKey);
    }
    
    /**
     * 清除所有防抖动记录
     */
    public static void clearAllDebounce() {
        lastCallTimeMap.clear();
    }
    
    /**
     * 获取距离上次调用的时间间隔
     * 
     * @param methodKey 方法的唯一标识符
     * @return 距离上次调用的时间间隔（毫秒），如果从未调用过则返回-1
     */
    public static long getTimeSinceLastCall(String methodKey) {
        Long lastCallTime = lastCallTimeMap.get(methodKey);
        if (lastCallTime == null) {
            return -1;
        }
        return System.currentTimeMillis() - lastCallTime;
    }
}
