<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.zizhiguanjia.model_video">
    <application>
        <activity
            android:name=".ui.VideoActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            />
<!--        <activity-->
<!--            android:screenOrientation="portrait"-->
<!--            android:name=".ui.VideoActivity"-->
<!--            android:launchMode="singleTop">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->
<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--            </intent-filter>-->
<!--        </activity>-->
    </application>
</manifest>