package com.zizhiguanjia.model_video.adapter;

import android.app.Activity;
import android.content.Context;
import android.view.TextureView;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.SeekBar;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.model_video.R;
import com.zizhiguanjia.model_video.custommedia.JZMediaAliyun;
import com.zizhiguanjia.model_video.databinding.VideoItemMainBinding;
import com.zizhiguanjia.model_video.listener.JzvdStdShowListenter;
import com.zizhiguanjia.model_video.listener.OnVideoAdapterListener;
import com.zizhiguanjia.model_video.model.VideoData;
import com.zizhiguanjia.model_video.model.VideoPlayer;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import cn.jzvd.JZDataSource;
import cn.jzvd.Jzvd;

public class MainAdapter extends  VideoPlayAdapter<VideoData.ListBean>{
    private Map<String,VideoItemMainBinding> vides=new HashMap<>();
    private Activity activity;
    public MainAdapter(Activity context) {
        super(R.layout.video_item_main);
        this.activity=context;
    }
    @Override
    protected void bind(BaseViewHolder holder, VideoData.ListBean item, int position) {
        VideoItemMainBinding videoItemMainBinding;
        if(vides.containsKey(String.valueOf(item.getId()))){
            videoItemMainBinding=vides.get(String.valueOf(item.getId()));
        }else {
            videoItemMainBinding=holder.getBinding();
            vides.put(String.valueOf(item.getId()),videoItemMainBinding);
        }
        videoItemMainBinding.setModel(this);
    }

    @Override
    public void onPageSelected(int itemPosition, View itemView) {
        VideoData.ListBean listBean=getItem(itemPosition);
        playVideo(vides.get(String.valueOf(listBean.getId())),itemView.getContext(),listBean);
    }
    private void playVideo(VideoItemMainBinding videoItemMainBinding,Context context,VideoData.ListBean item) {
        videoItemMainBinding.videoplayer.initSign(String.valueOf(item.getId()));
        JZDataSource jzDataSource = new JZDataSource(item.getVideoUrl(),
                item.getTitle());
        jzDataSource.looping = true;


        videoItemMainBinding.videoplayer.setUp(jzDataSource, Jzvd.SCREEN_NORMAL, JZMediaAliyun.class);
        videoItemMainBinding.videoplayer.startVideo();
    }
}
