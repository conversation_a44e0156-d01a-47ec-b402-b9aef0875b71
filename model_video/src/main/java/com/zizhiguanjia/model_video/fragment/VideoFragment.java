package com.zizhiguanjia.model_video.fragment;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_video.R;
import com.zizhiguanjia.model_video.adapter.MainAdapter;
import com.zizhiguanjia.model_video.databinding.VideoContentLayoutBinding;
import com.zizhiguanjia.model_video.databinding.VideoLoadingLayoutBinding;
import com.zizhiguanjia.model_video.listener.OnVideoAdapterListener;
import com.zizhiguanjia.model_video.navigator.VideoNavigator;
import com.zizhiguanjia.model_video.view.VideoPlayRecyclerView;
import com.zizhiguanjia.model_video.viewmodel.VideoViewModel;

import java.util.ArrayList;
import java.util.List;

import cn.jzvd.Jzvd;

public class VideoFragment extends BaseFragment  {
    private VideoPlayRecyclerView mRvVideo;
    private MainAdapter adapter;
    @BindViewModel
    VideoViewModel videoViewModel;
    private VideoLoadingLayoutBinding videoLoadingLayoutBinding;
    @Override
    public int initLayoutResId() {
        return R.layout.video_loading_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        videoLoadingLayoutBinding=getBinding();
        videoLoadingLayoutBinding.setModel(videoViewModel);
//        videoViewModel.initParams(this);
        videoLoadingLayoutBinding.mlstvCommonListView.showContent();
        mRvVideo = findViewById(R.id.rvVideo);
        adapter = new MainAdapter(getActivity());
        mRvVideo.setAdapter(adapter);
        List<String> address=new ArrayList<>();
        address.add("1111");
        address.add("1111");
        address.add("1111");
        address.add("1111");
        address.add("1111");
//        adapter.setDataItems(address);
        videoLoadingLayoutBinding.icdeCommonContent.rvVideo.getRecyclerView().addOnChildAttachStateChangeListener(new RecyclerView.OnChildAttachStateChangeListener() {
            @Override
            public void onChildViewAttachedToWindow(@NonNull View view) {
            }

            @Override
            public void onChildViewDetachedFromWindow(@NonNull View view) {
                Jzvd jzvd = view.findViewById(R.id.videoplayer);
                if (jzvd != null && Jzvd.CURRENT_JZVD != null &&
                        jzvd.jzDataSource.containsTheUrl(Jzvd.CURRENT_JZVD.jzDataSource.getCurrentUrl())) {
                    if (Jzvd.CURRENT_JZVD != null && Jzvd.CURRENT_JZVD.screen != Jzvd.SCREEN_FULLSCREEN) {
                        Jzvd.releaseAllVideos();
                    }
                }
            }
        });
    }

    @Override
    public void initViewData() {

    }

    @Override
    public void initObservable() {

    }

    @Override
    public void onInVisible() {
    }

}
