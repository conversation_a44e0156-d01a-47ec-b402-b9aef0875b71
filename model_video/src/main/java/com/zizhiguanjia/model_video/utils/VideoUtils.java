package com.zizhiguanjia.model_video.utils;

import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_utils.encrypt.AESUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.helper.DataHelper;
import com.zizhiguanjia.lib_base.utils.TimeUtils;
import com.zizhiguanjia.model_video.model.VideoRequBean;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.multi.SetValueMap;

public class VideoUtils {
    private TreeSet<VideoRequBean> signSet = new TreeSet<>();
    private static VideoUtils videoUtils;
    private boolean end = false;

    public static VideoUtils getInstance() {
        if (videoUtils == null) {
            synchronized (VideoUtils.class) {
                return videoUtils = new VideoUtils();
            }
        }
        return videoUtils;
    }

    public void checkTime(int progress, long posituon, String video) {
        if (progress >= 99) {
            if (!end) {
                signSet.add(createVideoRequModel(100, posituon, video));
                end = true;
                timeOneEnd();
            }
        } else {
            end = false;
            if (rangeInDefined(progress, 0, 5)) {
                //0~5
                signSet.add(createVideoRequModel(0, posituon, video));
            } else if (rangeInDefined(progress, 5, 15)) {
                signSet.add(createVideoRequModel(5, posituon, video));
            } else if (rangeInDefined(progress, 15, 30)) {
                signSet.add(createVideoRequModel(15, posituon, video));
            } else if (rangeInDefined(progress, 30, 45)) {
                signSet.add(createVideoRequModel(30, posituon, video));
            } else {
                signSet.add(createVideoRequModel(45, posituon, video));
            }

        }
    }

    private VideoRequBean createVideoRequModel(int pre, long posituon, String video) {
        VideoRequBean videoRequBean = new VideoRequBean();
        videoRequBean.setProgress(pre);
        videoRequBean.setIsComplete(pre == 99 || pre == 100 ? "true" : "false");
        videoRequBean.setVideoId(video);
        videoRequBean.setViewDuration(DownTimeUtils.getInstance().getCurrentTimes());
        videoRequBean.setSign(TimeUtils.getCurrentTimeFormat() + video);
        videoRequBean.setDeadLine(posituon / 1000);
        return videoRequBean;
    }

    public boolean rangeInDefined(long current, long min, long max) {
        return Math.max(min, current) == Math.min(current, max);
    }

    public void timeOneEnd() {
        if (signSet == null || signSet.size() == 0) return;
        List<VideoRequBean> list = new ArrayList<VideoRequBean>(signSet);
        List<VideoRequBean> strs = ListUtil.sortByProperty(list, "progress");
        String requestion = GsonUtils.gsonString(strs);
        DataHelper.uploadVideoCountData(requestion);
        clearData();
    }

    public void clearData() {
        signSet.clear();
    }

}
