package com.zizhiguanjia.model_video.utils;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_utils.utils.log.LogUtils;

import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
public class DownTimeUtils {
    private static  DownTimeUtils downTimeUtils;
    private Disposable disposable;
    private long currentTimes=0;
    public static DownTimeUtils getInstance(){
        if(downTimeUtils==null){
            synchronized (DownTimeUtils.class){
                return downTimeUtils=new DownTimeUtils();
            }
        }
        return downTimeUtils;
    }
    public void pauseTime(){
        if(disposable!=null){
            disposable.dispose();
        }
    }
    public void resumTime(){
        if(disposable!=null){
            disposable.dispose();
        }
        disposable=RxJavaUtils.delay(1, new Consumer<Long>() {
            @Override
            public void accept(Long aLong) throws Exception {
                currentTimes=currentTimes+1;
            }
        });
    }

    public long getCurrentTimes() {
        return currentTimes;
    }

    public void startTime(){
        if(disposable!=null){
            disposable.dispose();
        }
        currentTimes=0;
        disposable=RxJavaUtils.polling(1, new Consumer<Long>() {
            @Override
            public void accept(Long aLong) throws Exception {
                currentTimes=currentTimes+1;
            }
        });
    }
    public void cancelTime(){
        if(disposable!=null){
            currentTimes=0;
            disposable.dispose();
        }
    }
}
