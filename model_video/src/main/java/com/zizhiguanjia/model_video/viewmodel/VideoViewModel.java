package com.zizhiguanjia.model_video.viewmodel;

import android.view.View;

import androidx.databinding.ObservableField;

import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.model_video.R;
import com.zizhiguanjia.model_video.api.VideoServiceApi;
import com.zizhiguanjia.model_video.model.VideoData;
import com.zizhiguanjia.model_video.navigator.VideoNavigator;

import java.util.HashMap;
import java.util.Map;

public class VideoViewModel extends CommonViewModel {
    private VideoServiceApi mApi = new Http().create(VideoServiceApi.class);
    private VideoNavigator videoNavigator;
    public ObservableField<Boolean> pauseObs = new ObservableField<>();

    public void initParams(VideoNavigator videoNavigator) {
        this.videoNavigator = videoNavigator;
        pauseObs.set(false);
    }

    public void onClick(View view) {
        if(view.getId()==R.id.imgClose){
            if(videoNavigator==null)return;
            videoNavigator.closeActivity();
        }
    }

    public void initVideoData(String type,String liveId) {
        Map<String, String> params = new HashMap<>();
        params.put("majorId", BaseConfig.majId);
        params.put("type",type);
        if(liveId==null||liveId.isEmpty()){
        }else{
            params.put("liveId",liveId);
        }
        launchOnlyResult(mApi.getVideoListData(params), new OnHandleException<BaseData<VideoData>>() {
            @Override
            public void success(BaseData<VideoData> data) {
                if (data.Data == null || data.Data.getList() == null || data.Data.getList().size() == 0) {
                    //暂无数据
                    if (videoNavigator == null) return;
                    videoNavigator.showEmpty();
                } else {
                    //有数据
                    videoNavigator.showContentDataView(data.Data.getList());
                }
            }

            @Override
            public void error(String msg) {
                if (videoNavigator == null) return;
                videoNavigator.showEmpty();
            }
        });
    }
}
