package com.zizhiguanjia.model_video.model;

import androidx.annotation.NonNull;

public class VideoRequBean implements Comparable<VideoRequBean>{
    private int progress;
    private long deadLine;
    private long viewDuration;
    private String Sign;
    private String IsComplete;
    private String videoId;

    public int getProgress() {
        return progress;
    }

    public void setProgress(int progress) {
        this.progress = progress;
    }

    public long getDeadLine() {
        return deadLine;
    }

    public void setDeadLine(long deadLine) {
        this.deadLine = deadLine;
    }

    public long getViewDuration() {
        return viewDuration;
    }

    public void setViewDuration(long viewDuration) {
        this.viewDuration = viewDuration;
    }

    public String getSign() {
        return Sign;
    }

    public void setSign(String sign) {
        Sign = sign;
    }

    public String getIsComplete() {
        return IsComplete;
    }

    public void setIsComplete(String isComplete) {
        IsComplete = isComplete;
    }

    public String getVideoId() {
        return videoId;
    }

    public void setVideoId(String videoId) {
        this.videoId = videoId;
    }

    @Override
    public String toString() {
        return "VideoRequBean{" +
                "progress=" + progress +
                ", deadLine=" + deadLine +
                ", viewDuration=" + viewDuration +
                '}';
    }

    @Override
    public int compareTo(VideoRequBean o) {
        return this.progress==o.progress?0:1;
    }
}
