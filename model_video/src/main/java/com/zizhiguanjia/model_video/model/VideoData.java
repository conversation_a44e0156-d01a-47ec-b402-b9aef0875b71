package com.zizhiguanjia.model_video.model;

import java.util.List;

public class VideoData {

    private List<ListBean> list;

    public List<ListBean> getList() {
        return list;
    }

    public void setList(List<ListBean> list) {
        this.list = list;
    }

    public static class ListBean {
        /**
         * Id : 0
         * CateType : 1
         * CateTip : 导学课
         * Title : 消防法
         * CoverUrl : http://localhost:52199/UploadFile/ExamVideoCover//20211223183632368.jpg
         * Description : 《中华人民共和国消防法》规定，任何单位、个人不得（ ）停用消防设施、器材，不得埋压、圈占、遮挡消火栓或者占用防火间距，不得占用、堵塞、封闭疏散通道、安全出口、消防车通道。
         * VideoId : 6fa2e1f143d9433983e6857ae262c7ab
         * VideoUrl : http://oss.zizhiguanjia.net/sv/1b65de4a-17dd7193590/1b65de4a-17dd7193590.mp4?auth_key=1640329217-34c62ec69b85475385d8c4b3556d9184-0-0c8f6433a5357d30f121eeaa963e360f
         * Timeout : 6000
         * Duration : 40
         */

        private int Id;
        private int CateType;
        private String CateTip;
        private String Title;
        private String CoverUrl;
        private String Description;
        private String VideoId;
        private String VideoUrl;
        private int Timeout;
        private int Duration;

        public int getId() {
            return Id;
        }

        public void setId(int Id) {
            this.Id = Id;
        }

        public int getCateType() {
            return CateType;
        }

        public void setCateType(int CateType) {
            this.CateType = CateType;
        }

        public String getCateTip() {
            return CateTip;
        }

        public void setCateTip(String CateTip) {
            this.CateTip = CateTip;
        }

        public String getTitle() {
            return Title;
        }

        public void setTitle(String Title) {
            this.Title = Title;
        }

        public String getCoverUrl() {
            return CoverUrl;
        }

        public void setCoverUrl(String CoverUrl) {
            this.CoverUrl = CoverUrl;
        }

        public String getDescription() {
            return Description;
        }

        public void setDescription(String Description) {
            this.Description = Description;
        }

        public String getVideoId() {
            return VideoId;
        }

        public void setVideoId(String VideoId) {
            this.VideoId = VideoId;
        }

        public String getVideoUrl() {
            return VideoUrl;
        }

        public void setVideoUrl(String VideoUrl) {
            this.VideoUrl = VideoUrl;
        }

        public int getTimeout() {
            return Timeout;
        }

        public void setTimeout(int Timeout) {
            this.Timeout = Timeout;
        }

        public int getDuration() {
            return Duration;
        }

        public void setDuration(int Duration) {
            this.Duration = Duration;
        }
    }
}
