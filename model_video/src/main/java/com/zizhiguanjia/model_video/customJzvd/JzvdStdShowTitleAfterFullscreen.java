package com.zizhiguanjia.model_video.customJzvd;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.listeners.SpeedListener;
import com.zizhiguanjia.lib_base.utils.TimeUtils;
import com.zizhiguanjia.model_video.R;
import com.zizhiguanjia.model_video.listener.JzvdStdShowListenter;
import com.zizhiguanjia.model_video.model.VideoRequBean;
import com.zizhiguanjia.model_video.utils.DownTimeUtils;
import com.zizhiguanjia.model_video.utils.VideoUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;

import cn.jzvd.JZDataSource;
import cn.jzvd.JzvdStd;
import io.reactivex.functions.Consumer;

public class JzvdStdShowTitleAfterFullscreen extends JzvdStd {
    private List<Integer> postionTime=new ArrayList<>();
    private String videoId;
    private TextView speedTv;
    private View placeholder;
    private LinearLayout layout_bottom;
    public void initSign(String videoId){
        this.videoId=videoId;
        VideoUtils.getInstance().timeOneEnd();

    }
    @Override
    public int getLayoutId() {
        return R.layout.video_jz_layout_std;   //复制DEMO下的jz_layout_std(可以重命名，但不能删除控件或修改控件的ID) 增加你想要的控件
    }

    @Override
    public void init(Context context) {
        super.init(context);
        speedTv=findViewById(R.id.speedTv);
        layout_bottom=findViewById(R.id.layout_bottom);
        placeholder=findViewById(R.id.placeholder);
        if (speedTv == null) {
            speedTv = new TextView(context);
        }
        speedTv.setOnClickListener(this);
        initSpeed();
    }
    private void initSpeed(){
        String json=KvUtils.get("videoSpeed","");
        if(json==null||json.isEmpty()){
            speedTv.setText("倍速");
        }else {
            Map<String,String> jsonMap= GsonUtils.gsonToMaps(json);
            speedTv.setText(jsonMap.get("titleSpeed"));
        }
    }
    @Override
    public void onClick(View v) {
        super.onClick(v);
        if(v.getId()==R.id.speedTv){
            onCLickUiToggleToClear();
            if(screen==SCREEN_FULLSCREEN){
                //全屏模式
                MessageHelper.openSpeedFullDialog(getContext(), layout_bottom,new SpeedListener() {
                    @Override
                    public void onSpeed(float speed,String title) {
                        mediaInterface.setSpeed(speed);
                        speedTv.setText(title);
                    }
                });
            }else {
                MessageHelper.openSpeedDialog(getContext(), speedTv, new SpeedListener() {
                    @Override
                    public void onSpeed(float speed,String title) {
                        mediaInterface.setSpeed(speed);
                        speedTv.setText(title);
                    }
                });
            }

        }
    }

    public JzvdStdShowTitleAfterFullscreen(Context context) {
        super(context);
    }
    public JzvdStdShowTitleAfterFullscreen(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public void onStatePlaying() {
        super.onStatePlaying();
        titleTextView.setVisibility(View.INVISIBLE);
        DownTimeUtils.getInstance().startTime();
    }

    @Override
    public void setUp(JZDataSource jzDataSource, int screen) {
        super.setUp(jzDataSource, screen);
        titleTextView.setVisibility(View.INVISIBLE);
    }
    @Override
    public void gotoFullscreen() {
        super.gotoFullscreen();
        titleTextView.setVisibility(View.VISIBLE);
        placeholder.setVisibility(VISIBLE);
    }


    @Override
    public void gotoNormalScreen() {
        super.gotoNormalScreen();
        titleTextView.setVisibility(View.INVISIBLE);
        placeholder.setVisibility(GONE);
    }
    @Override
    public void onProgress(int progress, long position, long duration) {
        super.onProgress(progress, position, duration);
        VideoUtils.getInstance().checkTime(progress,position,videoId);
    }

}