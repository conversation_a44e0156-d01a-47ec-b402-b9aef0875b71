package com.zizhiguanjia.model_video.api;


import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.model_video.model.VideoData;

import java.util.Map;

import io.reactivex.Observable;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

public interface VideoServiceApi {
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/api/video/GetVidesByType")
    Observable<BaseData<VideoData>> getVideoListData(@FieldMap Map<String,String> params);
}
