package com.zizhiguanjia.model_video.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.model_video.R;

@SuppressLint("ClickableViewAccessibility")
public class PlayerProgressBar extends RelativeLayout {

    private static final String TAG = "PlayerProgressBar";
    
    private static final String DEFAULT_DURATION = "00:00:00";

    private boolean isInDragMode;
    
    private int duration;
    private int curDuration;

    private float indicatorMaxWidth;

    private StringBuilder sb;
    private RelativeLayout.LayoutParams indicatorLp;
    private OnProgressChangedListener onProgressChangedListener;

    private ProgressBar pb;
    private ImageView indicator;
    private TextView spendTime;
    private TextView leftTime;


    public PlayerProgressBar(Context context, AttributeSet attrs) {
        super(context, attrs);

        sb = new StringBuilder();
        indicatorMaxWidth = -1;

        pb = new ProgressBar(context, null, android.R.attr.progressBarStyleHorizontal);
        RelativeLayout.LayoutParams rlp = new RelativeLayout.LayoutParams(-1, DpUtils.px2dp(context,7));
        rlp.topMargin = DpUtils.px2dp(context,5);
        pb.setLayoutParams(rlp);

        indicator = new ImageView(context);
        rlp = new RelativeLayout.LayoutParams(DpUtils.px2dp(context,18), DpUtils.px2dp(context,18));
        indicator.setLayoutParams(rlp);
        indicatorLp = rlp;
        indicator.setScaleType(ImageView.ScaleType.CENTER);
        indicator.setImageDrawable(context.getResources().getDrawable(R.mipmap.ic_launcher));

        spendTime = new TextView(context);
        rlp = new RelativeLayout.LayoutParams(DpUtils.px2dp(context,46), DpUtils.px2dp(context,15));
        rlp.topMargin = DpUtils.px2dp(context,20);
        spendTime.setLayoutParams(rlp);
        spendTime.setText(DEFAULT_DURATION);
        spendTime.setTextSize(11);
        spendTime.setTextColor(context.getResources().getColor(R.color.red));

        leftTime = new TextView(context);
        rlp = new RelativeLayout.LayoutParams(DpUtils.px2dp(context,46), DpUtils.px2dp(context,15));
        rlp.topMargin = DpUtils.px2dp(context,20);
        rlp.addRule(RelativeLayout.ALIGN_PARENT_END);
        leftTime.setLayoutParams(rlp);
        leftTime.setText(DEFAULT_DURATION);
        leftTime.setTextSize(11);
        leftTime.setTextColor(context.getResources().getColor(R.color.green));

        indicator.setOnTouchListener(onIndicatorTouchListener);

        addView(pb);
        addView(indicator);
        addView(spendTime);
        addView(leftTime);
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);

        if(indicatorMaxWidth == -1) {
            indicatorMaxWidth = (float)pb.getWidth() - (float)indicator.getWidth();
        }
    }

    public void setDuration(int duration){
        if(duration < 0)
            return;
        this.duration = duration;
        pb.setMax(duration / 1000);
        applyDuration();
    }
    
    public void setCurrentPos(int duration){
        if(duration <= this.duration) {
            curDuration = duration;
            applyDuration();
        }
    }
    
    private void applyDuration(){
        leftTime.post(new Runnable() {
            @Override
            public void run() {
                applyLeftDuration();
                applyCurDuration();
            }
        });
    }
    
    private void applyLeftDuration(){
        if(duration < 0){
            leftTime.setText(DEFAULT_DURATION);
        }else{
            leftTime.setText(calTime(duration - curDuration));
        }
    }
    
    private void applyCurDuration(){
        if(curDuration < 0){
            spendTime.setText(DEFAULT_DURATION);
            pb.setProgress(0);
        }else{
            spendTime.setText(calTime(curDuration));
            pb.setProgress(curDuration / 1000);
            applyIndicator();
        }
    }

    private void applyIndicator(){
        indicatorLp.leftMargin = (int)((float)curDuration / (float)duration * indicatorMaxWidth);
        requestLayout();
    }

    private String calTime(int ms){
        if(ms < 1){
            return DEFAULT_DURATION;
        }

        sb.delete(0, sb.length());
        if(ms < 60000){
            // below 1 minute
            sb.append("00:00:");
            if(ms < 10000)
                sb.append("0");
            sb.append(ms/1000);
        }else if(ms < 3600000){
            // below 1 hour
            sb.append("00:");
            int tmp = ms / 60000;
            if(tmp < 10)
                sb.append("0");
            sb.append(tmp);
            sb.append(":");
            tmp = ms % 60000;
            if(tmp < 10000)
                sb.append("0");
            sb.append(tmp / 1000);
        }else if(ms < 360000000){
            // below 100 hour
            int tmp = ms / 3600000;
            if(tmp < 10)
                sb.append("0");
            sb.append(tmp);
            sb.append(":");
            tmp = ms % 3600000;
            int tmp2 = tmp / 60000;
            if(tmp2 < 10)
                sb.append("0");
            sb.append(tmp2);
            sb.append(":");
            tmp2 = tmp % 60000;
            if(tmp2 < 10000)
                sb.append("0");
            sb.append(tmp2 / 1000);
        }else{
            sb.append("99:59:59");
        }

        return sb.toString();
    }

    public boolean isInDragMode(){
        return isInDragMode;
    }

    public int getCurDuration(){
        return curDuration;
    }

    public void setOnProgressChangedListener(OnProgressChangedListener listener){
        onProgressChangedListener = listener;
    }

    private View.OnTouchListener onIndicatorTouchListener = new OnTouchListener() {

        private boolean isInvalidEvent;

        @Override
        public boolean onTouch(View v, MotionEvent event) {
            if(v != indicator) {
                return false;
            }

            if(isInvalidEvent) {
                if(event.getAction() == MotionEvent.ACTION_UP) {
                    isInvalidEvent = false;
                    isInDragMode = false;
                }

                return true;
            }

            if(duration == 0){
                return true;
            }

            switch(event.getAction()){
                case MotionEvent.ACTION_DOWN:{
                    isInDragMode = true;
                }break;
                case MotionEvent.ACTION_UP:{
                    if(onProgressChangedListener != null) {
                        onProgressChangedListener.onProgressChanged(duration, curDuration);
                    }
                    isInDragMode = false;
                    isInvalidEvent = false;
                }break;
                case MotionEvent.ACTION_MOVE:{
                    if(event.getY() < -40 || event.getY() > 130){
                        //Exit the drag mode.
                        isInvalidEvent = true;
                        isInDragMode = false;

                        refreshView(event);
                        if(onProgressChangedListener != null) {
                            onProgressChangedListener.onProgressChanged(duration, curDuration);
                        }
                    }else{
                        refreshView(event);
                    }
                }break;
            }

            return true;
        }

        private void refreshView(MotionEvent event){
            indicatorLp.leftMargin += (int)event.getX();
            curDuration = (int) ((float)indicatorLp.leftMargin / indicatorMaxWidth * duration);
            applyDuration();
            requestLayout();
        }
    };

    public interface OnProgressChangedListener {
        void onProgressChanged(int total, int current);
    }
}