package com.zizhiguanjia.model_video.service;

import android.content.Context;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.example.lib_common.arouter.ARouterUtils;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.VideoRouterPath;
import com.zizhiguanjia.lib_base.service.VideoService;

@Route(path = VideoRouterPath.SERVICE)
public class VideoServiceImpl implements VideoService {
    @Override
    public void start(Context context) {
    }

    @Override
    public IFragment mainPage(Context context) {
        return null;
    }

    @Override
    public IFragment showPage(Context context) {
        return null;
    }

    @Override
    public void init(Context context) {

    }

    @Override
    public void showVideoActivity(String type) {
        ARouter.getInstance().build(VideoRouterPath.MAIN_ACTIVITY).withString("type",type).navigation();
    }

    @Override
    public void showVideoActivity(String type, String liveId) {
        ARouter.getInstance().build(VideoRouterPath.MAIN_ACTIVITY).withString("type",type).withString("liveId",liveId).navigation();
    }
}
