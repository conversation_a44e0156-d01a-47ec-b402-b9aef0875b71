package com.zizhiguanjia.model_video.ui;

import android.os.Bundle;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.zizhiguanjia.lib_base.base.BaseActivity;
import com.zizhiguanjia.lib_base.base.ContainerActivity;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.constants.VideoRouterPath;
import com.zizhiguanjia.model_video.R;
import com.zizhiguanjia.model_video.adapter.MainAdapter;
import com.zizhiguanjia.model_video.databinding.VideoLoadingLayoutBinding;
import com.zizhiguanjia.model_video.model.VideoData;
import com.zizhiguanjia.model_video.navigator.VideoNavigator;
import com.zizhiguanjia.model_video.utils.VideoUtils;
import com.zizhiguanjia.model_video.view.VideoPlayRecyclerView;
import com.zizhiguanjia.model_video.view.VideoToast;
import com.zizhiguanjia.model_video.viewmodel.VideoViewModel;

import java.util.List;

import cn.jzvd.Jzvd;

@Route(path = VideoRouterPath.MAIN_ACTIVITY)
public class VideoActivity extends BaseActivity implements VideoNavigator {
    private VideoLoadingLayoutBinding videoLoadingLayoutBinding;
    private VideoPlayRecyclerView mRvVideo;
    private MainAdapter adapter;
    @BindViewModel
    VideoViewModel videoViewModel;
    @Autowired(name = "type")
    public String type;
    @Autowired(name = "liveId")
    public String liveId;
    @Override
    public void initView(Bundle savedInstanceState) {
        ARouter.getInstance().inject(this);
        videoLoadingLayoutBinding = DataBindingUtil.setContentView(this, R.layout.video_loading_layout);
        videoLoadingLayoutBinding.setModel(videoViewModel);
        videoViewModel.initParams(this);
        videoLoadingLayoutBinding.mlstvCommonListView.showLoading();
        mRvVideo = findViewById(R.id.rvVideo);
        adapter = new MainAdapter(getActivity());
        mRvVideo.setAdapter(adapter);
        videoViewModel.initVideoData(type,liveId);
        KvUtils.save("videoSpeed","");
    }
    @Override
    public void onBackPressed() {
        if (Jzvd.backPress()) {
            return;
        }
        VideoUtils.getInstance().timeOneEnd();
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        KvUtils.save("videoSpeed","");
        Jzvd.releaseAllVideos();
    }

    @Override
    protected void onPause() {
        super.onPause();
        Jzvd.goOnPlayOnPause();
    }

    @Override
    protected void onResume() {
        super.onResume();
        Jzvd.goOnPlayOnResume();
    }

    @Override
    public void showEmpty() {
        videoLoadingLayoutBinding.mlstvCommonListView.showEmpty();
    }

    @Override
    public void showContentDataView(List<VideoData.ListBean> listBeans) {
        if(type.equals("3")){
        }else{
            VideoToast.normal("已为您更新"+listBeans.size()+"条视频", Toast.LENGTH_LONG,VideoToast.getDrawable(getContext(),R.drawable.video_next));
        }
        videoLoadingLayoutBinding.mlstvCommonListView.showContent();
        adapter.setDataItems(listBeans);
    }

    @Override
    public void closeActivity() {
        finish();
    }

}
