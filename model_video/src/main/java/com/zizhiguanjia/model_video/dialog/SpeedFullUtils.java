package com.zizhiguanjia.model_video.dialog;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.zizhiguanjia.model_video.R;

import cn.jzvd.JZUtils;
import cn.jzvd.JzvdStd;

public class SpeedFullUtils {
    private static SpeedFullUtils speedFullUtils;
    public PopupWindow clarityPopWindow;
    public static SpeedFullUtils getInstance(){
        if(speedFullUtils==null){
            synchronized (SpeedFullUtils.class){
                return speedFullUtils=new SpeedFullUtils();
            }
        }
        return speedFullUtils;
    }
//    public void openFullSpeed(Context jzvdContext, JzvdStd jzvdStd,View textureViewContainer){
//        LayoutInflater inflater = (LayoutInflater) jzvdContext
//                .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
//        final LinearLayout layout = (LinearLayout) inflater.inflate(R.layout.jz_layout_clarity, null);
//        View.OnClickListener mQualityListener = v1 -> {
//            int index = (int) v1.getTag();
//            for (int j = 0; j < layout.getChildCount(); j++) {//设置点击之后的颜色
////                if (j == jzDataSource.currentUrlIndex) {
////                    ((TextView) layout.getChildAt(j)).setTextColor(Color.parseColor("#fff85959"));
////                } else {
////                    ((TextView) layout.getChildAt(j)).setTextColor(Color.parseColor("#ffffff"));
////                }
//            }
//            if (clarityPopWindow != null) {
//                clarityPopWindow.dismiss();
//            }
//        };
//
//        for (int j = 0; j < 5; j++) {
//            String key = jzDataSource.getKeyFromDataSource(j);
//            TextView clarityItem = (TextView) View.inflate(jzvdContext, R.layout.jz_layout_clarity_item, null);
//            clarityItem.setText(key);
//            clarityItem.setTag(j);
//            layout.addView(clarityItem, j);
//            clarityItem.setOnClickListener(mQualityListener);
////            if (j == jzDataSource.currentUrlIndex) {
////                clarityItem.setTextColor(Color.parseColor("#fff85959"));
////            }
//        }
////
//        clarityPopWindow = new PopupWindow(layout, JZUtils.dip2px(jzvdContext, 240), FrameLayout.LayoutParams.MATCH_PARENT, true);
//        clarityPopWindow.setContentView(layout);
//        clarityPopWindow.setAnimationStyle(R.style.pop_animation);
//        clarityPopWindow.showAtLocation(textureViewContainer, Gravity.END, 0, 0);
//    }
}
