ext.mainApp = false
apply from: rootProject.file('module.gradle')
android {
    defaultConfig {
        //仅在以application方式编译时才添加applicationId属性
        if (runAsApp) {
            applicationId build_version.applicationId+'.model_message'
        }
    }
    //统一规范资源名称前缀，防止多个 module 之间资源冲突
    resourcePrefix "message_"
}
dependencies {
    implementation 'com.google.zxing:core:3.3.0'
    implementation libs.timePicer
}