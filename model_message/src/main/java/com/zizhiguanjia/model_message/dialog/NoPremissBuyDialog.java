package com.zizhiguanjia.model_message.dialog;

import android.animation.ObjectAnimator;
import android.app.Activity;
import android.content.Context;
import android.graphics.Paint;
import android.text.TextPaint;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.wb.lib_network.utils.GsonUtils;
import com.wb.lib_pop.code.BottomPopupView;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_rxtools.task.RxAsyncTask;
import com.wb.lib_utils.AppUtils;
import com.wb.lib_utils.utils.FileUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.config.BaseConfig;
import com.zizhiguanjia.lib_base.helper.PayHelper;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.adapter.NoPresmissBuyAdapter;
import com.zizhiguanjia.model_message.bean.NoPremissBuyBean;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public class NoPremissBuyDialog extends BottomPopupView implements View.OnClickListener{
    private ImageView imgClose,imgUpVip;
    private Activity mActivity;
    private TextView pricesTv,timesTv,edTv,tvHint;
    private ImageView imgGuild;
    private boolean ed;
    private long time;
    private String payRouthParams;
    private RecyclerView mRecyclerView;
    private NoPresmissBuyAdapter mAdapter;
    private String mPrice,mTimes,mGoodsId;
    private int goodType;
    private int majorType;
    public NoPremissBuyDialog(@NonNull Context context, Activity activity, boolean ised, String payRouthParams,String price, String times, String GoodsId,int goodType,
            int majorType) {
        super(context);
        this.ed=ised;
        this.payRouthParams=payRouthParams;
        this.mActivity=activity;
        this.mPrice=price;
        this.mTimes=times;
        this.mGoodsId=GoodsId;
        this.goodType=goodType;
        this.majorType=majorType;
        addInnerContent();
    }
    @Override
    protected int initLayoutResId() {
        return R.layout.message_nopremiss_layout;
    }
    @Override
    protected void initPopupContent() {
        super.initPopupContent();
        BaseConfig.payToast=true;
        imgGuild=this.findViewById(R.id.imgGuild);
        imgClose=this.findViewById(R.id.imgClose);
        imgUpVip=this.findViewById(R.id.imgUpVip);
        timesTv=this.findViewById(R.id.timesTv);
        pricesTv=this.findViewById(R.id.pricesTv);
        edTv=this.findViewById(R.id.edTv);
        mRecyclerView=this.findViewById(R.id.premissRvs);
        tvHint=this.findViewById(R.id.tv_hint);
        removeLeft1();
        if(!ed){
            edTv.setText("试用额度已用完");
        }else {
            edTv.setText("VIP权益");
        }
        //仅仅安全员显示这个模块
        if(majorType == 6){
            tvHint.setVisibility(VISIBLE);
        }else {
            tvHint.setVisibility(GONE);
        }
        TextPaint paint = edTv.getPaint();
        paint.setStrokeWidth(1.0f);
        paint.setStyle(Paint.Style.FILL_AND_STROKE);

        mRecyclerView.setLayoutManager(new GridLayoutManager(getContext(),4));
        mAdapter=new NoPresmissBuyAdapter();
        mRecyclerView.setAdapter(mAdapter);
        imgClose.setOnClickListener(this);
        imgUpVip.setOnClickListener(this);
//        PayHelper.getPayInfoByMajId(BaseConfig.majId, new PayInfoListener() {
//            @Override
//            public void onPayInfo(boolean state, String price, String times, String GoodsId,String json1) {
//                LogUtils.e("-----"+state+price+times+GoodsId);
//                goodsId=GoodsId;
//                MainThreadUtils.post(new Runnable() {
//                    @Override
//                    public void run() {
//                        String prices;
//                        try {
//                            if (Integer.parseInt(price) < 99) {
//                                prices=(String.valueOf(Double.parseDouble(price) / 100) );
//                            } else {
//                                prices=(String.valueOf(Integer.parseInt(price) / 100));
//                            }
//                        }catch (Exception e){
//                            prices="0.00";
//                        }
//                        pricesTv.setText(prices);
//                        timesTv.setText(times+"有效期");
//                    }
//                });
//            }
//        });
        String prices;
        try {
            if (Integer.parseInt(mPrice) < 99) {
                prices=(String.valueOf(Double.parseDouble(mPrice) / 100) );
            } else {
                prices=(String.valueOf(Integer.parseInt(mPrice) / 100));
            }
        }catch (Exception e){
            prices="0.00";
        }
        pricesTv.setText(prices);
        timesTv.setText(mTimes+"有效期");
        initConfigData();
    }
    @Override
    public void onClick(View v) {
        LogUtils.e("点击了");
        if(v.getId()==R.id.imgUpVip){
            if(StringUtils.isEmpty(mGoodsId))return;
            PayHelper.payOrder(mGoodsId,mActivity,payRouthParams);
            BaseConfig.payToast=false;
            this.dismiss();
        }else if(v.getId()==R.id.imgClose){
            BaseConfig.payToast=false;
            this.dismiss();
        }
    }
    private void initConfigData(){
        String jsonDes="";
        if(majorType == 6){
            jsonDes = "permission_config_major_type_jianan.json";
        }else{
            jsonDes = "permission_config_major_type_no_jianan.json";
        }

        RxJavaUtils.executeAsyncTask(new RxAsyncTask<String, List<NoPremissBuyBean>>(jsonDes) {
            @Override
            public void doInUIThread(List<NoPremissBuyBean> userListDataConfigBeans) {
                mAdapter.setDataItems(userListDataConfigBeans);
            }
            @Override
            public List<NoPremissBuyBean> doInIOThread(String s) {
                try {
                    String json = FileUtils.getFromAssets(AppUtils.getApp(), s);
                    List<NoPremissBuyBean> userCertificateBeans = GsonUtils.jsonToList(json, NoPremissBuyBean.class);
                    return userCertificateBeans;
                } catch (Exception e) {
                    return null;
                }
            }
        });
    }
    private void removeLeft1() {
        ObjectAnimator animator = ObjectAnimator.ofFloat(imgGuild, "translationX", 0,6);
        animator.setDuration(500);
        animator.setRepeatCount(-1);
        animator.setRepeatMode(ObjectAnimator.REVERSE);
        animator.start();

        ObjectAnimator animator1 = ObjectAnimator.ofFloat(imgGuild, "translationY",   6,12);
        animator1.setDuration(500);
        animator1.setRepeatCount(-1);
        animator1.setRepeatMode(ObjectAnimator.REVERSE);
        animator1.start();
    }

    @Override
    protected void doAfterShow() {
        time=System.currentTimeMillis();
        super.doAfterShow();
    }

    @Override
    public void dismiss() {
        BaseConfig.dialogTime=System.currentTimeMillis()-time;
        super.dismiss();
    }

}
