package com.zizhiguanjia.model_message.adapter;

import android.graphics.Paint;
import android.text.TextPaint;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.zizhiguanjia.model_message.R;
import com.zizhiguanjia.model_message.bean.NoPremissBuyBean;
import com.zizhiguanjia.model_message.databinding.MessageNopremissbuyItemBinding;

public class NoPresmissBuyAdapter extends BaseAdapter<NoPremissBuyBean> {
    private MessageNopremissbuyItemBinding messageNopremissbuyItemBinding;

    public NoPresmissBuyAdapter() {
        super(R.layout.message_nopremissbuy_item);
    }

    @Override
    protected void bind(BaseViewHolder holder, NoPremissBuyBean item, int position) {
        messageNopremissbuyItemBinding = holder.getBinding();
        messageNopremissbuyItemBinding.setData(item);
        if (item.getResId() == 1) {
            messageNopremissbuyItemBinding.messageDesImg.setImageResource(R.drawable.message_book_1);
        } else if (item.getResId() == 2) {
            messageNopremissbuyItemBinding.messageDesImg.setImageResource(item.getTitle().equals("高频考题") ? R.drawable.message_book : R.drawable.meaasge_zbkc);
        } else if (item.getResId() == 3) {
            messageNopremissbuyItemBinding.messageDesImg.setImageResource(R.drawable.message_100);
        } else if (item.getResId() == 4) {
            messageNopremissbuyItemBinding.messageDesImg.setImageResource(R.drawable.message_ai);
        }
        TextPaint paint = messageNopremissbuyItemBinding.titleTv.getPaint();
        paint.setStrokeWidth(1.0f);
        paint.setStyle(Paint.Style.FILL_AND_STROKE);
    }
}
