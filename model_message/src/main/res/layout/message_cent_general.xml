<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/_popup_round4_bg"
    android:orientation="vertical">
    <LinearLayout
        android:layout_above="@+id/xpopup_divider"
        android:gravity="center"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:text="dadada"
            android:paddingLeft="18dp"
            android:paddingRight="18dp"
            android:id="@+id/tv_title"
            android:textSize="18sp"
            android:textColor="#333333"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal" />

        <TextView
            android:layout_marginBottom="16dp"
            android:layout_marginTop="10.5dp"
            android:id="@+id/tv_content"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:textColor="#999999"
            android:textSize="14sp"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="dasdasdasda" />

        <EditText
            android:id="@+id/et_input"
            android:visibility="gone"
            android:textSize="16sp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="10dp"
            android:layout_marginTop="10dp"
            android:singleLine="true"
            android:textColor="@color/_popup_content_color"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </LinearLayout>

    <View
        android:layout_above="@+id/llBottom"
        android:layout_width="match_parent"
        android:id="@+id/xpopup_divider"
        android:background="#C5C5C5"
        android:layout_height="1dp"/>
    <LinearLayout
        android:id="@+id/llBottom"
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="50.5dp"
        android:gravity="right"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_cancel"
            android:textSize="16sp"
            android:text="@string/xpopup_cancel"
            android:background="?android:attr/selectableItemBackground"
            android:gravity="center"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="match_parent" />

        <View
            android:layout_width="1dp"
            android:id="@+id/xpopup_divider_h"
            android:background="#C5C5C5"
            android:layout_height="match_parent"/>

        <TextView
            android:textColor="#3163F6"
            android:textSize="16sp"
            android:id="@+id/tv_confirm"
            android:text="@string/xpopup_ok"
            android:gravity="center"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:background="?android:attr/selectableItemBackground"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="match_parent" />
    </LinearLayout>
</RelativeLayout>