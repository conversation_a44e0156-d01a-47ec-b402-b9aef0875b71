<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:gravity="center"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="108dp"
        android:text="恭喜您"
        android:textColor="#007AFF"
        android:textSize="17.5sp" />
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="本次模考分数超过"
            android:textColor="#333333"
            android:textSize="17.5sp" />

        <TextView
            android:id="@+id/tvpass"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="XX"
            android:textColor="#007AFF"
            android:textSize="17.5sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="的学员"
            android:textColor="#333333"
            android:textSize="17.5sp" />
    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="邀请您参与用户体验度调研"
        android:textColor="#676767"
        android:textSize="14sp" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="提交后免费送您"
            android:textColor="#676767"
            android:textSize="14sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="安全员资料大礼包"
            android:textColor="#007AFF"
            android:textSize="14sp" />
    </LinearLayout>
</LinearLayout>