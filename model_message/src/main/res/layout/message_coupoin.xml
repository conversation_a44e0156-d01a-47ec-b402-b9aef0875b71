<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:background="@color/transparent"
    android:layout_height="match_parent">
    <ImageView
        android:id="@+id/imgBack"
        android:layout_marginBottom="11.5dp"
        android:layout_marginRight="57dp"
        android:layout_alignParentRight="true"
        android:layout_above="@+id/message_coupion_img"
        android:src="@drawable/message_close_img"
        android:layout_width="24dp"
        android:layout_height="24dp"/>

    <RelativeLayout
        android:id="@+id/message_coupion_img"
        android:layout_width="300dp"
        android:layout_height="281dp"
        android:background="@drawable/message_coupion"
        android:layout_centerInParent="true"
        >
        <TextView
            android:id="@+id/lingTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="85dp"
            android:background="@drawable/message_coupion_ling_bg"
            android:paddingLeft="14.5dp"
            android:paddingTop="4dp"
            android:paddingRight="12dp"
            android:paddingBottom="4dp"
            android:text="立即支付"
            android:textColor="@color/white"
            android:textSize="12.5dp" />
    </RelativeLayout>
<!--    <ImageView-->
<!--        android:layout_centerInParent="true"-->
<!--        android:id="@+id/message_coupion_img"-->
<!--        android:src="@drawable/message_coupion"-->
<!--        android:layout_width="286dp"-->
<!--        android:layout_height="267dp"/>-->
</RelativeLayout>