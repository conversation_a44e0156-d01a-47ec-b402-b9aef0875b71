<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/message_premissbuy_two_bg"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <RelativeLayout
        android:layout_marginTop="25dp"
        android:paddingRight="10.5dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/edTv"
            android:layout_centerHorizontal="true"
            android:text="试用额度已用完，升级题库畅快刷"
            android:textColor="#66240B"
            android:textSize="21sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <ImageView
            android:id="@+id/imgClose"
            android:layout_width="16dp"
            android:layout_height="17dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="22dp"
            android:src="@drawable/message_exam_faceback_close" />
    </RelativeLayout>
    <TextView
        android:layout_marginTop="1.5dp"
        android:text="升级题库，四步轻松备考"
        android:textSize="18sp"
        android:textColor="#66240B"
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
    <LinearLayout
        android:orientation="vertical"
        android:layout_marginTop="28dp"
        android:paddingRight="7dp"
        android:paddingLeft="19dp"
        android:gravity="bottom|center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <androidx.recyclerview.widget.RecyclerView
            app:spanCount="4"
            android:id="@+id/premissRvs"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
        <TextView
            android:id="@+id/tv_hint"
            android:layout_marginTop="10dp"
            android:text="考不过，全额退费"
            android:textColor="#000000"
            android:textSize="15sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>
    <RelativeLayout
        android:layout_marginTop="35dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <ImageView
                android:scaleType="fitXY"
                android:id="@+id/imgUpVip"
                android:layout_marginLeft="24.5dp"
                android:layout_marginRight="24.5dp"
                android:src="@drawable/message_exam_vip_bt_img"
                android:layout_width="wrap_content"
                android:layout_height="56dp"/>
            <LinearLayout
                android:gravity="center"
                android:layout_centerVertical="true"
                android:layout_marginLeft="56dp"
                android:orientation="vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <TextView
                        android:textSize="13sp"
                        android:textColor="#FFE7DB"
                        android:text="￥"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                    <TextView
                        android:id="@+id/pricesTv"
                        android:textSize="21sp"
                        android:textColor="#FFE7DB"
                        android:text="300.0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                </LinearLayout>
                <TextView
                    android:alpha="0.67"
                    android:id="@+id/timesTv"
                    android:textSize="11sp"
                    android:textColor="#FFE8DB"
                    android:text="-年有效期"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>

        </RelativeLayout>
        <ImageView
            android:id="@+id/imgGuild"
            android:scaleType="fitCenter"
            android:layout_marginTop="15dp"
            android:layout_alignParentRight="true"
            android:src="@drawable/message_exam_vip_ss_img"
            android:layout_width="59dp"
            android:layout_height="79dp"/>
    </RelativeLayout>
</LinearLayout>