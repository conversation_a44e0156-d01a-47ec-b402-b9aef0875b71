<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:background="@drawable/home_off_kf"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/closeOffImg"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignParentRight="true"
            android:layout_marginTop="11dp"
            android:layout_marginRight="9dp"
            android:src="@drawable/message_close" />
    </RelativeLayout>

    <TextView
        android:id="@+id/titleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="145dp"
        android:layout_marginBottom="13dp"
        android:text="小安老师不在线～"
        android:textColor="#1F3C64"
        android:textSize="20sp"
        android:textStyle="bold" />

    <TextView
        android:layout_below="@+id/titleTv"
        android:lineSpacingExtra="5dp"
        android:gravity="center"
        android:id="@+id/messgaeTv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="48dp"
        android:layout_marginRight="48dp"
        android:textSize="13sp" />

    <TextView
        android:id="@+id/onOnlieTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/messgaeTv"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="31dp"
        android:background="@drawable/message_offonline_bt_bg"
        android:paddingLeft="76.5dp"
        android:paddingTop="9dp"
        android:paddingRight="76.5dp"
        android:paddingBottom="9dp"
        android:text="在线客服"
        android:textColor="@color/white"
        android:textSize="16.5sp" />
</RelativeLayout>