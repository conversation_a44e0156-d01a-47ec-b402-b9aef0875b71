<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:background="@drawable/message_timechoice_bg"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:id="@+id/top1"
        android:orientation="vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <RelativeLayout
            android:layout_marginRight="16dp"
            android:layout_marginTop="15dp"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="请选择相应城市？"
                android:textColor="#000000"
                android:textSize="18sp" />

            <ImageView
                android:id="@+id/messageClose"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_alignParentRight="true"
                android:src="@drawable/message_time_close" />
        </RelativeLayout>
        <View
            android:layout_marginBottom="22dp"
            android:layout_marginTop="15dp"
            android:background="#C7C6C8"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"/>
        <TextView
            android:visibility="gone"
            android:gravity="center"
            android:textSize="16sp"
            android:textColor="#868BA1"
            android:text="向您推荐更高效的学习方案备份"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </LinearLayout>
    <LinearLayout
        android:layout_below="@+id/top1"
        android:layout_above="@+id/bottom1"
        android:layout_marginRight="5dp"
        android:layout_marginLeft="5dp"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <com.zizhiguanjia.model_message.view.WheelView
            xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/messageWheelCity"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="match_parent"/>

    </LinearLayout>

    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/bottom1"
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <View
            android:background="#C7C6C8"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"/>
        <TextView
            android:id="@+id/messageBt"
            android:paddingRight="20dp"
            android:paddingLeft="20dp"
            android:layout_marginBottom="13dp"
            android:layout_marginTop="13dp"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:text="确定"
            android:textColor="#3163F6"
            android:textSize="16sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>
</RelativeLayout>