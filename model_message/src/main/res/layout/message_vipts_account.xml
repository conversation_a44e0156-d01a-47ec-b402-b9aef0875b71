<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <TextView
        android:gravity="center"
        android:textStyle="bold"
        android:textSize="18sp"
        android:textColor="@color/white"
        android:text="温馨提示"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:background="@drawable/message_logoff_top_bg" />
    <RelativeLayout
        android:background="@drawable/message_logoff_center_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/vContextTop"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/llBottom"
            android:layout_marginLeft="23dp"
            android:layout_marginTop="29dp"
            android:layout_marginRight="23dp"
            android:background="@color/white"
            android:lineSpacingExtra="8dp"
            android:textColor="#000000"
            android:textSize="14sp" />
        <LinearLayout
            android:layout_marginBottom="25dp"
            android:id="@+id/llBottom"
            android:layout_alignParentBottom="true"
            android:gravity="center"
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/tvVipLogin"
                android:paddingLeft="35.5dp"
                android:paddingRight="35.5dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#FFFEFF"
                android:textSize="16sp"
                android:text="用VIP手机号重新登录"
                android:background="@drawable/message_logoff_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/tvOldLogin"
                android:layout_marginTop="9.5dp"
                android:textColor="#999999"
                android:textSize="13sp"
                android:text="继续使用非VIP手机号登录>"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </RelativeLayout>


</LinearLayout>