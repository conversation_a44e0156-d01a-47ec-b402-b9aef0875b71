<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="bean"
            type="com.zizhiguanjia.model_message.bean.SpeedBean" />
        <variable
            name="model"
            type="com.zizhiguanjia.model_message.adapter.SpeedAdapter" />
    </data>
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:paddingBottom="14dp"
            android:id="@+id/tvTitle"
            android:onClick="@{(view)->model.onClick(view,bean.speed,bean.ids)}"
            android:textColor="#666666"
            android:paddingTop="17dp"
            android:gravity="center"
            android:textSize="15sp"
            android:text="@{bean.title}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
        <View
            android:id="@+id/lineView"
            android:background="#EAEBEC"
            android:layout_width="match_parent"
            android:layout_height="0.25dp"/>
    </LinearLayout>
</layout>