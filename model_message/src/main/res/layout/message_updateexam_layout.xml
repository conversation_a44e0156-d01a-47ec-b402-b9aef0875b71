<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:background="@color/transparent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageView
            android:layout_marginTop="10dp"
            android:src="@drawable/message_update_exam_top_bg"
            android:layout_width="match_parent"
            android:layout_height="104dp"/>
        <ImageView
            android:layout_centerHorizontal="true"
            android:src="@drawable/message_updata_exam_top_bg1"
            android:layout_width="186dp"
            android:layout_height="98dp"/>
        <ImageView
            android:visibility="gone"
            android:id="@+id/updataCloseImg"
            android:layout_marginTop="33.5dp"
            android:layout_marginLeft="14dp"
            android:src="@drawable/message_bs_icon_close"
            android:layout_width="13dp"
            android:layout_height="13dp"/>
    </RelativeLayout>
    <LinearLayout
        android:paddingTop="17dp"
        android:paddingBottom="24.5dp"
        android:background="@drawable/message_exam_update_bg"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:gravity="center"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:layout_marginLeft="50dp"
                android:layout_marginRight="50dp"
                android:id="@+id/updataTitleTv"
                android:text="题库更新"
                android:textColor="#000000"
                android:textSize="17sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/updataSubTitleTv"
                android:layout_marginTop="2dp"
                android:text="题库更新"
                android:textColor="#7E7E7E"
                android:textSize="14sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
        <View
            android:layout_marginBottom="14.5dp"
            android:layout_marginRight="27.5dp"
            android:layout_marginLeft="27.5dp"
            android:layout_marginTop="9dp"
            android:background="#2E979797"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"/>
        <TextView
            android:id="@+id/updataConTitleTv"
            android:layout_marginLeft="27dp"
            android:layout_marginRight="27dp"
            android:layout_marginTop="2dp"
            android:text="3月17日题库更新"
            android:textColor="#333333"
            android:textSize="12sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:id="@+id/updataContentTitleTv"
            android:layout_marginLeft="27dp"
            android:layout_marginRight="27dp"
            android:layout_marginTop="5dp"
            android:text="优化题目及解析"
            android:textColor="#666666"
            android:textSize="12sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:id="@+id/updataSubContentTitleTv"
            android:layout_marginLeft="27dp"
            android:layout_marginRight="27dp"
            android:layout_marginTop="5dp"
            android:text="更新题库可能会影响您的部分做题记录更新题库可能会影响您的部分做题记录更新题库可能会影响您的部分做题记录更新题库可能会影响您的部分做题记录"
            android:textColor="#666666"
            android:textSize="12sp"
            android:lineSpacingExtra="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:id="@+id/updatePostTv"
            android:layout_marginTop="26.5dp"
            android:layout_marginLeft="25dp"
            android:layout_marginRight="25dp"
            android:paddingBottom="8dp"
            android:paddingTop="8.5dp"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:gravity="center"
            android:text="立即更新"
            android:background="@drawable/message_updata_bt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </LinearLayout>


</LinearLayout>