<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:gravity="center"
        android:textStyle="bold"
        android:textSize="18sp"
        android:textColor="@color/white"
        android:text="系统提示"
        android:id="@+id/vTop"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:background="@drawable/message_logoff_top_bg" />
    <RelativeLayout
        android:background="@drawable/message_logoff_center_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tvMsg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/llBottom"
            android:layout_marginLeft="23dp"
            android:layout_marginTop="29dp"
            android:layout_marginRight="23dp"
            android:layout_marginBottom="25dp"
            android:background="@color/white"
            android:lineSpacingExtra="8dp"
            android:textColor="#000000"
            android:textSize="14sp" />
        <LinearLayout
            android:layout_marginBottom="25dp"
            android:id="@+id/llBottom"
            android:layout_alignParentBottom="true"
            android:gravity="center"
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/tvLook"
                android:paddingLeft="44dp"
                android:paddingRight="44dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="#FFFEFF"
                android:textSize="16sp"
                android:text="我知道了"
                android:background="@drawable/message_logoff_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </RelativeLayout>


</LinearLayout>