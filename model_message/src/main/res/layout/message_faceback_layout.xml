<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="感谢您的参与"
        android:textColor="#0E152B"
        android:textSize="18sp" />
    <TextView
        android:layout_marginTop="13.5dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="安全员资料已发放，可以联系客服下载"
        android:textColor="#007AFF"
        android:textSize="14sp" />
    <EditText
        android:textColor="#333333"
        android:id="@+id/etInputBack"
        android:padding="5dp"
        android:gravity="top"
        android:textSize="14sp"
        android:hint="很遗憾给您带来不好的体验，请描述您遇到的问题，我们会尽快为您解决。"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_marginTop="22dp"
        android:background="@drawable/message_faceback_bg"
        android:layout_width="match_parent"
        android:layout_height="88dp"/>
</LinearLayout>