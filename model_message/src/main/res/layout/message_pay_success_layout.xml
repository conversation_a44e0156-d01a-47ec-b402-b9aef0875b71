<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:gravity="center"
    android:layout_height="match_parent">

   <RelativeLayout
       android:layout_width="287dp"
       android:layout_height="wrap_content">
       <ImageView
           android:id="@+id/messageCloseImg"
           android:layout_alignParentRight="true"
           android:src="@drawable/message_close_img"
           android:layout_width="24.5dp"
           android:layout_height="24.5dp"/>
       <RelativeLayout
           android:layout_below="@+id/messageCloseImg"
           android:background="@drawable/message_teacher_img"
           android:layout_width="287dp"
           android:layout_height="423dp">
           <RelativeLayout
               android:id="@+id/messageTop"
               android:layout_marginTop="158dp"
               android:orientation="horizontal"
               android:layout_width="match_parent"
               android:layout_height="wrap_content">
               <ImageView
                   android:scaleType="fitXY"
                   android:layout_marginLeft="43dp"
                   android:layout_centerVertical="true"
                   android:layout_toLeftOf="@+id/messageCentTxt"
                   android:src="@drawable/message_left_line_img"
                   android:layout_width="match_parent"
                   android:layout_height="0.5dp"/>
               <TextView
                   android:layout_marginRight="8dp"
                   android:layout_marginLeft="8dp"
                   android:layout_centerHorizontal="true"
                   android:id="@+id/messageCentTxt"
                   android:textSize="13sp"
                   android:textColor="#000000"
                   android:text="和老师一起搭伴学习"
                   android:layout_width="wrap_content"
                   android:layout_height="wrap_content"/>
               <ImageView
                   android:scaleType="fitXY"
                   android:layout_marginRight="43.5dp"
                   android:layout_centerVertical="true"
                   android:layout_toRightOf="@+id/messageCentTxt"
                   android:src="@drawable/message_right_line_img"
                   android:layout_width="match_parent"
                   android:layout_height="0.5dp"/>
           </RelativeLayout>
           <ImageView
               android:layout_marginTop="12dp"
               android:layout_centerHorizontal="true"
               android:layout_below="@+id/messageTop"
               android:id="@+id/messageQrCodeImage"
               android:layout_width="153dp"
               android:layout_height="156dp"/>

           <TextView
               android:id="@+id/addTeacherTv"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:layout_below="@+id/messageQrCodeImage"
               android:layout_centerHorizontal="true"
               android:layout_marginTop="19dp"
               android:background="@drawable/message_add_bg"
               android:paddingLeft="44dp"
               android:paddingTop="6dp"
               android:paddingRight="43dp"
               android:paddingBottom="6dp"
               android:text="添加老师微信"
               android:textColor="#FFFFFF"
               android:textSize="16sp" />
       </RelativeLayout>
   </RelativeLayout>
</LinearLayout>