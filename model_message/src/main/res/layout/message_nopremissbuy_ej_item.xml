<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="data"
            type="com.zizhiguanjia.model_message.bean.NoPremissBuyBean" />
    </data>
    <LinearLayout
        android:layout_marginRight="11dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <RelativeLayout
            android:background="@drawable/nopremiss_bg"
            android:layout_width="match_parent"
            android:layout_height="110.5dp">
            <LinearLayout
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <ImageView
                    android:id="@+id/messageDesImg"
                    android:layout_marginTop="22dp"
                    android:src="@drawable/meaasge_mnks"
                    android:layout_width="27dp"
                    android:layout_height="27dp"/>
                <TextView
                    android:id="@+id/titleTv"
                    android:layout_marginTop="5dp"
                    android:text="@{data.title}"
                    android:textColor="#863421"
                    android:textSize="13sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
            <TextView
                android:layout_marginBottom="5dp"
                android:layout_centerHorizontal="true"
                android:layout_alignParentBottom="true"
                android:textSize="12sp"
                android:textColor="#863421"
                android:text="@{data.msg}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <RelativeLayout
                android:background="@drawable/nopremiss_tv_bg"
                android:layout_width="37.5dp"
                android:layout_height="18dp">
                <TextView
                    android:layout_centerVertical="true"
                    android:paddingLeft="7.5dp"
                    android:text="@{data.index}"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </RelativeLayout>

        </RelativeLayout>
    </LinearLayout>
<!--    <LinearLayout-->
<!--        android:layout_marginRight="11dp"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content">-->
<!--        <RelativeLayout-->
<!--            android:background="@drawable/nopremiss_rj_bg"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="84dp">-->

<!--            <LinearLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="match_parent"-->
<!--                android:gravity="center_horizontal"-->
<!--                android:orientation="vertical">-->

<!--                <ImageView-->
<!--                    android:id="@+id/messageDesImg"-->
<!--                    android:layout_width="27dp"-->
<!--                    android:layout_height="27dp"-->
<!--                    android:layout_marginTop="22dp"-->
<!--                    android:src="@drawable/meaasge_mnks" />-->

<!--                <TextView-->
<!--                    android:id="@+id/titleTv"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginTop="5dp"-->
<!--                    android:text="@{data.title}"-->
<!--                    android:textColor="#863421"-->
<!--                    android:textSize="13sp" />-->
<!--            </LinearLayout>-->
<!--            <RelativeLayout-->
<!--                android:background="@drawable/nopremiss_tv_bg"-->
<!--                android:layout_width="37.5dp"-->
<!--                android:layout_height="18dp">-->
<!--                <TextView-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:paddingLeft="7.5dp"-->
<!--                    android:text="@{data.index}"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="12sp"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"/>-->
<!--            </RelativeLayout>-->

<!--        </RelativeLayout>-->
<!--    </LinearLayout>-->
</layout>