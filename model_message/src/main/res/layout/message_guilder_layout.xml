<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/relDes"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >

        <ImageView
            android:layout_width="74dp"
            android:layout_height="28dp"
            android:layout_alignTop="@+id/center_img"
            android:layout_toLeftOf="@+id/center_img"
            android:src="@drawable/message_left_img" />

        <ImageView
            android:id="@+id/center_img"
            android:layout_width="54dp"
            android:layout_height="89dp"
            android:layout_centerInParent="true"
            android:src="@drawable/message_ss_img" />

        <ImageView
            android:layout_width="74dp"
            android:layout_height="28dp"
            android:layout_alignTop="@+id/center_img"
            android:layout_toRightOf="@+id/center_img"
            android:src="@drawable/message_guider_right_img" />

        <TextView
            android:gravity="center"
            android:id="@+id/tvDesTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/center_img"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="16dp"
            android:text="左右滑动屏幕，\n可以查看上一题下一题哦~"
            android:textColor="@color/white"
            android:textSize="17sp" />
    </RelativeLayout>
</RelativeLayout>