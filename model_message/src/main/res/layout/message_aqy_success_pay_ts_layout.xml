<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:background="#00000000"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="115dp"
        android:background="@drawable/message_timechoice_bg"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="17dp"
            android:layout_marginTop="80dp"
            android:layout_marginRight="17dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="您已购"
                android:textColor="#333333"
                android:textSize="19sp" />

            <TextView
                android:id="@+id/selectDesTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="单科打包套餐"
                android:textColor="#D52930"
                android:textSize="18sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/selectMsgTv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="17dp"
            android:layout_marginTop="13dp"
            android:layout_marginRight="17dp"
            android:gravity="center"
            android:text="选择科目后即可畅快刷题"
            android:textColor="#333333"
            android:textSize="15sp" />
    </LinearLayout>
    <ImageView
        android:id="@+id/successDesImg"
        android:layout_centerHorizontal="true"
        android:src="@drawable/message_pay_aqy_success"
        android:layout_width="169dp"
        android:layout_height="171dp"/>
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/btMain"
        android:layout_marginBottom="10dp"
        android:gravity="center"
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/goToKmTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/message_gotokm_bg"
            android:paddingLeft="41dp"
            android:paddingTop="8dp"
            android:paddingRight="41dp"
            android:paddingBottom="8dp"
            android:text="去选择科目"
            android:textColor="@color/white"
            android:textSize="16sp" />
        <TextView
            android:id="@+id/goToKmTv1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/message_gotokm_bg"
            android:paddingLeft="19dp"
            android:paddingTop="8dp"
            android:paddingRight="18dp"
            android:paddingBottom="8dp"
            android:text="去选择科目"
            android:textColor="@color/white"
            android:textSize="16sp" />
            <TextView
                android:textSize="13sp"
                android:layout_marginBottom="18dp"
                android:layout_marginTop="10dp"
                android:text="买错了，更换科目"
                android:id="@+id/goToUpdateSubjectTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
    </LinearLayout>

</RelativeLayout>