<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="bean"
            type="com.zizhiguanjia.model_message.bean.GoodsInfoBean" />
    </data>
    <RelativeLayout
        android:layout_marginTop="7dp"
        android:layout_marginBottom="7dp"
        android:id="@+id/selectRel"
        android:paddingLeft="22dp"
        android:paddingRight="15dp"
        android:paddingBottom="12dp"
        android:paddingTop="12dp"
        android:background="@drawable/message_buy_select_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_toLeftOf="@+id/rightSelectImg"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
           <LinearLayout
               android:gravity="center_vertical"
               android:orientation="horizontal"
               android:layout_width="match_parent"
               android:layout_height="wrap_content">
               <TextView
                   android:textStyle="bold"
                   android:text="@{bean.goodsName}"
                   android:textSize="14sp"
                   android:textColor="#000000"
                   android:layout_width="wrap_content"
                   android:layout_height="wrap_content"/>
               <ImageView
                   android:layout_marginLeft="10dp"
                   android:scaleType="fitXY"
                   android:src="@drawable/message_hot"
                   android:layout_width="44dp"
                   android:layout_height="15dp"/>
           </LinearLayout>
            <TextView
                android:layout_marginTop="5dp"
                android:text="@{bean.info}"
                android:textSize="12sp"
                android:textColor="#B57D34"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
        <ImageView
            android:layout_centerVertical="true"
            android:id="@+id/rightSelectImg"
            android:layout_alignParentRight="true"
            android:src="@drawable/message_buy_select"
            android:layout_width="17dp"
            android:layout_height="17dp"/>
    </RelativeLayout>
</layout>