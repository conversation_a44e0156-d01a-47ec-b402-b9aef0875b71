<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:background="@drawable/message_exam_bottom_bg"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/imgClose"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignParentRight="true"
            android:layout_marginRight="5.5dp"
            android:layout_marginTop="11dp"
            android:scaleType="fitXY"
            android:src="@drawable/message_close" />
    </RelativeLayout>
   <include
       android:visibility="gone"
       android:id="@+id/message_scour_layout"
       layout="@layout/message_custom_scouce_layout"/>
    <include
        android:visibility="gone"
        android:id="@+id/message_scour_success_layout"
        layout="@layout/message_custom_scuccess_layout"/>
    <include
        android:visibility="gone"
        android:id="@+id/message_faceback_layout"
        layout="@layout/message_faceback_layout"/>
      <TextView
          android:id="@+id/tvPostDatas"
          android:layout_marginTop="38dp"
          android:layout_marginRight="44.5dp"
          android:layout_marginLeft="44.5dp"
          android:gravity="center"
          android:background="@drawable/message_login_nor_bg"
          android:paddingBottom="12dp"
          android:paddingTop="12dp"
          android:textColor="#FFFEFF"
          android:text="提交信息"
          android:textSize="16sp"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"/>
</LinearLayout>