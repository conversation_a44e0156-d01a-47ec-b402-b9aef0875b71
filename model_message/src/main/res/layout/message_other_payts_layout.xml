<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/bt1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:layout_marginTop="92dp"
        android:background="@drawable/message_other_pay_ts_top_bg"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:id="@+id/top1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="68dp"
            android:gravity="center"
            android:text="活动福利"
            android:textColor="#000000"
            android:textSize="20.5sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/freeBuyTv"
            android:layout_below="@+id/top1"
            android:orientation="vertical"
            android:paddingLeft="31dp"
            android:paddingTop="11dp"
            android:paddingRight="31dp">

            <TextView
                android:id="@+id/msgTv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/contextTv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="活动只有30天，结束后即恢复原价，抓紧升级～"
                android:textColor="#B04600"
                android:textSize="13sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/freeBuyTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="32dp"
            android:background="@drawable/message_other_ts_bt"
            android:paddingLeft="63.5dp"
            android:paddingTop="9dp"
            android:paddingRight="63.5dp"
            android:paddingBottom="9dp"
            android:text="免费升级VIP"
            android:textColor="#5A3C0B"
            android:textSize="16.5dp" />
        <ImageView
            android:id="@+id/otherCloseImage"
            android:layout_marginTop="7dp"
            android:layout_marginRight="9dp"
            android:layout_alignParentRight="true"
            android:src="@drawable/messale_close"
            android:layout_width="30dp"
            android:layout_height="30dp"/>
    </RelativeLayout>
    <ImageView
        android:layout_centerHorizontal="true"
        android:src="@drawable/message_other_ts"
        android:layout_width="142dp"
        android:layout_height="132dp"/>
</RelativeLayout>