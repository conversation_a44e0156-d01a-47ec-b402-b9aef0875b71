<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:background="@drawable/message_exam_bottom_bg"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:layout_marginTop="16dp"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:textSize="22.5sp"
            android:layout_centerHorizontal="true"
            android:text="更换科目"
            android:textColor="#0E152B"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:paddingLeft="13dp"
            android:paddingRight="13dp"
            android:id="@+id/tvCancle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:text="取消"
            android:textColor="#999999"
            android:textSize="15sp" />
    </RelativeLayout>
    <RelativeLayout
        android:layout_marginTop="23dp"
        android:layout_marginRight="12dp"
        android:layout_marginLeft="12dp"
        android:paddingBottom="12dp"
        android:paddingTop="12dp"
        android:paddingRight="10dp"
        android:paddingLeft="13dp"
        android:background="@drawable/message_certificate_updata_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:textStyle="bold"
                android:textColor="#666666"
                android:text="已选择地区："
                android:textSize="15sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/tvCityName"
                android:textStyle="bold"
                android:textSize="15sp"
                android:textColor="#4D7FF7"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
        <LinearLayout
            android:paddingRight="5dp"
            android:paddingLeft="5dp"
            android:id="@+id/tvUpdataCity"
            android:gravity="center_vertical"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView

                android:text="更改"
                android:textSize="12sp"
                android:textColor="#999999"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <ImageView
                android:src="@drawable/common_right_img"
                android:layout_width="11dp"
                android:layout_height="11dp"/>
        </LinearLayout>
    </RelativeLayout>
    <RelativeLayout
        android:layout_marginTop="10dp"
        android:layout_marginRight="12dp"
        android:layout_marginLeft="12dp"
        android:paddingBottom="12dp"
        android:paddingTop="12dp"
        android:paddingRight="10dp"
        android:paddingLeft="13dp"
        android:background="@drawable/message_certificate_updata_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:textColor="#666666"
                android:text="已选中科目："
                android:textStyle="bold"
                android:textSize="15sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/tvCertificateName"
                android:textStyle="bold"
                android:textSize="15sp"
                android:textColor="#4D7FF7"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
        <LinearLayout
            android:paddingRight="5dp"
            android:paddingLeft="5dp"
            android:id="@+id/tvUpdataCertificate"
            android:gravity="center_vertical"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:text="更改"
                android:textSize="12sp"
                android:textColor="#999999"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <ImageView
                android:src="@drawable/common_right_img"
                android:layout_width="11dp"
                android:layout_height="11dp"/>
        </LinearLayout>
    </RelativeLayout>
    <TextView
        android:id="@+id/tvSave"
        android:layout_marginTop="33dp"
        android:paddingTop="10dp"
        android:paddingBottom="12dp"
        android:paddingLeft="111dp"
        android:paddingRight="111dp"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:text="保存更改"
        android:background="@drawable/message_cydc_bg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
</LinearLayout>