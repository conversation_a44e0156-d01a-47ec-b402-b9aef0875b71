<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="270dp"
    android:layout_height="328dp">

    <LinearLayout
        android:layout_width="270dp"
        android:layout_height="328dp"
        android:background="@drawable/mesaage_ts"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <include
            android:id="@+id/message1"
            layout="@layout/message_learning_one"
            android:visibility="gone" />

        <include
            android:id="@+id/message2"
            layout="@layout/message_learning_two"
            android:visibility="gone" />

    </LinearLayout>
    <RelativeLayout
        android:layout_marginRight="18.5dp"
        android:layout_marginLeft="18.5dp"
        android:layout_marginBottom="20dp"
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvjxxx"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:background="@drawable/message_jxxx_bg"
            android:paddingLeft="24.5dp"
            android:paddingTop="8.5dp"
            android:paddingRight="24.5dp"
            android:paddingBottom="8.5dp"
            android:text="继续学习"
            android:textColor="#656565"
            android:textSize="14.4sp" />

        <TextView
            android:id="@+id/tvcydc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:background="@drawable/message_cydc_bg"
            android:paddingLeft="24.5dp"
            android:paddingTop="8.5dp"
            android:paddingRight="24.5dp"
            android:paddingBottom="8.5dp"
            android:text="参与调研"
            android:textColor="#FFFEFF"
            android:textSize="14.4sp" />
    </RelativeLayout>
</RelativeLayout>