<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="bean"
            type="com.zizhiguanjia.model_message.bean.GoodsInfoBean" />
    </data>
    <RelativeLayout
        android:id="@+id/selectNorRel"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:paddingRight="15dp"
        android:paddingLeft="22dp"
        android:background="@drawable/message_buy_bor_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:textStyle="bold"
            android:textColor="#3D4755"
            android:textSize="14sp"
            android:text="@{bean.goodsName}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <ImageView
            android:id="@+id/selectIma"
            android:layout_alignParentRight="true"
            android:src="@drawable/message_buy_nor"
            android:layout_width="17dp"
            android:layout_height="17dp"/>
    </RelativeLayout>
</layout>