<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/_popup_round4_bg"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:layout_marginBottom="31dp"
            android:ellipsize="end"
            android:gravity="center_horizontal"
            android:text="dadasdas"
            android:textColor="@color/_popup_title_color"
            android:textSize="18sp" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingStart="20dp"
            android:paddingTop="15dp"
            android:paddingEnd="20dp"
            android:paddingBottom="5dp"
            android:textColor="@color/_popup_content_color"
            android:textSize="16sp" />

        <EditText
            android:id="@+id/et_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="10dp"
            android:singleLine="true"
            android:textColor="@color/_popup_content_color"
            android:textSize="16sp"
            android:visibility="gone" />
    </LinearLayout>

    <View
        android:id="@+id/xpopup_divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_above="@+id/llBottom"
        android:layout_marginTop="31.5dp"
        android:background="#C5C5C5" />

    <LinearLayout
        android:id="@+id/llBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:gravity="right"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?android:attr/selectableItemBackground"
            android:gravity="center"
            android:paddingTop="11dp"
            android:paddingBottom="11dp"
            android:text="@string/xpopup_ok"
            android:textColor="#3163F6"
            android:textSize="16sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_above="@+id/llBottom"
            android:background="#C5C5C5" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?android:attr/selectableItemBackground"
            android:gravity="center"
            android:paddingTop="11dp"
            android:paddingBottom="11dp"
            android:text="@string/xpopup_cancel"
            android:textSize="16sp" />
    </LinearLayout>
</RelativeLayout>