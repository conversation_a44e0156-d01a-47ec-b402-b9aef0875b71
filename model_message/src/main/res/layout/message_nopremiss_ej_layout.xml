<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/message_premissbuy_two_bg"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:id="@+id/topRel"
        android:layout_marginTop="11dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <TextView
            android:layout_marginTop="14dp"
            android:id="@+id/edTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:text="没有权限哦，升级题库无限刷"
            android:textColor="#66240B"
            android:textSize="21sp" />
        <ImageView
            android:id="@+id/imgClose"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="5dp"
            android:src="@drawable/message_close" />

    </RelativeLayout>
    <LinearLayout
        android:layout_below="@+id/topRel"
        android:layout_above="@+id/bottomRel"
        android:orientation="vertical"
        android:layout_marginTop="28dp"
        android:gravity="top|center"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.recyclerview.widget.RecyclerView
            app:spanCount="4"
            android:layout_marginRight="8dp"
            android:layout_marginLeft="19dp"
            android:id="@+id/premissRvs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
        <androidx.recyclerview.widget.RecyclerView
            android:layout_marginTop="18dp"
            android:layout_marginRight="14dp"
            android:layout_marginLeft="21dp"
            android:id="@+id/premissGoodsRvs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </LinearLayout>
    <RelativeLayout
        android:id="@+id/bottomRel"
        android:layout_alignParentBottom="true"
        android:layout_marginTop="20dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/imgUpVip"
                android:layout_width="wrap_content"
                android:layout_height="56dp"
                android:layout_marginLeft="24.5dp"
                android:layout_marginRight="24.5dp"
                android:scaleType="fitXY"
                android:src="@drawable/message_exam_vip_bt_img" />
            <LinearLayout
                android:gravity="center"
                android:layout_centerVertical="true"
                android:layout_marginLeft="56dp"
                android:orientation="vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <TextView
                        android:textSize="13sp"
                        android:textColor="#FFE7DB"
                        android:text="￥"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                    <TextView
                        android:id="@+id/pricesTv"
                        android:textSize="21sp"
                        android:textColor="#FFE7DB"
                        android:text="300.0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                </LinearLayout>
                <TextView
                    android:visibility="gone"
                    android:alpha="0.67"
                    android:id="@+id/timesTv"
                    android:textSize="11sp"
                    android:textColor="#FFE8DB"
                    android:text="-年有效期"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>

        </RelativeLayout>
        <ImageView
            android:id="@+id/imgGuild"
            android:scaleType="fitCenter"
            android:layout_marginTop="15dp"
            android:layout_alignParentRight="true"
            android:src="@drawable/message_exam_vip_ss_img"
            android:layout_width="59dp"
            android:layout_height="79dp"/>
    </RelativeLayout>
</RelativeLayout>