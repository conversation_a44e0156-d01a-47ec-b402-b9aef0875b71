<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data></data>

    <LinearLayout
        android:background="#F4F7F9"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.wb.lib_weiget.titlebar.TitleBar xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/tbContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tb_centerText="考前模拟"
            app:tb_centerTextColor="@color/white"
            app:tb_centerTextSize="18sp"
            app:tb_centerType="textView"
            app:tb_leftImageResource="@drawable/common_left_back_write_img"
            app:tb_leftType="imageButton"
            app:tb_statusBarColor="#3163F6"
            app:tb_titleBarColor="#3163F6" />

        <com.wb.lib_weiget.views.MultipleStatusView xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/mlstvCommonListView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:emptyView="@layout/layout_common_empty"
            app:errorView="@layout/custom_error_view"
            app:loadingView="@layout/public_loading_list"
            app:noNetworkView="@layout/custom_no_network_view">

            <include
                android:id="@+id/icdeCommonContent"
                layout="@layout/list_exam_resh_listview" />
        </com.wb.lib_weiget.views.MultipleStatusView>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_clear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableTop="@drawable/icon_exam_clear_record"
            android:gravity="center"
            android:layout_gravity="center_horizontal"
            android:drawablePadding="7dp"
            android:text="一键清空所有做题记录"
            android:textColor="#ff999999"
            android:textSize="12sp"
            android:layout_marginVertical="35dp"
            android:visibility="gone"
            tools:visibility="visible"/>
    </LinearLayout>
</layout>