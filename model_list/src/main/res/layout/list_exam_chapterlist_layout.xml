<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="data"
            type="com.zizhiguanjia.model_list.model.ExamChapterBean" />
    </data>
    <LinearLayout
        android:background="#F4F7F9"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <com.wb.lib_weiget.views.MultipleStatusView
            app:emptyView="@layout/layout_common_empty"
            app:loadingView="@layout/public_loading_list"
            app:noNetworkView="@layout/custom_no_network_view"
            android:id="@+id/msvExamChapter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1">
            <include
                app:data="@{data}"
                android:id="@+id/icdContent"
                layout="@layout/list_exam_chapter_content_layout"/>
        </com.wb.lib_weiget.views.MultipleStatusView>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_clear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableTop="@drawable/icon_exam_clear_record"
            android:gravity="center"
            android:layout_gravity="center_horizontal"
            android:drawablePadding="7dp"
            android:text="一键清空所有做题记录"
            android:textColor="#ff999999"
            android:textSize="12sp"
            android:layout_marginVertical="35dp"/>
    </LinearLayout>
</layout>