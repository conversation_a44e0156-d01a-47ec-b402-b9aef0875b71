<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_list.viewmodel.AutoTestExamViewMode" />
    </data>
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fillViewport="true">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <View
                android:layout_width="match_parent"
                android:layout_height="312.5dp"
                android:background="@drawable/list_exam_autotest_bg" />

            <ImageView
                android:id="@+id/imgExamAutoDes"
                android:layout_width="251dp"
                android:layout_height="179dp"
                android:layout_marginLeft="76.5dp"
                android:layout_marginTop="203dp"
                android:scaleType="fitXY"
                android:src="@drawable/list_exam_auto_des_bg" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/imgExamAutoDes"
                android:layout_marginTop="35dp"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="模拟考试  智能组卷"
                    android:textColor="#1D1E21"
                    android:textSize="20sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="47.5dp"
                    android:layout_marginTop="9.5dp"
                    android:layout_marginRight="47.5dp"
                    android:gravity="center"
                    android:text="由安全员考试宝典为你生成与目标考试考点范围，难度分布适量匹配的模考"
                    android:textColor="#888C94"
                    android:textSize="14.5sp" />

                <TextView
                    android:id="@+id/tvAutoExamTest"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="172dp"
                    android:layout_marginRight="15dp"
                    android:background="@drawable/list_exam_button_bg"
                    android:gravity="center"
                    android:onClick="@{model::onClick}"
                    android:paddingTop="12.5dp"
                    android:paddingBottom="10dp"
                    android:text="开始模考"
                    android:textColor="@color/white"
                    android:textSize="16sp" />
            </LinearLayout>

            <RelativeLayout
                android:id="@+id/relBack"
                android:layout_width="22dp"
                android:layout_height="38dp"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="45dp"
                android:onClick="@{model::onClick}">

                <ImageView
                    android:id="@+id/imgAuto"
                    android:layout_width="11dp"
                    android:layout_height="19dp"
                    android:src="@drawable/list_exam_back" />
            </RelativeLayout>
        </RelativeLayout>
    </androidx.core.widget.NestedScrollView>
</layout>
