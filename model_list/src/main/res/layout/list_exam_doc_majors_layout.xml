<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.wb.lib_weiget.titlebar.TitleBar
            android:id="@+id/tbTopTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tb_centerText="学习资料"
            app:tb_centerTextColor="@color/white"
            app:tb_centerTextSize="18sp"
            app:tb_leftImageResource="@drawable/common_left_back_write_img"
            app:tb_leftType="imageButton"
            app:tb_statusBarColor="#3163F6"
            app:tb_titleBarColor="#3163F6" />

        <com.wb.lib_weiget.views.MultipleStatusView
            android:id="@+id/mlstvCommonListView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:emptyView="@layout/layout_common_empty"
            app:errorView="@layout/custom_error_view"
            app:loadingView="@layout/public_loading_list"
            app:noNetworkView="@layout/custom_no_network_view">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </com.wb.lib_weiget.views.MultipleStatusView>
    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>