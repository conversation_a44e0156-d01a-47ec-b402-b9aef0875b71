<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/bg_dialog_exam_clear_record"/>
    
    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/iv_close"
        android:layout_width="30dp"
        android:layout_height="30dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="35dp"
        android:layout_marginEnd="9dp"
        android:scaleType="centerCrop"
        android:src="@drawable/common_close"
        android:background="@null"
        android:padding="8dp"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_close"
        android:layout_marginTop="4dp"
        android:layout_marginHorizontal="29dp"
        android:text="全部重新练习"
        android:textColor="#ff1f3c64"
        android:textSize="19sp"
        android:textStyle="bold"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_hint"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@id/tv_title"
        app:layout_constraintRight_toRightOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        android:layout_marginTop="13.5dp"
        android:text="所有已做题记录一键清空，包含错题集和模拟考试的数据，您可以完全重新开始练习本题库。"
        android:textColor="#ff6f737c"
        android:textSize="14sp"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_hint_second"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@id/tv_title"
        app:layout_constraintRight_toRightOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_hint"
        android:layout_marginTop="35dp"
        android:text="确认重新练习吗？"
        android:textColor="#ff6f737c"
        android:textSize="14sp"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/btn_cancel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintWidth_percent="0.4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/btn_confirm"
        android:layout_marginBottom="30dp"
        android:background="@drawable/hollow_max"
        android:backgroundTint="#396BF6"
        android:paddingVertical="9dp"
        android:text="取消"
        android:gravity="center"
        android:textColor="#ff007aff"
        android:textSize="17sp"
        android:textStyle="bold"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/btn_confirm"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintWidth_percent="0.4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toRightOf="@id/btn_cancel"
        android:layout_marginBottom="30dp"
        android:background="@drawable/solid_max"
        android:backgroundTint="#396BF6"
        android:paddingVertical="9dp"
        android:text="确定"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:textStyle="bold"/>

</androidx.constraintlayout.widget.ConstraintLayout>