<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_list.viewmodel.ExamResultViewModel" />
        <import type="android.view.View"/>
    </data>
    <ScrollView
        android:orientation="vertical"
        android:fillViewport="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <RelativeLayout
            android:background="@{model.themeBean.bgColor}"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:layout_above="@+id/llBottomView"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <LinearLayout
                    android:id="@+id/lnTopCard"
                    android:layout_marginRight="10dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginBottom="10dp"
                    android:layout_marginTop="10dp"
                    android:paddingBottom="15dp"
                    android:orientation="vertical"
                    android:background="@drawable/list_exam_chapter_bg"
                    android:backgroundTint="@{model.themeBean.answerPageCardBgColor}"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:layout_marginLeft="17.5dp"
                        android:layout_marginRight="12.5dp"
                        android:gravity="bottom"
                        android:orientation="horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="73.5dp">
                        <RelativeLayout
                            android:paddingTop="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content">
                            <com.zizhiguanjia.model_list.view.CircularProgressView
                                android:id="@+id/cpvScollViews"
                                xmlns:app="http://schemas.android.com/apk/res-auto"
                                app:list_backColor="#F4F4F4"
                                app:list_backWidth="2dp"
                                app:list_progColor="#3163F6"
                                app:list_progWidth="5dp"
                                android:layout_width="70dp"
                                android:layout_height="70dp"/>
                            <LinearLayout
                                android:layout_centerInParent="true"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content">
                                <TextView
                                    android:id="@+id/tvCurrentExam"
                                    android:text="27"
                                    android:textStyle="bold"
                                    android:textSize="26sp"
                                    android:textColor="#3163F6"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>
                                <TextView
                                    android:id="@+id/tvExamChildInfo"
                                    android:textSize="13sp"
                                    android:textColor="#3163F6"
                                    android:text=".5分"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>
                            </LinearLayout>
                        </RelativeLayout>
                        <RelativeLayout
                            android:layout_marginLeft="34.5dp"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">
                            <TextView
                                android:layout_alignParentBottom="true"
                                android:paddingBottom="7dp"
                                android:paddingTop="6.5dp"
                                android:paddingRight="8dp"
                                android:paddingLeft="8dp"
                                android:textSize="13sp"
                                android:textColor="@color/white"
                                android:text="@{model.questionTitlteDes}"
                                android:background="@drawable/list_exam_result_des_bg"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                            <ImageView
                                android:id="@+id/imgExamResultTopTags"
                                android:layout_marginTop="8dp"
                                android:layout_width="47.5dp"
                                android:layout_height="38.5dp"/>
                        </RelativeLayout>
                    </LinearLayout>
                    <LinearLayout
                        android:gravity="center_vertical"
                        android:layout_marginTop="17.5dp"
                        android:orientation="horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <LinearLayout
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content">
                            <TextView
                                android:id="@+id/tvCount1"
                                android:textStyle="bold"
                                android:textSize="18sp"
                                android:textColor="@{model.themeBean.answerPageCountTextColor}"
                                android:text="@{model.questionNum}"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:id="@+id/tvCountTip1"
                                android:layout_marginTop="9.5dp"
                                android:textSize="12sp"
                                android:textColor="@{model.themeBean.answerPageCountTipTextColor}"
                                android:text="题目数量"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                        <View
                            android:background="#F4F4F4"
                            android:layout_width="1dp"
                            android:layout_height="20dp"/>
                        <LinearLayout
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content">
                            <TextView
                                android:id="@+id/tvCount2"
                                android:textStyle="bold"
                                android:textSize="18sp"
                                android:textColor="@{model.themeBean.answerPageCountTextColor}"
                                android:text="@{model.questionRight}"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:id="@+id/tvCountTip2"
                                android:layout_marginTop="9.5dp"
                                android:textSize="12sp"
                                android:textColor="@{model.themeBean.answerPageCountTipTextColor}"
                                android:text="正确"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                        <View
                            android:background="#F4F4F4"
                            android:layout_width="1dp"
                            android:layout_height="20dp"/>
                        <LinearLayout
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content">
                            <TextView
                                android:id="@+id/tvCount3"
                                android:textStyle="bold"
                                android:textSize="18sp"
                                android:textColor="@{model.themeBean.answerPageCountTextColor}"
                                android:text="@{model.questionError}"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:id="@+id/tvCountTip3"
                                android:layout_marginTop="9.5dp"
                                android:textSize="12sp"
                                android:textColor="@{model.themeBean.answerPageCountTipTextColor}"
                                android:text="错误"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                        <View
                            android:background="#F4F4F4"
                            android:layout_width="1dp"
                            android:layout_height="20dp"/>
                        <LinearLayout
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content">
                            <TextView
                                android:id="@+id/tvCount4"
                                android:textStyle="bold"
                                android:textSize="18sp"
                                android:textColor="@{model.themeBean.answerPageCountTextColor}"
                                android:text="@{model.questionDoNor}"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:id="@+id/tvCountTip4"
                                android:layout_marginTop="9.5dp"
                                android:textSize="12sp"
                                android:textColor="@{model.themeBean.answerPageCountTipTextColor}"
                                android:text="未填写"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
                <LinearLayout
                    android:visibility="@{model.questionRemind==null?View.GONE:View.VISIBLE}"
                    android:layout_marginBottom="10dp"
                    android:layout_marginLeft="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <ImageView
                        android:src="@drawable/list_exam_result_tag_img"
                        android:layout_width="14dp"
                        android:layout_height="14dp"/>
                    <TextView
                        android:id="@+id/tvDes"
                        android:layout_marginLeft="4dp"
                        android:text="@{model.questionRemind}"
                        android:textSize="11sp"
                        android:textColor="@{model.themeBean.answerPageDesTextColor}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/lnBottomCard"
                    android:background="@drawable/list_exam_chapter_bg"
                    android:backgroundTint="@{model.themeBean.answerPageCardBgColor}"
                    android:layout_marginBottom="10dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <RelativeLayout
                        android:layout_marginLeft="10dp"
                        android:layout_marginTop="15dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:id="@+id/tvBottomCardTitle"
                            android:text="答题卡"
                            android:textColor="@{model.themeBean.answerPageTitle}"
                            android:textSize="18sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_marginLeft="19dp"
                            android:layout_marginRight="19dp"
                            android:gravity="right"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical">

                                <View
                                    android:layout_width="8dp"
                                    android:layout_height="8dp"
                                    android:background="@drawable/list_exam_single_right_bg" />

                                <TextView
                                    android:id="@+id/tvStateTipSure"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="2.5dp"
                                    android:text="正确"
                                    android:textColor="@{model.themeBean.answerResultTipTextColor}"
                                    android:textSize="12sp" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="15dp"
                                android:gravity="center">

                                <View
                                    android:layout_width="8dp"
                                    android:layout_height="8dp"
                                    android:background="@drawable/list_exam_single_error" />

                                <TextView
                                    android:id="@+id/tvStateTipError"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="2.5dp"
                                    android:text="错误"
                                    android:textColor="@{model.themeBean.answerResultTipTextColor}"
                                    android:textSize="12sp" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="15dp"
                                android:gravity="center">

                                <androidx.appcompat.widget.AppCompatImageView
                                    android:id="@+id/ivTagUnDo"
                                    android:layout_width="8dp"
                                    android:layout_height="8dp" />
                                <TextView
                                    android:id="@+id/tvStateTipNo"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="2.5dp"
                                    android:text="未填写"
                                    android:textColor="@{model.themeBean.answerResultTipTextColor}"
                                    android:textSize="12sp" />
                            </LinearLayout>
                        </LinearLayout>
                    </RelativeLayout>
                    <View
                        android:layout_marginTop="10dp"
                        android:background="@{model.themeBean.lineColor}"
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"/>
                    <androidx.recyclerview.widget.RecyclerView
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="10dp"
                        android:id="@+id/icbRcyExamResult"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:background="@{model.themeBean.bgColor}"
                android:id="@+id/llBottomView"
                android:orientation="vertical"
                android:layout_alignParentBottom="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <View
                    android:background="@{model.themeBean.lineColor}"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"/>
                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/tvReStartExam"
                        android:visibility="@{model.syylState?View.VISIBLE:View.GONE}"
                        android:onClick="@{model::onClick}"
                        android:layout_marginTop="15dp"
                        android:layout_marginBottom="15dp"
                        android:layout_marginRight="19.5dp"
                        android:layout_marginLeft="19.5dp"
                        android:paddingBottom="11dp"
                        android:paddingTop="11.5dp"
                        android:gravity="center"
                        android:text="重新练习"
                        android:textColor="@color/white"
                        android:background="@drawable/list_exam_button_bg"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"/>

                    <TextView
                        android:id="@+id/tvAlsy"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="19.5dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginRight="19.5dp"
                        android:layout_marginBottom="15dp"
                        android:layout_weight="1"
                        android:background="@drawable/list_exam_button_bg"
                        android:gravity="center"
                        android:onClick="@{model::onClick}"
                        android:paddingTop="11.5dp"
                        android:paddingBottom="11dp"
                        android:text="查看错题解析"
                        android:textColor="@color/white" />
                </LinearLayout>
            </LinearLayout>
        </RelativeLayout>
    </ScrollView>
</layout>