<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="data"
            type="com.zizhiguanjia.model_list.model.AnswerSheetOptionBean" />
        <variable
            name="model"
            type="com.zizhiguanjia.model_list.adapter.ExamSheetChildAdapter" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:orientation="vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <View
            android:id="@+id/viewExamSheetQuNumBg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintLeft_toLeftOf="@id/tvExamSheetQuNum"
            app:layout_constraintRight_toRightOf="@id/tvExamSheetQuNum"
            app:layout_constraintTop_toTopOf="@id/tvExamSheetQuNum"
            app:layout_constraintBottom_toBottomOf="@id/tvExamSheetQuNum"
            android:background="@drawable/solid_max"/>

        <TextView
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:onClick="@{(view)->model.onClick(view,data.questionNum,data.see)}"
            android:id="@+id/tvExamSheetQuNum"
            android:gravity="center"
            android:text="@{data.see?String.valueOf(data.questionNum):null}"
            android:textSize="12sp"
            android:textColor="@color/white"
            android:layout_width="35dp"
            android:layout_height="35dp"/>

        <View
            android:id="@+id/viewExamSheetQuNumStore"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintLeft_toLeftOf="@id/tvExamSheetQuNum"
            app:layout_constraintRight_toRightOf="@id/tvExamSheetQuNum"
            app:layout_constraintTop_toTopOf="@id/tvExamSheetQuNum"
            app:layout_constraintBottom_toBottomOf="@id/tvExamSheetQuNum"
            android:background="@drawable/hollow_max"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>