<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="bean"
            type="com.zizhiguanjia.model_list.model.CommonListDataBean.ItemsBean" />
        <variable
            name="go"
            type="java.lang.Boolean" />
        <variable
            name="model"
            type="com.zizhiguanjia.model_list.adapter.ExamCommonListAdapter" />
        <variable
            name="postion"
            type="java.lang.Integer" />
        <import type="android.view.View"/>
    </data>
    <LinearLayout
        android:orientation="vertical"
        android:background="#F4F7F9"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <View
            android:visibility="@{postion==0?View.VISIBLE:View.GONE}"
            android:background="#F4F7F9"
            android:layout_width="match_parent"
            android:layout_height="10dp"/>
        <RelativeLayout
            android:paddingTop="16dp"
            android:paddingRight="12dp"
            android:paddingLeft="12dp"
            android:paddingBottom="20dp"
            android:layout_marginLeft="10dp"
            android:layout_marginBottom="10dp"
            android:layout_marginRight="10dp"
            android:background="@drawable/list_exam_chapter_bg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:layout_toLeftOf="@+id/right_main_ll"
                android:id="@+id/llLeftMain"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <View
                        android:id="@+id/vLine"
                        android:layout_alignParentBottom="true"
                        android:background="@drawable/list_exam_common_list_exam_item_flags_bg"
                        android:layout_width="34dp"
                        android:layout_height="7dp"/>
                    <TextView
                        android:text="@{bean.title}"
                        android:textColor="#333333"
                        android:textSize="16sp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                </RelativeLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:text='@{""+bean.doneCount}'
                        android:layout_marginTop="11dp"
                        android:textSize="14sp"
                        android:textColor="#3163F6"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                    <TextView
                        android:text='@{"/"+bean.examCount}'
                        android:layout_marginTop="11dp"
                        android:textSize="12sp"
                        android:textColor="#666666"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                    <TextView
                        android:layout_marginTop="11dp"
                        android:textSize="13sp"
                        android:textColor="#666666"
                        android:layout_marginLeft="15dp"
                        android:text='@{"正确率："+bean.rightRate}'
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:id="@+id/right_main_ll"
                android:layout_centerVertical="true"
                android:gravity="right"
                android:layout_alignParentRight="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:textSize="12sp"
                    android:textColor="@color/white"
                    android:paddingTop="5dp"
                    android:paddingBottom="4.5dp"
                    android:paddingLeft="13.5dp"
                    android:paddingRight="13.5dp"
                    android:background="@drawable/list_exam_commonlist_exam_answer"
                    android:id="@+id/tvLookExam1"
                    android:text='@{go?"继续答题":"开始答题"}'
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <RelativeLayout
                    android:id="@+id/canGoRel1"
                    android:background="@drawable/list_cango_nor_bg"
                    android:paddingLeft="31dp"
                    android:paddingTop="6dp"
                    android:paddingRight="31dp"
                    android:paddingBottom="6dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <ImageView
                        android:src="@drawable/list_nor"
                        android:layout_width="12dp"
                        android:layout_height="15dp"/>
                </RelativeLayout>
            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>
</layout>