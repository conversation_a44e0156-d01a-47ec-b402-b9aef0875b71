<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_list.viewmodel.ExamResultViewModel" />
    </data>

    <LinearLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.wb.lib_weiget.titlebar.TitleBar
            android:id="@+id/ttbExamTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tb_centerText="模拟考试"
            app:tb_centerTextColor="@color/white"
            app:tb_centerTextSize="18sp"
            app:tb_leftImageResource="@drawable/common_left_back_write_img"
            app:tb_leftType="imageButton"
            app:tb_rightType="textView"
            app:tb_statusBarColor="#3163F6"
            app:tb_titleBarColor="#3163F6" />
        <com.wb.lib_weiget.views.MultipleStatusView
            android:id="@+id/msvExamResult"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:emptyView="@layout/layout_common_empty"
            app:loadingView="@layout/public_loding_view"
            app:noNetworkView="@layout/custom_no_network_view">

            <include
                android:id="@+id/icdExamResultContent"
                layout="@layout/list_exam_result_content"
                app:model="@{model}" />
        </com.wb.lib_weiget.views.MultipleStatusView>
    </LinearLayout>
</layout>