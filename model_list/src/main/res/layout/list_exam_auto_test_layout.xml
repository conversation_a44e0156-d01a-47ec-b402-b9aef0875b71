<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_list.viewmodel.AutoTestExamViewMode" />
        <import type="android.view.View"/>
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            app:model="@{model}"
            android:id="@+id/aqyAuto"
            android:visibility="@{model.commonViewVist?View.VISIBLE:View.GONE}"
            layout="@layout/list_aqy_auto_layout"/>
        <include
            app:model="@{model}"
            android:id="@+id/otherAuto"
            android:visibility="@{model.commonViewVist?View.GONE:View.VISIBLE}"
            layout="@layout/list_other_auto_layout"/>

    </FrameLayout>
</layout>