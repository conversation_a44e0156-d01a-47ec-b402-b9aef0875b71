<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="data"
            type="com.zizhiguanjia.model_list.model.ExamChapterBean" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#F4F7F9">

        <!-- 知识点掌握情况描述 -->
        <LinearLayout
            android:id="@+id/llMasteryDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:orientation="horizontal"
            android:paddingLeft="15dp"
            android:paddingTop="15dp"
            android:paddingBottom="10dp"
            android:paddingRight="15dp"
            android:gravity="start"
            android:visibility="gone">


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="合计"
                    android:textColor="#666666"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tvTotalCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="80"
                    android:textColor="#3163F6"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="个知识点，已掌握"
                    android:textColor="#666666"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tvMasteredCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="20"
                    android:textColor="#3163F6"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="个，知识点掌握度"
                    android:textColor="#666666"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tvMasteryPercent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="25%"
                    android:textColor="#3163F6"
                    android:textSize="14sp"
                    android:textStyle="bold" />
            </LinearLayout>

        <com.wb.lib_refresh_layout.SmartRefreshLayout
            android:id="@+id/main_refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/llMasteryDescription"
            app:srlEnableLoadMore="true">

            <com.zizhiguanjia.model_list.view.StickyListHeaderLayout
                android:id="@+id/slhlExam"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <pokercc.android.expandablerecyclerview.ExpandableRecyclerView
                    android:layout_marginBottom="10dp"
                    android:id="@+id/ervExamChapter"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
            </com.zizhiguanjia.model_list.view.StickyListHeaderLayout>
        </com.wb.lib_refresh_layout.SmartRefreshLayout>


        <LinearLayout
            android:id="@+id/llFloatView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                android:id="@+id/vvChapterGroup"
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:background="#F4F7F9" />

            <com.lihang.ShadowLayout
                app:hl_cornerRadius="4dp"
                android:id="@+id/mShadowLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:hl_bottomShow="true"
                app:hl_leftShow="false"
                app:hl_rightShow="false"
                app:hl_shadowColor="#0A000000"
                app:hl_shadowLimit="15dp"
                app:hl_topShow="false">

                <RelativeLayout
                    android:background="@drawable/list_exam_chapter_xf_bg"
                    android:id="@+id/relGroupMain"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="10dp"
                    android:paddingLeft="19dp"
                    android:paddingTop="20dp"
                    android:paddingRight="3dp"
                    android:paddingBottom="10dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_toLeftOf="@+id/imgEditExam"
                        android:orientation="vertical">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/tvChapterGroupNum"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="10"
                                android:textColor="#333333"
                                android:textSize="20sp"
                                android:textStyle="italic" />

                            <TextView
                                android:id="@+id/tvChapterGroupTitle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_marginLeft="2dp"
                                android:layout_marginRight="7.5dp"
                                android:layout_toRightOf="@+id/tvChapterGroupNum"
                                android:maxWidth="230dp"
                                android:text="@{data.title}"
                                android:textColor="#333333"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <ImageView
                                android:id="@+id/imgRight"
                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                android:layout_centerVertical="true"
                                android:layout_marginTop="7dp"
                                android:layout_toRightOf="@id/tvChapterGroupTitle"
                                android:src="@drawable/list_exam_down_img" />
                        </RelativeLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:gravity="center_vertical">

                            <TextView
                                android:id="@+id/tvChapterGroupDoCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{String.valueOf(data.doneExamCount)}"
                                android:textColor="#3163F6"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tvChapterGroupCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text='@{"/"+String.valueOf(data.examCount)}'
                                android:textColor="#666666"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/tvRight"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="22.5dp"
                                android:text='@{"正确率:"+data.rightRate}'
                                android:textColor="#666666"
                                android:textSize="13sp" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvChapterDes"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="6dp"
                            android:text="@{data.remark}"
                            android:textColor="#A8A8A8"
                            android:textSize="12sp"
                            android:visibility="gone" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/imgEditExam"
                        android:layout_width="33dp"
                        android:layout_height="33dp"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/list_exam_edit_img" />
                </RelativeLayout>
            </com.lihang.ShadowLayout>
        </LinearLayout>


    </RelativeLayout>
</layout>