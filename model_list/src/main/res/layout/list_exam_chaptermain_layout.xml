<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data></data>
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="71dp">

            <com.wb.lib_weiget.titlebar.TitleBar
                android:id="@+id/tbExamChapter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:tb_centerText="章节练习111"
                app:tb_centerTextColor="@color/white"
                app:tb_centerTextSize="18sp"
                app:tb_centerType="textView"
                app:tb_fillStatusBar="true"
                app:tb_leftImageResource="@drawable/list_exam_test_img"
                app:tb_statusBarColor="#3163F6"
                app:tb_titleBarColor="#3163F6" />

            <ImageView
                android:id="@+id/switchImg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginTop="45dp"
                android:layout_marginRight="13dp"
                android:src="@drawable/list_exam_qh" />
        </RelativeLayout>
        <com.zizhiguanjia.model_list.view.NoScrollViewPager
            android:id="@+id/cvpChapterMain"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </LinearLayout>
</layout>