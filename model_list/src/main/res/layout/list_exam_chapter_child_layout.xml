<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data></data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#F4F7F9"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/relExamChapter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:background="@color/white"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12.5dp"
                android:layout_toLeftOf="@+id/llRight"
                android:gravity="center_vertical">

                <com.github.vipulasri.timelineview.TimelineView
                    android:id="@+id/timeline"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    app:endLineColor="#F2F2F2"
                    app:lineOrientation="vertical"
                    app:lineWidth="1dp"
                    app:marker="@drawable/list_exam_chapter_yh_bg"
                    app:markerInCenter="false"
                    app:markerPaddingTop="10dp"
                    app:markerSize="10dp"
                    app:startLineColor="#F2F2F2" />

                <LinearLayout
                    android:id="@+id/llTitleContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5.5dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="5dp">

                    <TextView
                        android:id="@+id/tvChapterChildTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="1.1《中华人民共和国刑法》相关规定"
                        android:textColor="#333333"
                        android:textSize="14sp" />

                    <ImageView
                        android:id="@+id/imgChapterChildExpand"
                        android:layout_width="15dp"
                        android:layout_height="15dp"
                        android:layout_marginLeft="8dp"
                        android:scaleType="centerInside"
                        android:src="@drawable/list_exam_down_img"
                        android:visibility="gone" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llRight"
                android:layout_width="85dp"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="27.5dp"
                android:gravity="center_vertical|end"
                android:orientation="horizontal">

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tvChapterChildDo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textColor="#3163F6"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tvChapterChildCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="/100"
                    android:textColor="#666666"
                    android:textSize="13sp" />

                <ImageView
                    android:id="@+id/imgChapterChildRight"
                    android:layout_width="15dp"
                    android:layout_marginLeft="5dp"
                    android:layout_height="15dp"
                    android:rotation="-90"
                    android:scaleType="centerInside"
                    android:src="@drawable/list_exam_down_img" />
            </LinearLayout>
        </RelativeLayout>
        <!-- 第三层子项的容器，包含时间轴连接 -->
        <LinearLayout
            android:id="@+id/llSubChapterContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:background="@color/white"
            android:orientation="horizontal"
            android:visibility="gone">

            <!-- 第二层到第三层的时间轴连接 -->
            <View
                android:id="@+id/vTimelineConnection"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="17.5dp"
                android:background="#F2F2F2" />

            <!-- 第三层子项的RecyclerView，整体偏右 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvSubChapter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:nestedScrollingEnabled="false" />
        </LinearLayout>

        <View
            android:id="@+id/vvExamChapterChild"
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:background="#F4F7F9"
            android:visibility="gone" />
    </LinearLayout>
</layout>