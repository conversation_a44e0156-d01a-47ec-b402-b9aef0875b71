<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_list.viewmodel.AutoTestExamViewMode" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/list_auto_bg"
        >
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/tvAutoExamTest">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="10dp">

                <RelativeLayout
                    android:id="@+id/topRel"
                    android:layout_marginLeft="25dp"
                    android:layout_marginRight="17dp"
                    android:layout_marginTop="83dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="91.5dp"
                        android:gravity="center_vertical"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="104.5dp"
                            android:layout_height="24.5dp"
                            android:src="@drawable/list_znzh" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="6dp"
                            android:text="根据官方考试大纲与分值占比，智能组卷。"
                            android:textColor="#414A6E"
                            android:textSize="13sp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="85dp"
                        android:layout_height="91.5dp"
                        android:layout_alignParentRight="true"
                        android:src="@drawable/list_auto_top" />
                </RelativeLayout>
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_below="@+id/topRel"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <LinearLayout
                        android:layout_marginTop="8.5dp"
                        android:layout_marginLeft="7.5dp"
                        android:layout_marginRight="7.5dp"
                        android:orientation="vertical"
                        android:background="@drawable/list_other_auto_bg"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:id="@+id/ortherTitleTv"
                            android:layout_marginTop="16.5dp"
                            android:layout_marginLeft="20dp"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#007AFF"
                            android:text="科目：道路运输企业主要负责人"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <TextView
                            android:id="@+id/ortherRemarkTv"
                            android:layout_marginTop="4.5dp"
                            android:layout_marginRight="19.5dp"
                            android:layout_marginLeft="19.5dp"
                            android:textColor="#7E8189"
                            android:textSize="12sp"
                            android:text="科目：道路运输企业主要负责人科目：道路运输企业主要负责人科目：道路运输企业主要负责人科目：道路运输企业主要负责人科目：道路运输企业主要负责人科目：道路运输企业主要负责人"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <View
                            android:layout_marginTop="13dp"
                            android:layout_marginBottom="12dp"
                            android:background="#EAEBF0"
                            android:layout_width="match_parent"
                            android:layout_height="0.5dp"/>
                        <LinearLayout
                            android:layout_marginLeft="20.5dp"
                            android:orientation="horizontal"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <TextView
                                android:text="考试时间："
                                android:textColor="#878CA1"
                                android:textSize="14sp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:id="@+id/ortherTimeTv"
                                android:text="90分钟"
                                android:textColor="#333333"
                                android:textSize="14sp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                        <LinearLayout
                            android:layout_marginBottom="20dp"
                            android:layout_marginTop="12dp"
                            android:layout_marginLeft="20.5dp"
                            android:orientation="horizontal"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <TextView
                                android:text="合格标准："
                                android:textColor="#878CA1"
                                android:textSize="14sp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:id="@+id/ortherBzTv"
                                android:text="总分100分，80分及以上合格"
                                android:textColor="#333333"
                                android:textSize="14sp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                    </LinearLayout>
                    <ImageView
                        android:layout_marginLeft="7.5dp"
                        android:layout_marginRight="7.5dp"
                        android:id="@+id/ortherImage1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>
                    <ImageView
                        android:id="@+id/ortherImage2"
                        android:layout_marginLeft="7.5dp"
                        android:layout_marginRight="7.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>
                    <TextView
                        android:id="@+id/radialTaskTv"
                        android:layout_marginLeft="13dp"
                        android:textSize="12sp"
                        android:text="以上信息仅供参考，具体以当地考试厅发布的信息为准。"
                        android:textColor="#7E8189"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>


                </LinearLayout>

            </RelativeLayout>

        </androidx.core.widget.NestedScrollView>

        <RelativeLayout
            android:id="@+id/relBack"
            android:layout_width="22dp"
            android:layout_height="38dp"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="45dp"
            android:onClick="@{model::onClick}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/imgAuto"
                android:layout_width="match_parent"
                android:layout_height="21dp"
                android:src="@drawable/list_exam_back" />
        </RelativeLayout>

        <TextView
            android:id="@+id/tvAutoExamTest"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="25dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/list_exam_button_bg"
            android:gravity="center"
            android:onClick="@{model::onClick}"
            android:paddingBottom="10dp"
            android:paddingTop="12.5dp"
            android:text="开始模考"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:visibility="gone"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>