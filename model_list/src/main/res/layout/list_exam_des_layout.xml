<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <import type="android.view.View"/>
        <variable
            name="model"
            type="com.zizhiguanjia.model_list.viewmodel.ExamDesViewModel" />
    </data>
    <LinearLayout
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <com.wb.lib_weiget.titlebar.TitleBar
            android:id="@+id/tbTopTitle"
            app:tb_titleBarColor="#3163F6"
            app:tb_statusBarColor="#3163F6"
            app:tb_leftType="imageButton"
            app:tb_leftImageResource="@drawable/common_left_back_write_img"
            app:tb_centerTextColor="@color/white"
            app:tb_centerTextSize="18sp"
            app:tb_centerText="题型练习"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
        <com.wb.lib_weiget.views.MultipleStatusView
            android:id="@+id/mlsvExamDes"
            app:noNetworkView="@layout/custom_no_network_view"
            app:loadingView="@layout/public_loding_view"
            app:emptyView="@layout/layout_common_empty"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <include
                app:model="@{model}"
                android:visibility="@{model.pageState==1||model.pageState==3||model.pageState==4?View.GONE:View.VISIBLE}"
                android:id="@+id/icbExamCommonDes"
                layout="@layout/list_exam_des_tklx_layout"/>
            <include
                app:model="@{model}"
                android:visibility="@{model.pageState==2||model.pageState==4?View.GONE:View.VISIBLE}"
                android:id="@+id/icbExamChapterDes"
                layout="@layout/list_exam_des_chapter"/>
            <include
                app:model="@{model}"
                android:visibility="@{model.pageState==4?View.VISIBLE:View.GONE}"
                android:id="@+id/icbExamQuestionWrongDes"
                layout="@layout/list_exam_des_question_wrong_layout"/>
        </com.wb.lib_weiget.views.MultipleStatusView>
    </LinearLayout>
</layout>