<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data></data>
    <LinearLayout
        android:orientation="vertical"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:padding="5dp"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- 第三层时间轴 -->
            <com.github.vipulasri.timelineview.TimelineView
                android:visibility="gone"
                android:id="@+id/timelineSubChapter"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="12.5dp"
                app:markerPaddingTop="4dp"
                app:marker="@drawable/list_exam_chapter_yh_bg"
                app:markerSize="8dp"
                app:lineWidth="1dp"
                app:markerInCenter="false"
                app:startLineColor="#F2F2F2"
                app:endLineColor="#F2F2F2"
                app:lineOrientation="vertical" />

            <RelativeLayout
                android:id="@+id/relExamSubChapter"
                android:background="@drawable/list_exam_zj"
                android:layout_marginTop="1dp"
                android:layout_marginLeft="5.5dp"
                android:paddingLeft="15dp"
                android:paddingTop="15dp"
                android:paddingBottom="15dp"
                android:paddingRight="15dp"
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvChapterSubChildTitle"
                android:layout_toLeftOf="@+id/llSubRight"
                android:text="1.1.1 具体题目内容"
                android:textSize="14sp"
                android:textColor="#000000"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"/>
            <LinearLayout
                android:gravity="center_vertical"
                android:layout_marginLeft="27.5dp"
                android:id="@+id/llSubRight"
                android:layout_alignParentRight="true"
                android:orientation="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/tvChapterSubChildDo"
                    android:textColor="#3163F6"
                    android:textSize="14sp"
                    android:text="0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:id="@+id/tvChapterSubChildCount"
                    android:textColor="#666666"
                    android:textSize="13sp"
                    android:text="/100"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <ImageView
                    android:visibility="gone"
                    android:layout_marginLeft="29dp"
                    android:layout_marginRight="13dp"
                    android:src="@drawable/common_right_img"
                    android:layout_width="7.5dp"
                    android:layout_height="8.5dp"/>
            </LinearLayout>
        </RelativeLayout>
        </LinearLayout>
    </LinearLayout>
</layout>
