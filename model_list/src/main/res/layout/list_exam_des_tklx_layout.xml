<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="model"
            type="com.zizhiguanjia.model_list.viewmodel.ExamDesViewModel" />
    </data>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:paddingLeft="12dp"
            android:paddingRight="12dp"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <TextView
                android:id="@+id/tvExamCommonTitle"
                android:layout_marginTop="17.5dp"
                android:text="@{model.commonTitlteObs}"
                android:textSize="23sp"
                android:textColor="#424A61"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:layout_marginTop="1.5dp"
                android:text="即将开始"
                android:textSize="12sp"
                android:textColor="#A4B0B9"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <View
                android:layout_marginTop="19.5dp"
                android:layout_marginBottom="21.5dp"
                android:background="#F1F1F1"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"/>
            <TextView
                android:id="@+id/tvExamCommonDes"
                android:text="@{model.commonDesObs}"
                android:textSize="13sp"
                android:textColor="#7E8189"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
        <TextView
            android:onClick="@{model::onclick}"
            android:layout_marginRight="12.5dp"
            android:layout_marginLeft="12.5dp"
            android:layout_marginBottom="130.5dp"
            android:layout_alignParentBottom="true"
            android:paddingBottom="10dp"
            android:paddingTop="12.5dp"
            android:gravity="center"
            android:background="@drawable/list_exam_button_bg"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:text="开始答题"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </RelativeLayout>
</layout>