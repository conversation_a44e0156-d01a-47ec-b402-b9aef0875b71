<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data></data>
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <View
            android:id="@+id/vvChapterGroup"
            android:background="#F4F7F9"
            android:layout_width="match_parent"
            android:layout_height="10dp"/>
        <RelativeLayout
            android:paddingRight="13dp"
            android:paddingLeft="9dp"
            android:paddingTop="20dp"
            android:paddingBottom="10dp"
            android:id="@+id/relGroupMain"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:background="@drawable/list_exam_chapter_bg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:layout_toLeftOf="@+id/imgEditExam"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
               <RelativeLayout
                   android:layout_width="match_parent"
                   android:layout_height="wrap_content">
                       <TextView
                           android:id="@+id/tvChapterGroupNum"
                           android:text="10"
                           android:textColor="#333333"
                           android:textSize="20sp"
                           android:textStyle="italic"
                           android:layout_width="wrap_content"
                           android:layout_height="wrap_content"/>
                       <TextView
                           android:layout_centerVertical="true"
                           android:textStyle="bold"
                           android:maxWidth="230dp"
                           android:layout_toRightOf="@+id/tvChapterGroupNum"
                           android:layout_marginLeft="2dp"
                           android:id="@+id/tvChapterGroupTitle"
                           android:layout_marginRight="7.5dp"
                           android:text="adadas"
                           android:textSize="16sp"
                           android:textColor="#333333"
                           android:layout_width="wrap_content"
                           android:layout_height="wrap_content"/>

                   <ImageView
                       android:id="@+id/imgRight"
                       android:layout_width="15dp"
                       android:layout_height="15dp"
                       android:layout_centerVertical="true"
                       android:layout_marginTop="7dp"
                       android:layout_toRightOf="@id/tvChapterGroupTitle"
                       android:src="@drawable/list_exam_down_img" />
               </RelativeLayout>
                <LinearLayout
                    android:gravity="center_vertical"
                    android:layout_marginTop="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/tvChapterGroupDoCount"
                        android:textColor="#3163F6"
                        android:textSize="14sp"
                        android:text="0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                    <TextView
                        android:id="@+id/tvChapterGroupCount"
                        android:textColor="#666666"
                        android:textSize="12sp"
                        android:text="/100"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                    <TextView
                        android:id="@+id/tvRight"
                        android:layout_marginLeft="22.5dp"
                        android:textSize="13sp"
                        android:textColor="#666666"
                        android:text="正确率:"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                </LinearLayout>
                <TextView
                    android:visibility="gone"
                    android:text="dsadasdasdas"
                    android:id="@+id/tvChapterDes"
                    android:layout_marginTop="6dp"
                    android:textSize="12sp"
                    android:textColor="#A8A8A8"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
            <ImageView
                android:layout_centerVertical="true"
                android:id="@+id/imgEditExam"
                android:layout_alignParentRight="true"
                android:src="@drawable/list_exam_edit_img"
                android:layout_width="33dp"
                android:layout_height="33dp"/>
        </RelativeLayout>
    </LinearLayout>
</layout>