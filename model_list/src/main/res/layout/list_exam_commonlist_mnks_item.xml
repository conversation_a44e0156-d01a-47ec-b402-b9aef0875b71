<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="go"
            type="java.lang.Boolean" />

        <variable
            name="bean"
            type="com.zizhiguanjia.model_list.model.CommonListDataBean.ItemsBean" />

        <variable
            name="postion"
            type="java.lang.Integer" />

        <import type="android.view.View" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#F4F7F9"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:background="#F4F7F9"
            android:visibility="@{postion==0?View.VISIBLE:View.GONE}" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/list_exam_chapter_bg"
            android:paddingLeft="12dp"
            android:paddingTop="16dp"
            android:paddingRight="12dp"
            android:paddingBottom="20dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toLeftOf="@+id/rightRel"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{bean.title}"
                    android:textColor="#333333"
                    android:textSize="16sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14.5dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text='@{""+bean.doneCount}'
                            android:textColor="#3163F6"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text='@{"/"+bean.examCount}'
                            android:textColor="#666666"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="26dp"
                        android:text='@{"得分:"+bean.score}'
                        android:textColor="#3163F6"
                        android:textSize="13sp"
                        android:visibility="@{bean.isSubmit?View.VISIBLE:View.GONE}" />
                </LinearLayout>
            </LinearLayout>

           <LinearLayout
               android:layout_alignParentRight="true"
               android:layout_centerVertical="true"
               android:id="@+id/rightRel"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content">
               <TextView
                   android:visibility="gone"
                   android:id="@+id/tvLookExam"
                   android:layout_width="wrap_content"
                   android:layout_height="wrap_content"
                   android:background="@drawable/list_exam_commonlist_exam_answer"
                   android:paddingLeft="13.5dp"
                   android:paddingTop="5dp"
                   android:paddingRight="13.5dp"
                   android:paddingBottom="4.5dp"
                   android:textColor="@color/white"
                   android:textSize="12sp" />
               <RelativeLayout
                   android:id="@+id/canGoRel"
                   android:background="@drawable/list_cango_nor_bg"
                   android:paddingLeft="31dp"
                   android:paddingTop="6dp"
                   android:paddingRight="31dp"
                   android:paddingBottom="6dp"
                   android:layout_width="wrap_content"
                   android:layout_height="wrap_content">
                   <ImageView
                       android:src="@drawable/list_nor"
                       android:layout_width="12dp"
                       android:layout_height="15dp"/>
               </RelativeLayout>
           </LinearLayout>
        </RelativeLayout>
    </LinearLayout>
</layout>