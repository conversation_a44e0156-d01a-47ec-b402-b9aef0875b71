<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="list_optin">
        <attr name="list_postion" format="string" />
    </declare-styleable>
    <declare-styleable name="list_CircularProgressView">
        <attr name="list_backWidth" format="dimension" />    <!--背景圆环宽度-->
        <attr name="list_progWidth" format="dimension" />    <!--进度圆环宽度-->
        <attr name="list_backColor" format="color" />        <!--背景圆环颜色-->
        <attr name="list_progColor" format="color" />        <!--进度圆环颜色-->
        <attr name="list_progStartColor" format="color" />   <!--进度圆环开始颜色-->
        <attr name="list_progFirstColor" format="color" />   <!--进度圆环结束颜色-->
        <attr name="list_progress" format="integer" />       <!--圆环进度-->
    </declare-styleable>
</resources>