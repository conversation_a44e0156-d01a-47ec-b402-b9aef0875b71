package com.zizhiguanjia.model_list.viewmodel;

import android.view.View;

import com.example.lib_common.base.CommonViewModel;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.model_list.api.ExamListServiceApi;
import com.zizhiguanjia.model_list.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_list.model.ExamDesBean;
import com.zizhiguanjia.model_list.navigator.ExamDesNavigator;

import java.util.HashMap;
import java.util.Map;

import androidx.databinding.ObservableField;

public class ExamDesViewModel extends CommonViewModel {
    private ExamDesNavigator examDesNavigator;
    public ObservableField<Integer> pageState=new ObservableField<>();

    public ObservableField<String> commonTitlteObs=new ObservableField<>();
    public ObservableField<String> commonDesObs=new ObservableField<>();
    public ObservableField<ExamDesBean> commonDesInfo=new ObservableField<>();
    private ExamListServiceApi mApi=new Http().create(ExamListServiceApi.class);


    public ObservableField<String> titleTipObs=new ObservableField<>();
    public ObservableField<ExamDesBean.PaperTipBean> paperTipObs=new ObservableField<>();
    public ObservableField<ExamDesBean.TestTipBean> testTipObs=new ObservableField<>();
    public void initParams(ExamDesNavigator examDesNavigator,String tkType, String title){
        this.examDesNavigator=examDesNavigator;
        initTopTitleParams(Integer.parseInt(tkType),title);
    }
    public void getData(String type,String ids){
        Map<String,String> params=new HashMap<>();
        params.put("paperType",type);
        if(ids != null) {
            params.put("paperValue", ids);
        }
        launchOnlyResult(mApi.getExamDes(params), new OnHandleException<BaseData<ExamDesBean>>() {
           @Override
           public void success(BaseData<ExamDesBean> data) {
               try {
                   commonDesInfo.set(data.Data);
                   String tes1="<b>提示：</b>";
                   String tex=data.Data.getTitleTip().replace("提示：",tes1);
                   examDesNavigator.setDesTips(tex);
                   testTipObs.set(data.Data.getTestTip());
                   paperTipObs.set(data.Data.getPaperTip());
               }catch (Exception e){
               }
           }

           @Override
           public void error(String msg) {
           }
       });
    }
    private void initTopTitleParams(int tkType, String title){
        if(tkType== ExamPaperTypeConfig.EXAM_PAPER_LNZT){
            pageState.set(1);
            examDesNavigator.showTopTitltView("历年真题");
        }else if(tkType== ExamPaperTypeConfig.EXAM_PAPER_MNZT){
            pageState.set(3);
            examDesNavigator.showTopTitltView("模拟考试");
        }else if(tkType== ExamPaperTypeConfig.EXAM_PAPER_ZJLX){
            pageState.set(2);
            examDesNavigator.showTopTitltView("章节练习");
            commonDesObs.set("章节专项练习为自由练习模式，您可根据个人需要选择自己想要练习的章节，每答一道题目会直接判断对错，答对题目直接跳转下一题，答错题目停留在当前试题；");
        }else if(tkType== ExamPaperTypeConfig.EXAM_PAPER_TKLX){
            pageState.set(2);
            examDesNavigator.showTopTitltView("题型练习");
            commonDesObs.set("题型练习为自由练习模式，每答一题会直接判断对错，答对直接跳转下一题，答错后停留在当前试题；");
        }else if(tkType== ExamPaperTypeConfig.EXAM_PAPER_SAVE){
            pageState.set(2);
            examDesNavigator.showTopTitltView("收藏夹");
        }else if(tkType== ExamPaperTypeConfig.EXAM_PAPER_ERROR){
            pageState.set(2);
            examDesNavigator.showTopTitltView("我的错题");
        }else if(tkType== ExamPaperTypeConfig.EXAM_PAPER_ESTIONS_TO_GET_WRONG){
            pageState.set(4);
            examDesNavigator.showTopTitltView(title);
        }else{
            pageState.set(2);
            examDesNavigator.showTopTitltView("题库");

        }
    }
    public void onclick(View view){
        if(examDesNavigator==null)return;
        examDesNavigator.goToExamPage();
    }
}
