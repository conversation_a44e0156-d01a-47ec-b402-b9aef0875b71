package com.zizhiguanjia.model_list.viewmodel;

import com.example.lib_common.base.CommonViewModel;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.emums.WebViewLoadTypeEnum;
import com.zizhiguanjia.lib_base.helper.CommonHelper;
import com.zizhiguanjia.model_list.api.ExamListServiceApi;
import com.zizhiguanjia.model_list.fragment.DocMajorsFragment;
import com.zizhiguanjia.model_list.model.DocMajorsBean;

import androidx.lifecycle.MutableLiveData;

/**
 * 功能作用： 学习资料页面ViewModel
 * 初始注释时间： 2023/12/18 16:54
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class DocMajorsViewModel extends CommonViewModel {
    /**
     * 页面实例
     */
    private DocMajorsFragment mFragment;
    private final ExamListServiceApi mApi = new Http().create(ExamListServiceApi.class);
    /**
     * 实际显示的数据
     */
    private final MutableLiveData<DocMajorsBean> mShowDataBean = new MutableLiveData<>();
    /**
     * 加载中处理
     */
    private final MutableLiveData<Boolean> mShowLoading = new MutableLiveData<>();

    public void initParams(DocMajorsFragment docMajorsFragment) {
        mFragment = docMajorsFragment;
    }

    /**
     * 跳转到资料详情页面
     */
    public void jumpToDetail(DocMajorsBean.Major item) {
        mFragment.initArguments().putString("title", item.getName());
        mFragment.initArguments().putSerializable("loadType", WebViewLoadTypeEnum.DOC_MAJORS);
        mFragment.initArguments().putString("params", String.valueOf(item.getId()));
        mFragment.startFragment(CommonHelper.showCommonWebViewHtmlTextLoad());
    }

    /**
     * 刷新数据
     */
    public void refreshData() {
        mShowLoading.postValue(true);
        launchOnlyResult(mApi.getExamDocMajors(), new OnHandleException<BaseData<DocMajorsBean>>() {
            @Override
            public void success(BaseData<DocMajorsBean> data) {
                mShowDataBean.postValue(data.Data);
                mShowLoading.postValue(false);
            }

            @Override
            public void error(String msg) {
                mShowDataBean.postValue(null);
                mShowLoading.postValue(false);
            }
        });
    }

    public MutableLiveData<DocMajorsBean> getShowDataBean() {
        return mShowDataBean;
    }

    public MutableLiveData<Boolean> getShowLoading() {
        return mShowLoading;
    }
}
