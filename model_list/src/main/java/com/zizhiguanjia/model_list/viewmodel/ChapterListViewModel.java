package com.zizhiguanjia.model_list.viewmodel;

import com.example.lib_common.base.CommonViewModel;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.msgconfig.PointCheckType;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.model_list.api.ExamListServiceApi;
import com.zizhiguanjia.model_list.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_list.model.ExamChapterBean;
import com.zizhiguanjia.model_list.model.ExamChapterGroupBean;
import com.zizhiguanjia.model_list.navigator.ChapterListNavigator;

import java.util.HashMap;
import java.util.Map;

import androidx.databinding.ObservableField;

public class ChapterListViewModel extends CommonViewModel {
    private ChapterListNavigator navigator;
    private ExamListServiceApi mApi = new Http().create(ExamListServiceApi.class);
    public ObservableField<Boolean> statePage = new ObservableField<>();
    ObservableField<ExamChapterBean> examChapterBeanObservableField = new ObservableField<>();


    public void initParams(ChapterListNavigator navigator) {
        this.navigator = navigator;
        statePage.set(false);
    }

    public void getChapterListData() {
        statePage.set(true);
        Map<String, String> params = new HashMap<>();
        params.put("paperType", String.valueOf(ExamPaperTypeConfig.EXAM_PAPER_ZJLX));
        launchOnlyResult(mApi.getExamChapterListData(params), new OnHandleException<BaseData<ExamChapterGroupBean>>() {
            @Override
            public void success(BaseData<ExamChapterGroupBean> data) {
                String masteryPercent = data.Data.getMasteryPercent();
                com.wb.lib_utils.utils.log.LogUtils.e("ViewModel获取到的MasteryPercent: " + masteryPercent);
                navigator.showMasteryDescription(masteryPercent);
                navigator.showChapterListData(data.Data.getList());
                navigator.showContentView();
            }

            @Override
            public void error(String msg) {
                navigator.showEmptyView();
            }
        });
    }

    public void checkRouth(boolean again) {
        if (PointHelper.checkPoinRouth(PointCheckType.CHECK_MAIN_HOMEBT_CHAPER)) {
            PointHelper.joinPointData(again?PointerMsgType.POINTER_A_PROBLEMPAGE_CHAPTER:PointerMsgType.POINTER_A_HOMEPAGE_STARTPRACTICE_CHAPTER, false);
        }
    }
    public void clearAllRecord() {
        Map<String, String> params = new HashMap<>();
        params.put("PaperType", String.valueOf(ExamPaperTypeConfig.EXAM_PAPER_ZJLX));
        params.put("Status", "1");
        navigator.showLoading(true, "清除中...");
        launchOnlyResult(mApi.clearErrorOrSaveData(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                getChapterListData();
            }
            @Override
            public void error(String msg) {
                navigator.showLoading(false, null);
            }
        });
    }
}
