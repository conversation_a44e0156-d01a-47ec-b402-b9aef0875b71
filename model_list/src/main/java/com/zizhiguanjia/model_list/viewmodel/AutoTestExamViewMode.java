package com.zizhiguanjia.model_list.viewmodel;

import android.app.Activity;
import android.graphics.drawable.Drawable;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.bumptech.glide.Glide;
import com.bumptech.glide.Priority;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.model_list.R;
import com.zizhiguanjia.model_list.api.ExamListServiceApi;
import com.zizhiguanjia.model_list.config.AutoTextExamConfig;
import com.zizhiguanjia.model_list.model.ExamDesBean;
import com.zizhiguanjia.model_list.navigator.AutoTestExamNavigator;

import java.util.HashMap;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.ObservableField;

public class AutoTestExamViewMode extends CommonViewModel {
    private AutoTestExamNavigator navigator;
    private ExamListServiceApi mApi = new Http().create(ExamListServiceApi.class);
    public ObservableField<Integer> canGoObs = new ObservableField<>();
    public ObservableField<Boolean> commonViewVist=new ObservableField<>();
    public void initPaprams(AutoTestExamNavigator autoTestExamNavigator,int type,String pagerType) {
        this.navigator = autoTestExamNavigator;
        canGoObs.set(AutoTextExamConfig.AUTO_EXAM_TEST_NEED_POST);
        commonViewVist.set(type==0?true:false);
        navigator.initLoadConfig();
        boolean isVip = UserHelper.isBecomeVip();
        if(!commonViewVist.get()){
            getPageInfo(pagerType);
        }else {
            if (!isVip) {
                getAutoTextExam(false);
            }
        }
    }
    public void  getPageInfo(String pagerType){
        Map<String,String> params=new HashMap<>();
        params.put("paperType", pagerType);
        launchOnlyResult(mApi.getExamDes(params), new OnHandleException<BaseData<ExamDesBean>>() {
            @Override
            public void success(BaseData<ExamDesBean> data) {
                if(data.getResult()==null){
                }else {
                    canGoObs.set(Integer.parseInt(data.getResult().getPaperTip().getIsCanDoPaper()));
                    navigator.showOtherAutoView(data.getResult().getPaperTip());
                }
            }

            @Override
            public void error(String msg) {
            }
        });
    }
    public void onClick(View mView) {
        if (mView.getId() == R.id.tvAutoExamTest) {
            checkOutPayPage();
        } else if (mView.getId() == R.id.relBack) {
            navigator.closeView();
        }
    }
    public void updataExamTime(String time){
        Map<String,String> params=new HashMap<>();
        params.put("examDate",time);
        navigator.showLoading(true);
        launchOnlyResult(mApi.updateExamTime(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                navigator.showLoading(false);
                navigator.updateExamTimeUpdate();
                if(StringUtils.isEmpty(data.getMsg()))return;
                ToastUtils.normal(data.getMsg(),Gravity.CENTER);
            }

            @Override
            public void error(String msg) {
                navigator.showLoading(false);
                if(StringUtils.isEmpty(msg))return;
                ToastUtils.normal(msg,Gravity.CENTER);
            }
        });
    }

    private void checkOutPayPage() {
        boolean isVip = UserHelper.isBecomeVip();
        LogUtils.e("看看vip状态" + isVip);
        if (!isVip) {
            if (canGoObs.get() == AutoTextExamConfig.AUTO_EXAM_TEST_NORMAL) {
                navigator.startAutoTestExam();
            } else if (canGoObs.get() == AutoTextExamConfig.AUTO_EXAM_TEST_NEED_PAY) {
                navigator.showNoBuyVipView();
            } else if (canGoObs.get() == AutoTextExamConfig.AUTO_EXAM_TEST_NEED_POST) {
                getAutoTextExam(true);
            } else if (canGoObs.get() == AutoTextExamConfig.AUTO_EXAM_TEST_NEED_VERSON) {
                ToastUtils.normal("App版本太低，使用该功能需要升级App！", Gravity.CENTER);
            } else if (canGoObs.get() == AutoTextExamConfig.AUTO_EXAM_TEST_NAB) {
                ToastUtils.normal("该功能正在维护，请稍后尝试！", Gravity.CENTER);
                getAutoTextExam(false);
            }
        } else {
            navigator.startAutoTestExam();
        }
    }

    private void getAutoTextExam(boolean show) {
        if (show) {
            navigator.showLoading(true);
        }
        launchOnlyResult(mApi.getAutoTextExam(new HashMap<>()), new OnHandleException<BaseData<String>>() {
            @Override
            public void success(BaseData<String> data) {
                if (show) {
                    navigator.showLoading(false);
                }
                try {
                    canGoObs.set(Integer.parseInt(data.getResult()));
                    if (show) {
                        checkOutPayPage();
                    }
                } catch (Exception e) {
                    canGoObs.set(AutoTextExamConfig.AUTO_EXAM_TEST_NEED_POST);
                }
            }

            @Override
            public void error(String msg) {
                if (show) {
                    navigator.showLoading(false);
                }
                ToastUtils.normal(msg, Gravity.CENTER);
                canGoObs.set(AutoTextExamConfig.AUTO_EXAM_TEST_NEED_POST);
            }
        });
    }
    public void reshImage(Activity activity, String url, ImageView imageView){
        RequestOptions options = new RequestOptions()
                .centerCrop()
                .placeholder(R.drawable.qst_img) //占位图
                .error(R.drawable.qst_img)       //错误图
                .priority(Priority.HIGH)
                .diskCacheStrategy(DiskCacheStrategy.ALL);
        Glide.with(activity).load(url)
                .apply(options)
                .into(new SimpleTarget<Drawable>() {
                    @Override
                    public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                        // 在这里可以获得图片的宽高
                        LogUtils.e("看图片大小----->>>>"+resource.getIntrinsicHeight());
                        MainThreadUtils.post(new Runnable() {
                            @Override
                            public void run() {
                                LinearLayout.LayoutParams params=new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                                int h=(DpUtils.getScreenWidth(activity)-DpUtils.dp2px(activity,15))*resource.getIntrinsicHeight()/resource.getIntrinsicWidth();
                                params.height=h;
                                imageView.setLayoutParams(params);
                                imageView.setImageDrawable(resource);
                            }
                        });
                    }
                });
    }
}
