package com.zizhiguanjia.model_list.viewmodel;

import com.example.lib_common.base.CommonViewModel;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.model_list.api.ExamListServiceApi;
import com.zizhiguanjia.model_list.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_list.listener.CommonExamListCall;
import com.zizhiguanjia.model_list.model.CommonListDataBean;

import java.util.HashMap;
import java.util.Map;

public class CommonExamListViewModel extends CommonViewModel {
    private ExamListServiceApi mApi=new Http().create(ExamListServiceApi.class);
    private CommonExamListCall commonExamListCall;


    public void initParams(CommonExamListCall commonExamListCall){
        this.commonExamListCall=commonExamListCall;
    }
    public void initTopTitle(int paperType){
        if(paperType== ExamPaperTypeConfig.EXAM_PAPER_MNZT){
            commonExamListCall.showTopTitle("模拟考试");
        }else if(paperType== ExamPaperTypeConfig.EXAM_PAPER_TKLX){
            commonExamListCall.showTopTitle("题型练习");
        }else if(paperType== ExamPaperTypeConfig.EXAM_PAPER_LNZT){
            commonExamListCall.showTopTitle("历年真题");
        }
    }
    public void getListData(String paperType){
        Map<String,String> params=new HashMap<>();
        params.put("paperType",paperType);
        launchOnlyResult(mApi.getCommonListData(params), new OnHandleException<BaseData<CommonListDataBean>>() {
            @Override
            public void success(BaseData<CommonListDataBean> data) {
                    if(commonExamListCall==null)return;
                    if(data==null||data.Data==null||data.Data.getItems()==null||data.Data.getItems().size()==0){
                        commonExamListCall.showEmptyView();
                    }else {
                        commonExamListCall.getCommonListData(data.Data.getItems());
                    }
            }

            @Override
            public void error(String msg) {
                commonExamListCall.showEmptyView();
            }
        });
    }
    public boolean checkUserState(String paperType,String paperValue){
        boolean isVip= UserHelper.isBecomeVip();
        if((Integer.parseInt(paperType)==ExamPaperTypeConfig.EXAM_PAPER_LNZT&&!isVip)||(Integer.parseInt(paperType)==ExamPaperTypeConfig.EXAM_PAPER_MNZT&&!isVip)){
            commonExamListCall.showNoBuyVipView();
            return false;
        }else {
            return true;
        }

    }
    public void clearAllRecord(String paperType) {
        Map<String, String> params = new HashMap<>();
        params.put("PaperType", paperType);
        params.put("Status", "1");
        commonExamListCall.showLoading(true);
        launchOnlyResult(mApi.clearErrorOrSaveData(params), new OnHandleException<BaseData>() {
            @Override
            public void success(BaseData data) {
                getListData(paperType);
            }
            @Override
            public void error(String msg) {
                commonExamListCall.showLoading(false);
            }
        });
    }
}
