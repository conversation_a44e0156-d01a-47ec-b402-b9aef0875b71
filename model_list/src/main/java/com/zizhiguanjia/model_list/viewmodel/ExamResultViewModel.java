package com.zizhiguanjia.model_list.viewmodel;

import android.view.View;

import com.example.lib_common.base.CommonViewModel;
import com.wb.lib_utils.utils.DataUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.lib_base.config.Http;
import com.zizhiguanjia.model_list.R;
import com.zizhiguanjia.model_list.api.ExamListServiceApi;
import com.zizhiguanjia.model_list.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_list.listener.ExamSheetDataListener;
import com.zizhiguanjia.model_list.model.ExamAnswerSheetBean;
import com.zizhiguanjia.model_list.model.ExamPackAccessDataBean;
import com.zizhiguanjia.model_list.model.ExamResultTagsBean;
import com.zizhiguanjia.model_list.navigator.ExamResultNavigator;
import com.zizhiguanjia.model_list.repository.ExamDataListRepository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringTokenizer;

import androidx.databinding.ObservableField;

public class ExamResultViewModel extends CommonViewModel {
    private ExamListServiceApi mApi=new Http().create(ExamListServiceApi.class);
    private ExamDataListRepository examDataListRepository;
    private ExamResultNavigator navigator;
    public ObservableField<String> questionNum=new ObservableField<>();
    public ObservableField<String> questionRight=new ObservableField<>();
    public ObservableField<String> questionError=new ObservableField<>();
    public ObservableField<String> questionDoNor=new ObservableField<>();
    public ObservableField<String> questionTitlteDes=new ObservableField<>();
    public ObservableField<String> questionScoreStr=new ObservableField<>();
    public ObservableField<Integer> questionScoreInt=new ObservableField<>();
    public ObservableField<String> questionRemind=new ObservableField<>();
    public ObservableField<Boolean> syylState=new ObservableField<>();
    public ExamCoreThemeConfigThemeBean themeBean = null;
    public ExamCoreThemeConfigTextSizeBean textSizeBean = null;
    public ExamCoreStyleConfigBean styleConfigBean;
    /**
     * 初始化主题数据
     */
    public void initThemeData(int paperType,String title) {
        if (styleConfigBean == null) {
            styleConfigBean = ExamCoreStyleConfigBean.getConfigInfoByStore(String.valueOf(paperType),title);
        }
        styleConfigBean.saveConfigInfoToStore(String.valueOf(paperType),title);
        ExamCoreThemeConfigThemeBean themeBeanInfo = styleConfigBean.getThemeBeanInfo();
        if (themeBeanInfo != null) {
            themeBean = themeBeanInfo;
        }
        ExamCoreThemeConfigTextSizeBean textSizeBeanInfo = styleConfigBean.getTextSizeBeanInfo();
        if (textSizeBeanInfo != null) {
            this.textSizeBean = textSizeBeanInfo;
        }
        navigator.changeShowThemeData(styleConfigBean, themeBean, textSizeBean);
    }
    public void initParams(ExamResultNavigator navigator, int papyType,String title){
        this.navigator=navigator;
        initThemeData(papyType,title);
        examDataListRepository=new ExamDataListRepository();
        examDataListRepository.init(examDataListener);
        if(papyType== ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM||papyType== ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS){
            syylState.set(false);
        }else {
            syylState.set(true);
        }

    }
    public void getExamResultTags(int paperType,String paperValue){
        Map<String,String> params=new HashMap<>();
        params.put("paperType",paperType+"");
        params.put("paperValue",paperValue+"");
        launchOnlyResult(mApi.getExamResultTags(params), new OnHandleException<BaseData<ExamResultTagsBean>>() {
            @Override
            public void success(BaseData<ExamResultTagsBean> data) {
                setTopQuestionCount(data.Data);
                navigator.showContentView();
                examDataListRepository.getExamSheetListData(paperType+"",paperValue+"",false,-1);
                try {
                    float a=Float.parseFloat(data.Data.getSource()+"")/Float.parseFloat(data.Data.getAllScore()+"");
                    navigator.showZdJd((int) (a*100));
                }catch (Exception e){
                    LogUtils.e(e.getMessage());
                    navigator.showZdJd(0);
                }
                String[] strings=convertStrToArray2(data.Data.getSource());
                navigator.showCurrntScoreP(strings[0]);
                if(strings[1].equals("0")){
                    navigator.showCurrntScoreC("");
                }else {
                    navigator.showCurrntScoreC(strings[1]);
                }
                if(data.getResult().isPopupScoreFrame()){
                   navigator.goToLearing(data.Data.getMorePercent());
                }
            }
            @Override
            public void error(String msg) {

            }
        });
    }
    public  String[] convertStrToArray2(String str){
        StringTokenizer st = new StringTokenizer(str,".");
        String[] strArray = new String[st.countTokens()];
        int i=0;
        while(st.hasMoreTokens()){
            strArray[i++] = st.nextToken().trim();
        }
        return strArray;
    }
    private void setTopQuestionCount(ExamResultTagsBean examResultTagsBean){
        questionNum.set(String.valueOf(examResultTagsBean.getAllCount()));
        questionRight.set(String.valueOf(examResultTagsBean.getRightCount()));
        questionError.set(String.valueOf(examResultTagsBean.getWrongCount()));
        questionDoNor.set(String.valueOf(examResultTagsBean.getNoWriteCount()));
        questionTitlteDes.set(examResultTagsBean.getScoreTip()==null||StringUtils.isEmpty(examResultTagsBean.getScoreTip().getTxt())?"":examResultTagsBean.getScoreTip().getTxt());
        double xy=Double.parseDouble(examResultTagsBean.getSource())/Double.parseDouble(examResultTagsBean.getAllScore()+"");
        int percent=DataUtils.stringToInt(String.valueOf(xy*100));
        questionScoreStr.set(String.valueOf(examResultTagsBean.getSource()));
        questionScoreInt.set(percent);
        questionRemind.set(StringUtils.isEmpty(examResultTagsBean.getTitleTip())?null:examResultTagsBean.getTitleTip());
        if(examResultTagsBean.getScoreTip()==null||StringUtils.isEmpty(examResultTagsBean.getScoreTip().getImage()))return;
        navigator.setScoreImageTags(examResultTagsBean.getScoreTip().getImage());
        navigator.changeShowThemeData(styleConfigBean,themeBean,textSizeBean);
    }
    private ExamSheetDataListener examDataListener =new  ExamSheetDataListener(){

        @Override
        public void getExamSheetDataListener(ExamAnswerSheetBean commonGroupExamBeanLists) {
            navigator.initSheetData(commonGroupExamBeanLists.getRecords());
        }
        @Override
        public void getExamListData(List<ExamPackAccessDataBean.RecordsBean> lists) {
        }

        @Override
        public void getReadPostHttpExam(int index) {

        }
        @Override
        public void getReadStartPostHttpExam(String qid) {

        }
    };
    public void onClick(View view){
        if(view.getId()== R.id.tvAlsy){
            if(navigator==null)return;
            navigator.showErrorAsyView();
        }else if(view.getId()==R.id.tvReStartExam){
            if(navigator==null)return;
            navigator.showRestExam();
        }

    }
}
