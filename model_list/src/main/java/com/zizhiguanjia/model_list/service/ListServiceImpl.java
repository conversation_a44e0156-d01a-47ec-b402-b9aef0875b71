package com.zizhiguanjia.model_list.service;

import android.content.Context;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.example.lib_common.arouter.ARouterUtils;
import com.wb.lib_arch.base.IFragment;
import com.zizhiguanjia.lib_base.constants.ListRouterPath;
import com.zizhiguanjia.lib_base.service.ListServie;

@Route(path = ListRouterPath.SERVICE)
public class ListServiceImpl implements ListServie {

    @Override
    public void start(Context context) {

    }

    @Override
    public IFragment mainPage(Context context) {
       return null;
    }

    @Override
    public IFragment showPage(Context context) {
        return null;
    }

    @Override
    public void init(Context context) {

    }

    @Override
    public IFragment toPageExamResult() {
        return ARouterUtils.navFragment(ListRouterPath.MAIN_EXAM_RESULT);
    }

    @Override
    public IFragment toPageExamList() {
        return ARouterUtils.navFragment(ListRouterPath.MAIN_EXAM_LIST);
    }
    @Override
    public IFragment toPageDocMajors() {
        return ARouterUtils.navFragment(ListRouterPath.MAIN_EXAM_DOC_MAJORS);
    }

    @Override
    public IFragment toPageExamDes() {
        return ARouterUtils.navFragment(ListRouterPath.MAIN_EXAM_DES);
    }

    @Override
    public IFragment toPageExamAuto() {
        return ARouterUtils.navFragment(ListRouterPath.MAIN_EXAM_AUTO);
    }

    @Override
    public IFragment showCommonListPage() {
        return ARouterUtils.navFragment(ListRouterPath.MAIN_EXAM_COMMONLIST);
    }
}
