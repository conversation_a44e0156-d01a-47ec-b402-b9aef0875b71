package com.zizhiguanjia.model_list.config;

public interface ExamPaperTypeConfig {
    //历年真题
    int EXAM_PAPER_LNZT=1;
    //模拟真题
    int EXAM_PAPER_MNZT=2;
    //章节练习
    int EXAM_PAPER_ZJLX=3;
    //题库练习
    int EXAM_PAPER_TKLX=4;
    //收藏
    int EXAM_PAPER_SAVE=6;
    //错题
    int EXAM_PAPER_ERROR=5;
    //自动组件
    int EXAM_PAPER_AUTOEXAM=7;
    //模拟考核，后端使用的type，首页的navtype会返回10，对应就是这个type11
    int EXAM_PAPER_AUTOEXAM_MNKS=11;
    //全部解析
    int EXAM_PAPER_ALSY=8;
    //错题解析
    int EXAM_PAPER_ALSYERROR=9;
    //易错100题
    int EXAM_PAPER_ESTIONS_TO_GET_WRONG=16;
    //摸底测评
    int EXAM_EvaluationReport=20;
}
