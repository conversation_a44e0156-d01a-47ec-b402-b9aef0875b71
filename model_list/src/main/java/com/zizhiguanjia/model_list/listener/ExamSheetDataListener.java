package com.zizhiguanjia.model_list.listener;


import com.zizhiguanjia.model_list.model.ExamAnswerSheetBean;
import com.zizhiguanjia.model_list.model.ExamPackAccessDataBean;

import java.util.List;

public interface ExamSheetDataListener {
    void getExamSheetDataListener(ExamAnswerSheetBean commonGroupExamBeanLists);
    void getExamListData(List<ExamPackAccessDataBean.RecordsBean> lists);
    void getReadPostHttpExam(int index);
    void getReadStartPostHttpExam(String qid);
}
