package com.zizhiguanjia.model_list.adapter;

import android.content.res.ColorStateList;
import android.graphics.Color;
import android.view.View;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.model_list.R;
import com.zizhiguanjia.model_list.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_list.databinding.ListExamSheetchildLayoutBinding;
import com.zizhiguanjia.model_list.model.AnswerSheetOptionBean;
import com.zizhiguanjia.model_list.navigator.ExamSheetCall;

public class ExamSheetChildAdapter extends BaseAdapter<AnswerSheetOptionBean> {
    private ListExamSheetchildLayoutBinding binding;
    private ExamSheetCall call;
    private int paperType;
    private int paperId;

    public ExamCoreThemeConfigThemeBean themeBean ;
    public ExamCoreThemeConfigTextSizeBean textSizeBean = null;
    public ExamCoreStyleConfigBean styleConfigBean;

    public void setPaperId(int paperId) {
        this.paperId = paperId;
    }

    public void setPaperType(int paperType) {
        this.paperType = paperType;
    }

    public ExamSheetChildAdapter(ExamSheetCall call) {
        super(R.layout.list_exam_sheetchild_layout);
        this.call = call;
    }

    public void setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.themeBean = themeBean;
        this.textSizeBean = textSizeBean;
        this.styleConfigBean = styleConfigBean;
        notifyItemRangeChanged(0,getItemCount());
    }

    @Override
    protected void bind(BaseViewHolder holder, AnswerSheetOptionBean item, int position) {
        binding = holder.getBinding();
        binding.setData(item);
        binding.setModel(this);
        if(themeBean == null){
            binding.tvExamSheetQuNum.setVisibility(View.GONE);
            binding.viewExamSheetQuNumBg.setVisibility(View.GONE);
            binding.viewExamSheetQuNumStore.setVisibility(View.GONE);
            return;
        }
        binding.tvExamSheetQuNum.setVisibility(View.VISIBLE);
        binding.viewExamSheetQuNumBg.setVisibility(View.VISIBLE);
        binding.viewExamSheetQuNumStore.setVisibility(View.VISIBLE);
        if (item.isSee()) {
            binding.viewExamSheetQuNumStore.setBackgroundResource(R.drawable.hollow_max);
            if ((paperType == ExamPaperTypeConfig.EXAM_PAPER_TKLX && paperId == 3) ||
                    paperType == ExamPaperTypeConfig.EXAM_PAPER_ERROR ||
                    paperType == ExamPaperTypeConfig.EXAM_PAPER_SAVE ||
                    paperType == ExamPaperTypeConfig.EXAM_PAPER_LNZT ||
                    paperType == ExamPaperTypeConfig.EXAM_PAPER_MNZT ||
                    paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM
                    || paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS ||
                    paperType == ExamPaperTypeConfig.EXAM_EvaluationReport) {
                if (item.isDone()) {
                    binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorDoing())));
                    binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorDoing())));
                    binding.tvExamSheetQuNum.setTextColor(Color.parseColor(themeBean.getAnswerResultItemTextColorSure()));
                } else {
                    binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorNo())));
                    binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorNo())));
                    binding.tvExamSheetQuNum.setTextColor(Color.parseColor(themeBean.getAnswerResultItemTextColorNo()));
                }
            } else {
                if (item.isDone()) {
                    //已做过
                    if (!getStateExam()) {
                        // 对于摸底测评等不显示对错的考试类型，使用统一的已做样式
                        binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorDoing())));
                        binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorDoing())));
                        binding.tvExamSheetQuNum.setTextColor(Color.parseColor(themeBean.getAnswerResultItemTextColorSure()));
                    } else {
                        if (item.isRight()) {
                            //正确
                            binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorSure())));
                            binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorSure())));
                            binding.tvExamSheetQuNum.setTextColor(Color.parseColor(themeBean.getAnswerResultItemTextColorSure()));
                        } else {
                            //错误
                            binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorError())));
                            binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorError())));
                            binding.tvExamSheetQuNum.setTextColor(Color.parseColor(themeBean.getAnswerResultItemTextColorError()));
                        }
                    }
                } else {
                    //未做过
                    binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorNo())));
                    binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorNo())));
                    binding.tvExamSheetQuNum.setTextColor(Color.parseColor(themeBean.getAnswerResultItemTextColorNo()));
                }
            }
        } else {
            //不能看
            binding.viewExamSheetQuNumBg.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorLuck())));
            binding.viewExamSheetQuNumStore.setBackgroundResource(R.drawable.list_exam_sheet_unlink);
            binding.viewExamSheetQuNumStore.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemTextColorLuck())));
        }
    }

    public void onClick(View view, int questionNum, boolean isSee) {
        if (call == null) return;
        call.onUserSelectSheetQuestionNum(questionNum, isSee);
    }

    private boolean getStateExam() {
        if (paperType == ExamPaperTypeConfig.EXAM_PAPER_MNZT || paperType == ExamPaperTypeConfig.EXAM_PAPER_LNZT || paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM || paperType == ExamPaperTypeConfig.EXAM_EvaluationReport) {
            return false;
        } else {
            return true;
        }
    }
}
