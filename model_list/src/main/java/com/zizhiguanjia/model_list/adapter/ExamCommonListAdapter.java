package com.zizhiguanjia.model_list.adapter;

import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_adapter.ItemDelegate;
import com.wb.lib_adapter.MultiItemTypeAdapter;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.log.IPrinter;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_list.R;
import com.zizhiguanjia.model_list.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_list.databinding.ListExamCommonExamItemBinding;
import com.zizhiguanjia.model_list.databinding.ListExamCommonlistMnksItemBinding;
import com.zizhiguanjia.model_list.listener.ExamCommonListCall;
import com.zizhiguanjia.model_list.model.CommonListDataBean;

public class ExamCommonListAdapter extends MultiItemTypeAdapter<CommonListDataBean.ItemsBean> {
    private ExamCommonListCall commonListCall;

    public ExamCommonListAdapter(int paperType, ExamCommonListCall commonListCall) {
        this.commonListCall = commonListCall;
        addItemDelegate(new ItemDelegate<CommonListDataBean.ItemsBean>() {
                             private ListExamCommonExamItemBinding examCommonExamItemBinding;
                            @Override
                            public int layoutId() {
                                return R.layout.list_exam_common_exam_item;
                            }

                            @Override
                            public boolean isThisType(CommonListDataBean.ItemsBean item, int position) {
                                return paperType == ExamPaperTypeConfig.EXAM_PAPER_TKLX ? true : false;
                            }

                            @Override
                            public void convert(BaseViewHolder holder, CommonListDataBean.ItemsBean item, int position) {
                                examCommonExamItemBinding = holder.getBinding();
                                examCommonExamItemBinding.setBean(item);
                                examCommonExamItemBinding.setGo(item.getDoneCount() == 0 ? false : true);
                                examCommonExamItemBinding.setPostion(position);
                                examCommonExamItemBinding.tvLookExam1.setSelected(item.getDoneCount() == 0 ? false : true);
                                RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(DpUtils.dp2px(holder.itemView.getContext(), 34), DpUtils.dp2px(holder.itemView.getContext(), 7));
                                params.setMargins(DpUtils.dp2px(holder.itemView.getContext(), item.getTitle().length() <= 3 ? 15 : 30), 0, 0, 0);
                                params.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
                                examCommonExamItemBinding.vLine.setLayoutParams(params);



                                if(item.isCanGo()){
                                    examCommonExamItemBinding.tvLookExam1.setVisibility(View.VISIBLE);
                                    examCommonExamItemBinding.canGoRel1.setVisibility(View.GONE);
                                }else {
                                    examCommonExamItemBinding.tvLookExam1.setVisibility(View.GONE);
                                    examCommonExamItemBinding.canGoRel1.setVisibility(View.VISIBLE);
                                }
                                examCommonExamItemBinding.tvLookExam1.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        if (commonListCall == null) return;
                                        if(item.isCanGo()){
                                            commonListCall.onExamListClick(String.valueOf(paperType), String.valueOf(item.getPaperId()),false);
                                        }else {
                                            commonListCall.onExamBoBuy(String.valueOf(paperType), String.valueOf(item.getPaperId()));
                                        }
                                    }
                                });
                                examCommonExamItemBinding.canGoRel1.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        if (commonListCall == null) return;
                                        commonListCall.onExamBoBuy(String.valueOf(paperType), String.valueOf(item.getPaperId()));
                                    }
                                });
                            }
                        }

        );
        addItemDelegate(new ItemDelegate<CommonListDataBean.ItemsBean>() {
            private ListExamCommonlistMnksItemBinding binding;
            @Override
            public int layoutId() {
                return R.layout.list_exam_commonlist_mnks_item;
            }

            @Override
            public boolean isThisType(CommonListDataBean.ItemsBean item, int position) {
                return paperType == ExamPaperTypeConfig.EXAM_PAPER_MNZT || paperType == ExamPaperTypeConfig.EXAM_PAPER_LNZT ? true : false;
            }

            @Override
            public void convert(BaseViewHolder holder, CommonListDataBean.ItemsBean item, int position) {
                binding = holder.getBinding();
                binding.setBean(item);
                binding.setPostion(position);
                binding.setGo(item.isIsSubmit()?true:(item.getDoneCount() == 0 ? true : false));
                binding.tvLookExam.setSelected(item.isIsSubmit()?false:(item.getDoneCount() == 0 ? false : true));
                if(item.isCanGo()){
                    binding.canGoRel.setVisibility(View.GONE);
                    binding.tvLookExam.setVisibility(View.VISIBLE);
                }else {
                    binding.canGoRel.setVisibility(View.VISIBLE);
                    binding.tvLookExam.setVisibility(View.GONE);
                }
                String txt="";
                if(item.isIsSubmit()){
                    txt="查看成绩单";
                }else {
                    if(item.getDoneCount()==0){
                        txt="开始答题";
                    }else {
                        txt="继续答题";
                    }
                }

                binding.tvLookExam.setText(txt);
                binding.tvLookExam.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (commonListCall == null) return;
                        if(item.isIsSubmit()){
                            commonListCall.onTopageResult(String.valueOf(paperType), String.valueOf(item.getPaperId()));
                        }else {
                            if(item.getDoneCount() == 0){
                                commonListCall.onExamListClick(String.valueOf(paperType), String.valueOf(item.getPaperId()),true);
                            }else {
                                commonListCall.onExamAgain(String.valueOf(paperType), String.valueOf(item.getPaperId()));
                            }
                        }
                    }
                });
                binding.canGoRel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        LogUtils.e("点击了1");
                        commonListCall.onExamBoBuy(String.valueOf(paperType), String.valueOf(item.getPaperId()));
                    }
                });
            }
        });
    }
}
