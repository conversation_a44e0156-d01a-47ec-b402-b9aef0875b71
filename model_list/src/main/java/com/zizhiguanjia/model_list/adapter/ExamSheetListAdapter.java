package com.zizhiguanjia.model_list.adapter;

import android.graphics.Color;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.lib_base.decoration.GridSpaceItemDecoration;
import com.zizhiguanjia.model_list.R;
import com.zizhiguanjia.model_list.databinding.ListExamSheetgroupLayoutBinding;
import com.zizhiguanjia.model_list.model.ExamChaptersBean;
import com.zizhiguanjia.model_list.navigator.ExamSheetCall;

import androidx.recyclerview.widget.GridLayoutManager;

public class ExamSheetListAdapter extends BaseAdapter<ExamChaptersBean> {
    private ListExamSheetgroupLayoutBinding binding;
    private ExamSheetCall call;
    private int paperType;
    private int paperId;

    public ExamCoreThemeConfigThemeBean themeBean ;
    public ExamCoreThemeConfigTextSizeBean textSizeBean = null;
    public ExamCoreStyleConfigBean styleConfigBean;
    public void setPaperId(int paperId) {
        this.paperId = paperId;
    }

    public void setPaperType(int paperType) {
        this.paperType = paperType;
    }

    public ExamSheetListAdapter(ExamSheetCall call) {
        super(R.layout.list_exam_sheetgroup_layout);
        this.call=call;
    }

    public void setThemeConfigData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        this.themeBean = themeBean;
        this.textSizeBean = textSizeBean;
        this.styleConfigBean = styleConfigBean;
        notifyItemRangeChanged(0,getItemCount());
    }

    @Override
    protected void bind(BaseViewHolder holder, ExamChaptersBean item, int position) {
        binding=holder.getBinding();
        binding.setBean(item);
        if(binding.rcyExamSheetGroup.getLayoutManager()==null){
            binding.rcyExamSheetGroup.setHasFixedSize(true);
            GridLayoutManager gridLayoutManager=new GridLayoutManager(holder.itemView.getContext(),5);
            gridLayoutManager.setInitialPrefetchItemCount(5);
            binding.rcyExamSheetGroup.setItemViewCacheSize(1000);
            binding.rcyExamSheetGroup.setLayoutManager(gridLayoutManager);
            binding.rcyExamSheetGroup.addItemDecoration(new GridSpaceItemDecoration(5, DpUtils.dp2px(holder.itemView.getContext(),20),DpUtils.dp2px(holder.itemView.getContext(),20)));
        }
        if(binding.rcyExamSheetGroup.getAdapter()==null){
            ExamSheetChildAdapter childAdapter=new ExamSheetChildAdapter(call);
            binding.rcyExamSheetGroup.setNestedScrollingEnabled(false);
            childAdapter.setPaperType(paperType);
            binding.rcyExamSheetGroup.setAdapter(childAdapter);
            childAdapter.setDataItems(item.getItems());
        }else {
            ((ExamSheetChildAdapter)  binding.rcyExamSheetGroup.getAdapter()).setDataItems(item.getItems());
        }
        ((ExamSheetChildAdapter)  binding.rcyExamSheetGroup.getAdapter()).setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        binding.tvTitle.setText(item.getQuestionName());
        if(themeBean != null) {
            binding.tvTitle.setTextColor(Color.parseColor(themeBean.getAnswerResultTitleTextColor()));
        }
    }
}
