package com.zizhiguanjia.model_list.adapter;

import android.view.ViewGroup;

import com.wb.lib_adapter.BaseAdapter;
import com.wb.lib_adapter.BaseViewHolder;
import com.zizhiguanjia.model_list.R;
import com.zizhiguanjia.model_list.model.DocMajorsBean;
import com.zizhiguanjia.model_list.viewmodel.DocMajorsViewModel;

import androidx.appcompat.widget.AppCompatTextView;

/**
 * 功能作用：学习资料列表适配器
 * 初始注释时间： 2023/12/18 17:08
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class DocMajorsAdapter extends BaseAdapter<DocMajorsBean.Major> {
    private DocMajorsViewModel mViewModel;

    public DocMajorsAdapter(DocMajorsViewModel model) {
        super(R.layout.item_list_doc_majors);
        mViewModel = model;
    }

    @Override
    protected void onViewHolderCreated(BaseViewHolder holder, ViewGroup parent, int viewType) {
        super.onViewHolderCreated(holder, parent, viewType);
        ViewGroup.LayoutParams params = holder.itemView.getLayoutParams();
        params.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        holder.itemView.setLayoutParams(params);
    }

    @Override
    protected void bind(BaseViewHolder holder, DocMajorsBean.Major item, int position) {
        ((AppCompatTextView) holder.itemView.findViewById(R.id.tv_text)).setText(item.getName());
        holder.itemView.setOnClickListener(v -> mViewModel.jumpToDetail(item));
    }
}
