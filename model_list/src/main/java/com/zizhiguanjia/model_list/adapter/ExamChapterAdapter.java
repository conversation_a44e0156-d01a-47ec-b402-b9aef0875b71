package com.zizhiguanjia.model_list.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.zizhiguanjia.model_list.R;
import com.zizhiguanjia.model_list.adapter.holder.ChildVH;
import com.zizhiguanjia.model_list.adapter.holder.GroupVH;
import com.zizhiguanjia.model_list.databinding.ListExamChapterChildLayoutBinding;
import com.zizhiguanjia.model_list.databinding.ListExamChapterGroupLayoutBinding;
import com.zizhiguanjia.model_list.model.ExamChapterBean;
import com.zizhiguanjia.model_list.navigator.ExamChapterCall;

import java.util.ArrayList;
import java.util.List;

import pokercc.android.expandablerecyclerview.ExpandableAdapter;

public class ExamChapterAdapter extends ExpandableAdapter<ExpandableAdapter.ViewHolder> {
    private List<ExamChapterBean> lists = new ArrayList<>();
    private ExamChapterCall chapterCall;
    private int openChapter=-1;

    public void setOpenChapter(int openChapter) {
        this.openChapter = openChapter;
    }

    public int isOpenChapter() {
        return openChapter;
    }

    public void addItem(List<ExamChapterBean> chapterBeans) {
        lists.clear();
        lists.addAll(chapterBeans);
        notifyDataSetChanged();
    }
    public ExamChapterBean getGroupItem(int i){
        if(lists==null||lists.size()==0)return null;
        return lists.get(i);
    }
    public ExamChapterAdapter(ExamChapterCall chapterCall) {
        this.chapterCall = chapterCall;
    }

    @Override
    public int getChildCount(int i) {
        try {
            return lists.get(i).getChilds().size();
        }catch (Exception e){
            return  0;
        }

    }

    @Override
    public int getGroupCount() {
        return lists.size();
    }

    @Override
    protected void onBindChildViewHolder(ExpandableAdapter.ViewHolder viewHolder, int i, int i1, List<?> list) {
        if (list.isEmpty()) {
            ExamChapterBean examChapterBean = lists.get(i);
            ExamChapterBean.ChildsBean childsBean = examChapterBean.getChilds().get(i1);
            ListExamChapterChildLayoutBinding examChapterChildLayoutBinding = ((ChildVH) viewHolder).itemBinding;

            examChapterChildLayoutBinding.tvChapterChildCount.setText("/" + childsBean.getExamCount() + "");
            examChapterChildLayoutBinding.tvChapterChildDo.setText(childsBean.getDoneExamCount() + "");
            examChapterChildLayoutBinding.tvChapterChildTitle.setText((i + 1) + "." + (i1 + 1) + " " + childsBean.getTitle());

            // 检查是否有第三层子项
            boolean hasSubChildren = childsBean.getChilds() != null && !childsBean.getChilds().isEmpty();

            if (hasSubChildren) {
                // 有第三层子项，显示标题旁的展开箭头和右侧跳转箭头
                examChapterChildLayoutBinding.imgChapterChildExpand.setVisibility(View.VISIBLE);
                examChapterChildLayoutBinding.imgChapterChildRight.setVisibility(View.VISIBLE);
                examChapterChildLayoutBinding.imgChapterChildExpand.setImageResource(R.drawable.list_exam_down_img);

                // 重置第三层的展开状态，确保重新绑定时状态正确
                examChapterChildLayoutBinding.llSubChapterContainer.setVisibility(View.GONE);

                // 设置第三层RecyclerView
                setupSubChapterRecyclerView(examChapterChildLayoutBinding, childsBean, i, i1);

                // 设置标题点击事件 - 展开收起第三层
                examChapterChildLayoutBinding.llTitleContainer.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        // 切换第三层的显示/隐藏
                        toggleSubChapterVisibility(examChapterChildLayoutBinding);
                    }
                });

                // 设置右侧箭头点击事件 - 跳转到刷题页面
                examChapterChildLayoutBinding.imgChapterChildRight.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (childsBean.isCanGo()) {
                            chapterCall.onExamChapterCall(childsBean.getExamCode() + "", false);
                        } else {
                            chapterCall.onExamChapterFail(childsBean.getExamCode() + "");
                        }
                    }
                });
            } else {
                // 没有第三层子项，隐藏标题旁的展开箭头，显示右侧跳转箭头
                examChapterChildLayoutBinding.imgChapterChildExpand.setVisibility(View.GONE);
                examChapterChildLayoutBinding.imgChapterChildRight.setVisibility(View.VISIBLE);
                examChapterChildLayoutBinding.llSubChapterContainer.setVisibility(View.GONE);

                // 设置标题点击事件 - 直接跳转到刷题页面
                examChapterChildLayoutBinding.llTitleContainer.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (childsBean.isCanGo()) {
                            chapterCall.onExamChapterCall(childsBean.getExamCode() + "", false);
                        } else {
                            chapterCall.onExamChapterFail(childsBean.getExamCode() + "");
                        }
                    }
                });

                // 设置右侧箭头点击事件 - 跳转到刷题页面
                examChapterChildLayoutBinding.imgChapterChildRight.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (childsBean.isCanGo()) {
                            chapterCall.onExamChapterCall(childsBean.getExamCode() + "", false);
                        } else {
                            chapterCall.onExamChapterFail(childsBean.getExamCode() + "");
                        }
                    }
                });
            }

            if (i1 == 0) {
                examChapterChildLayoutBinding.timeline.initLine(1);
            } else if (i1 == getChildCount(i) - 1) {
                examChapterChildLayoutBinding.timeline.initLine(2);
            } else {
                examChapterChildLayoutBinding.timeline.initLine(0);
            }
            examChapterChildLayoutBinding.timeline.setMarkerInCenter(false);
        }
    }

    @Override
    protected void onBindGroupViewHolder(ViewHolder viewHolder, int i, boolean b, List<?> list) {
        if (list.isEmpty()) {
            ExamChapterBean examChapterBean = lists.get(i);
            ListExamChapterGroupLayoutBinding examChapterChildLayoutBinding = ((GroupVH) viewHolder).itemBinding;

            examChapterChildLayoutBinding.tvChapterGroupCount.setText("/" + examChapterBean.getExamCount());
            examChapterChildLayoutBinding.tvChapterGroupDoCount.setText(examChapterBean.getDoneExamCount() + "");
            // 修复角标问题：确保角标始终正确显示
            examChapterChildLayoutBinding.tvChapterGroupNum.setText((i + 1) + " ");
            examChapterChildLayoutBinding.tvChapterGroupTitle.setText(examChapterBean.getTitle());
//            examChapterChildLayoutBinding.tvChapterDes.setText(examChapterBean.getRemark());
            examChapterChildLayoutBinding.tvRight.setText("正确率：" + examChapterBean.getRightRate());
            examChapterChildLayoutBinding.imgRight.setVisibility(getChildCount(i)==0?View.GONE:View.VISIBLE);

            // 根据展开状态设置背景和图标
            examChapterChildLayoutBinding.relGroupMain.setBackgroundResource(b ? R.drawable.list_exam_bottom_bg : R.drawable.list_exam_chapter_bg);
            examChapterChildLayoutBinding.imgRight.setImageResource(b ? R.drawable.list_exam_up_img : R.drawable.list_exam_down_img);

            examChapterChildLayoutBinding.imgEditExam.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //章节 ----章点击
                    if (examChapterBean.isCanGo()) {
                        chapterCall.onExamChapterCall(examChapterBean.getExamCode() + "",true);
                    } else {
                        chapterCall.onExamChapterFail(examChapterBean.getExamCode() + "");
                    }

                }
            });
        }
    }

    @Override
    protected ViewHolder onCreateChildViewHolder(ViewGroup viewGroup, int i) {
        LayoutInflater inflater = LayoutInflater.from(viewGroup.getContext());
        ListExamChapterChildLayoutBinding examChapterGroupLayoutBinding = ListExamChapterChildLayoutBinding.inflate(inflater, viewGroup, false);
        return new ChildVH(examChapterGroupLayoutBinding);
    }

    @Override
    protected ViewHolder onCreateGroupViewHolder(ViewGroup viewGroup, int i) {
        LayoutInflater inflater = LayoutInflater.from(viewGroup.getContext());
        ListExamChapterGroupLayoutBinding examChapterGroupLayoutBinding = ListExamChapterGroupLayoutBinding.inflate(inflater, viewGroup, false);
        return new GroupVH(examChapterGroupLayoutBinding);
    }

    @Override
    protected void onGroupViewHolderExpandChange(ViewHolder viewHolder, int i, long l, boolean b) {
        if (chapterCall != null) {
            if (b) {
                chapterCall.openChapter(i);
                openChapter=i;
            } else {
                openChapter=-1;
                chapterCall.closeChapter(i);
            }
        }
        if(getChildCount(i)==0)
            return;
        if (viewHolder instanceof GroupVH) {
            ListExamChapterGroupLayoutBinding examChapterGroupLayoutBinding = ((GroupVH) viewHolder).itemBinding;
            examChapterGroupLayoutBinding.relGroupMain.setBackgroundResource(b ? R.drawable.list_exam_bottom_bg : R.drawable.list_exam_chapter_bg);
            if (b) {
                examChapterGroupLayoutBinding.imgRight.setImageResource(R.drawable.list_exam_up_img);
                examChapterGroupLayoutBinding.tvChapterDes.setVisibility(View.GONE);
            } else {
                examChapterGroupLayoutBinding.imgRight.setImageResource(R.drawable.list_exam_down_img);
                examChapterGroupLayoutBinding.tvChapterDes.setVisibility(View.GONE);
            }
        }
    }

    /**
     * 设置第三层子章节的RecyclerView
     */
    private void setupSubChapterRecyclerView(ListExamChapterChildLayoutBinding binding,
                                           ExamChapterBean.ChildsBean childsBean,
                                           int groupIndex, int childIndex) {
        // 每次都创建新的适配器，避免角标错误
        ExamSubChapterAdapter subAdapter = new ExamSubChapterAdapter(chapterCall, groupIndex, childIndex);
        binding.rvSubChapter.setLayoutManager(new LinearLayoutManager(binding.getRoot().getContext()));
        binding.rvSubChapter.setAdapter(subAdapter);
        binding.rvSubChapter.setNestedScrollingEnabled(false);

        // 设置数据
        subAdapter.setSubChildList(childsBean.getChilds());
    }

    /**
     * 切换第三层子章节的显示/隐藏状态
     */
    private void toggleSubChapterVisibility(ListExamChapterChildLayoutBinding binding) {
        if (binding.llSubChapterContainer.getVisibility() == View.VISIBLE) {
            // 当前是展开状态，收起
            binding.llSubChapterContainer.setVisibility(View.GONE);
            binding.imgChapterChildExpand.setImageResource(R.drawable.list_exam_down_img);
        } else {
            // 当前是收起状态，展开
            binding.llSubChapterContainer.setVisibility(View.VISIBLE);
            binding.imgChapterChildExpand.setImageResource(R.drawable.list_exam_up_img);
        }
    }

}
