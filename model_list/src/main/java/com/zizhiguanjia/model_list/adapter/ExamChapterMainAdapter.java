package com.zizhiguanjia.model_list.adapter;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;

import com.zizhiguanjia.model_list.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_list.fragment.ChapterListFragment;
import com.zizhiguanjia.model_list.fragment.CommonExamListFragment;

public class ExamChapterMainAdapter extends FragmentStatePagerAdapter {
    public ExamChapterMainAdapter(@NonNull FragmentManager fm) {
        super(fm);
    }

    @NonNull
    @Override
    public Fragment getItem(int position) {
        return position==0? CommonExamListFragment.newInstance(ExamPaperTypeConfig.EXAM_PAPER_TKLX,false):new ChapterListFragment();
    }

    @Override
    public int getCount() {
        return 2;
    }
}
