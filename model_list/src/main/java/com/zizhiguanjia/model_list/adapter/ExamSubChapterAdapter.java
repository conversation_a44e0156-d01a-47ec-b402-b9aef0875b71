package com.zizhiguanjia.model_list.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.zizhiguanjia.model_list.adapter.holder.SubChildVH;
import com.zizhiguanjia.model_list.databinding.ListExamChapterSubchildLayoutBinding;
import com.zizhiguanjia.model_list.model.ExamChapterBean;
import com.zizhiguanjia.model_list.navigator.ExamChapterCall;

import java.util.ArrayList;
import java.util.List;

public class ExamSubChapterAdapter extends RecyclerView.Adapter<SubChildVH> {
    private List<ExamChapterBean.ChildsBean> subChildList = new ArrayList<>();
    private ExamChapterCall chapterCall;
    private int groupIndex;
    private int childIndex;

    public ExamSubChapterAdapter(ExamChapterCall chapterCall, int groupIndex, int childIndex) {
        this.chapterCall = chapterCall;
        this.groupIndex = groupIndex;
        this.childIndex = childIndex;
    }

    public void setSubChildList(List<ExamChapterBean.ChildsBean> subChildList) {
        this.subChildList.clear();
        if (subChildList != null) {
            this.subChildList.addAll(subChildList);
        }
        notifyDataSetChanged();
    }

    /**
     * 更新组索引和子索引，用于正确显示角标
     */
    public void updateIndices(int groupIndex, int childIndex) {
        this.groupIndex = groupIndex;
        this.childIndex = childIndex;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public SubChildVH onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        ListExamChapterSubchildLayoutBinding binding = ListExamChapterSubchildLayoutBinding.inflate(inflater, parent, false);
        return new SubChildVH(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull SubChildVH holder, int position) {
        ExamChapterBean.ChildsBean subChild = subChildList.get(position);
        ListExamChapterSubchildLayoutBinding binding = holder.itemBinding;

        // 设置标题
        binding.tvChapterSubChildTitle.setText((groupIndex + 1) + "." + (childIndex + 1) + "." + (position + 1) + " " + subChild.getTitle());
        
        // 设置完成数量和总数量
        binding.tvChapterSubChildDo.setText(subChild.getDoneExamCount() + "");
        binding.tvChapterSubChildCount.setText("/" + subChild.getExamCount());

        // 第三层不需要时间轴，已在布局中移除

        // 设置整体点击事件
        binding.relExamSubChapter.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (subChild.isCanGo()) {
                    chapterCall.onExamChapterCall(subChild.getExamCode() + "", false);
                } else {
                    chapterCall.onExamChapterFail(subChild.getExamCode() + "");
                }
            }
        });

    }

    @Override
    public int getItemCount() {
        return subChildList.size();
    }
}
