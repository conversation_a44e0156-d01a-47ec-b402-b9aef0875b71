package com.zizhiguanjia.model_list.dialog;

import android.content.Context;

import com.wb.lib_pop.code.CenterPopupView;
import com.zizhiguanjia.model_list.R;

import androidx.annotation.NonNull;

/**
 * 功能作用：清除答题记录弹窗
 * 初始注释时间： 2023/12/22 13:42
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
public class ExamClearRecordDialog extends CenterPopupView {
    private OnClickListener mConfirmClick;
    public ExamClearRecordDialog(@NonNull Context context, OnClickListener onClickListener) {
        super(context);
        mConfirmClick = onClickListener;
    }
    @Override
    protected int initLayoutResId() {
        return R.layout.list_dialog_exam_clear_record;
    }

    @Override
    protected void initPopupContent() {
        super.initPopupContent();
        findViewById(R.id.btn_cancel).setOnClickListener(v -> dismiss());
        findViewById(R.id.iv_close).setOnClickListener(v -> dismiss());
        findViewById(R.id.btn_confirm).setOnClickListener(v -> mConfirmClick.onClick(v));
    }
}
