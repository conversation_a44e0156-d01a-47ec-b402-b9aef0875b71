package com.zizhiguanjia.model_list.fragment;

import android.os.Bundle;
import android.text.Html;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.constants.ListRouterPath;
import com.zizhiguanjia.lib_base.helper.CoreExamHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.model_list.R;
import com.zizhiguanjia.model_list.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_list.databinding.ListExamDesLayoutBinding;
import com.zizhiguanjia.model_list.navigator.ExamDesNavigator;
import com.zizhiguanjia.model_list.viewmodel.ExamDesViewModel;
//
@BindRes( statusBarStyle= BarStyle.LIGHT_CONTENT,swipeBack = SwipeStyle.NONE)
@Route(path = ListRouterPath.MAIN_EXAM_DES)
public class ExamDesFragment extends BaseFragment implements ExamDesNavigator, TitleBar.OnTitleBarListener {
    private ListExamDesLayoutBinding binding;

    @BindViewModel
    ExamDesViewModel examDesViewModel;
    @Override
    public int initLayoutResId() {
        return R.layout.list_exam_des_layout;
    }
    private String title,paperType,paperId;
    private boolean restart;
    @Override
    public void initView(Bundle savedInstanceState) {
        binding=getBinding();
        binding.setModel(examDesViewModel);
        title=initArguments().getString("title","知识点练习");
        restart=initArguments().getBoolean("restart",false);
        paperType=initArguments().getString("paperType", String.valueOf(ExamPaperTypeConfig.EXAM_PAPER_ZJLX));
        paperId=initArguments().getString("paperId");
        examDesViewModel.initParams(this,paperType,title);
        binding.tbTopTitle.setClickListener(this);
        if(Integer.parseInt(paperType)==ExamPaperTypeConfig.EXAM_PAPER_LNZT||Integer.parseInt(paperType)==ExamPaperTypeConfig.EXAM_PAPER_MNZT){
            examDesViewModel.getData(paperType,paperId);
        }

    }

    @Override
    public void onVisible() {
        super.onVisible();
        if (Integer.parseInt(paperType) == ExamPaperTypeConfig.EXAM_PAPER_ESTIONS_TO_GET_WRONG) {
           postAtTime(() -> examDesViewModel.getData(paperType, paperId),500);
        }
    }

    @Override
    public void initViewData() {

    }

    @Override
    public void initObservable() {

    }

    @Override
    public void showTopTitltView(String msg) {
        examDesViewModel.commonTitlteObs.set(msg);
        binding.tbTopTitle.getCenterTextView().setText(msg);
    }

    @Override
    public void showCommonView() {
        binding.icbExamCommonDes.tvExamCommonDes.setText("");
        binding.icbExamCommonDes.tvExamCommonTitle.setText("题型练习");
    }

    @Override
    public void showChapterView() {

    }

    @Override
    public void goToExamPage() {
        if(Integer.parseInt(paperType)==ExamPaperTypeConfig.EXAM_PAPER_ESTIONS_TO_GET_WRONG) {
            if (examDesViewModel.commonDesInfo.get() != null && examDesViewModel.commonDesInfo.get().getPaperTip() != null &&
                    !examDesViewModel.commonDesInfo.get().getPaperTip().isCanGo()) {
                MessageHelper.openNoPremissBuyDialog(getActivity(), false, PayRouthConfig.PAY_EXAM_PAPER_ESTIONS_TO_GET_WRONG);
                return;
            }
        }
        initArguments().putString("title",title);
        initArguments().putBoolean("restart",restart);
        initArguments().putString("paperType", paperType);
        initArguments().putString("paperId",paperId);
        startFragment(CoreExamHelper.mainPage(getContext()));
        if(Integer.parseInt(paperType)==ExamPaperTypeConfig.EXAM_PAPER_TKLX){
            KvUtils.save("tklx","1");
        }else if(Integer.parseInt(paperType)==ExamPaperTypeConfig.EXAM_PAPER_ZJLX){
            KvUtils.save("zjlx","1");
        }
        if(Integer.parseInt(paperType) != ExamPaperTypeConfig.EXAM_PAPER_ESTIONS_TO_GET_WRONG) {
            finish();
        }
    }

    @Override
    public void setDesTips(String des) {
        binding.icbExamChapterDes.desTips.setText(Html.fromHtml(des));
    }

    @Override
    public void onClicked(View v, int action) {
        if(action==TitleBar.ACTION_LEFT_BUTTON){
            finish();
        }
    }
}
