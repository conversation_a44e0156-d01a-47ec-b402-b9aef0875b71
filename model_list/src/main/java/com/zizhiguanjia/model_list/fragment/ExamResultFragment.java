package com.zizhiguanjia.model_list.fragment;

import android.content.res.ColorStateList;
import android.graphics.Color;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.baselib.utils.SpanUtil;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_image_loadcal.ImageManager;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.lib_base.constants.ListRouterPath;
import com.zizhiguanjia.lib_base.helper.CoreExamHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.PayHelper;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.model_list.R;
import com.zizhiguanjia.model_list.adapter.ExamSheetListAdapter;
import com.zizhiguanjia.model_list.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_list.databinding.ListExamResultLayoutBinding;
import com.zizhiguanjia.model_list.model.ExamChaptersBean;
import com.zizhiguanjia.model_list.navigator.ExamResultNavigator;
import com.zizhiguanjia.model_list.navigator.ExamSheetCall;
import com.zizhiguanjia.model_list.viewmodel.ExamResultViewModel;

import java.util.List;

import androidx.recyclerview.widget.LinearLayoutManager;

@BindRes( statusBarStyle= BarStyle.LIGHT_CONTENT,swipeBack = SwipeStyle.NONE)
@Route(path = ListRouterPath.MAIN_EXAM_RESULT)
public class ExamResultFragment extends BaseFragment implements ExamResultNavigator, ExamSheetCall, TitleBar.OnTitleBarListener {
    @BindViewModel
    ExamResultViewModel model;
    private ListExamResultLayoutBinding binding;
    private ExamSheetListAdapter adapter;
    private int paperType;
    private String paperValue;
    @Override
    public int initLayoutResId() {
        return R.layout.list_exam_result_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        binding=getBinding();
        binding.setModel(model);
        binding.msvExamResult.showLoading();
        paperType=initArguments().getInt("paperType");
        try {
            paperValue = initArguments().getString("paperValue", "");
            if (paperValue == null || paperValue.isEmpty()) {
                paperValue = String.valueOf(initArguments().getInt("paperValue"));
            }
        } catch (Exception e) {
            if (paperValue == null || paperValue.isEmpty()) {
                paperValue = String.valueOf(initArguments().getInt("paperValue"));
            }
        }
        String title = paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM || paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS ? "模拟考试" :
                (paperType == ExamPaperTypeConfig.EXAM_PAPER_MNZT ? "模拟考试" : "历年真题");
        model.initParams(this,paperType,title);
        showTopTitleView(title);
        model.getExamResultTags(paperType,paperValue);
        adapter=new ExamSheetListAdapter(this);
        binding.ttbExamTitle.setClickListener(this);
        SpannableStringBuilder spannableStringBuilder=new SpanUtil.SpanBuilder()
                .addForeColorSection("60", Color.parseColor("#3163F6"))
                .addAbsSizeSection(".0", DpUtils.sp2px(getContext(),15)).getSpanStrBuilder();
        binding.icdExamResultContent.tvCurrentExam.setText(spannableStringBuilder);

    }
    @Override
    public void initViewData() {
        binding.icdExamResultContent.icbRcyExamResult.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.icdExamResultContent.icbRcyExamResult.setAdapter(adapter);
    }
    @Override
    public void initObservable() {

    }

    @Override
    public void initSheetData(List<ExamChaptersBean> examChaptersBeans) {
        adapter.setDataItems(examChaptersBeans);
        binding.msvExamResult.showContent();
    }

    @Override
    public void setScoreImageTags(String url) {
        if(StringUtils.isEmpty(url))return;
        ImageManager.getInstance().displayImage(url,binding.icdExamResultContent.imgExamResultTopTags);
    }

    @Override
    public void showContentView() {
        binding.msvExamResult.showContent();
    }

    @Override
    public void showErrorAsyView() {
        initArguments().putString("title","错题解析");
        initArguments().putBoolean("restart",false);
        initArguments().putString("paperType", String.valueOf(ExamPaperTypeConfig.EXAM_PAPER_ALSYERROR));
        initArguments().putString("paperId",String.valueOf(paperValue));

        startFragment(CoreExamHelper.mainPage(getActivity()));
    }

    @Override
    public void showTopTitleView(String msg) {
        binding.ttbExamTitle.getCenterTextView().setText(msg);
    }

    @Override
    public void showRestExam() {
        initArguments().putString("title",paperType==ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM || paperType == ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM_MNKS?"模拟考试":(paperType==ExamPaperTypeConfig.EXAM_PAPER_MNZT?"模拟考试":"历年真题"));
        initArguments().putString("paperId",String.valueOf(paperValue));
        initArguments().putString("paperType", String.valueOf(paperType));
        initArguments().putBoolean("restart",true);
        startFragment(new ExamDesFragment());
    }

    @Override
    public void showZdJd(int p) {
        LogUtils.e("测试结果"+p);
        binding.icdExamResultContent.cpvScollViews.setProgress(p,1000);
    }

    @Override
    public void showCurrntScoreP(String p) {
        binding.icdExamResultContent.tvCurrentExam.setText(p);
    }

    @Override
    public void showCurrntScoreC(String p) {
        binding.icdExamResultContent.tvExamChildInfo.setText(StringUtils.isEmpty(p)?"分":("."+p+"分"));
    }

    @Override
    public void goToLearing(String time) {
        MessageHelper.openLearningPlanDialog(getActivity(),time,2);
    }

    @Override
    public void changeShowThemeData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean themeBean,
            ExamCoreThemeConfigTextSizeBean textSizeBean) {
        if(themeBean == null){
            return;
        }
        binding.ttbExamTitle.setStatusBarColor(Color.parseColor(themeBean.getToolsBarBgColor()));
        binding.ttbExamTitle.setBackgroundColor(Color.parseColor(themeBean.getToolsBarBgColor()));
        binding.ttbExamTitle.getCenterTextView().setTextColor(Color.parseColor(themeBean.getToolsBarTextColor()));
        binding.ttbExamTitle.getLeftImageButton().setImageTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getToolsBarTextColor())));
        binding.icdExamResultContent.lnTopCard.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerPageCardBgColor())));
        binding.icdExamResultContent.lnBottomCard.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerPageCardBgColor())));
        binding.icdExamResultContent.llBottomView.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerPageCardBgColor())));
        binding.icdExamResultContent.tvCount1.setTextColor(Color.parseColor(themeBean.getAnswerPageCountTextColor()));
        binding.icdExamResultContent.tvCount2.setTextColor(Color.parseColor(themeBean.getAnswerPageCountTextColor()));
        binding.icdExamResultContent.tvCount3.setTextColor(Color.parseColor(themeBean.getAnswerPageCountTextColor()));
        binding.icdExamResultContent.tvCount4.setTextColor(Color.parseColor(themeBean.getAnswerPageCountTextColor()));
        binding.icdExamResultContent.tvCountTip1.setTextColor(Color.parseColor(themeBean.getAnswerPageCountTipTextColor()));
        binding.icdExamResultContent.tvCountTip2.setTextColor(Color.parseColor(themeBean.getAnswerPageCountTipTextColor()));
        binding.icdExamResultContent.tvCountTip3.setTextColor(Color.parseColor(themeBean.getAnswerPageCountTipTextColor()));
        binding.icdExamResultContent.tvCountTip4.setTextColor(Color.parseColor(themeBean.getAnswerPageCountTipTextColor()));
        binding.icdExamResultContent.tvDes.setTextColor(Color.parseColor(themeBean.getAnswerPageDesTextColor()));
        binding.icdExamResultContent.tvBottomCardTitle.setTextColor(Color.parseColor(themeBean.getAnswerResultTitleTextColor()));
        binding.icdExamResultContent.tvStateTipSure.setTextColor(Color.parseColor(themeBean.getAnswerResultTipTextColor()));
        binding.icdExamResultContent.tvStateTipError.setTextColor(Color.parseColor(themeBean.getAnswerResultTipTextColor()));
        binding.icdExamResultContent.tvStateTipNo.setTextColor(Color.parseColor(themeBean.getAnswerResultTipTextColor()));

        //标记样式调整
        binding.icdExamResultContent.ivTagUnDo.setBackgroundResource(R.drawable.solid_max);
        binding.icdExamResultContent.ivTagUnDo.setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemBgColorNo())));
        binding.icdExamResultContent.ivTagUnDo.setImageResource(R.drawable.hollow_max);
        binding.icdExamResultContent.ivTagUnDo.setImageTintList(ColorStateList.valueOf(Color.parseColor(themeBean.getAnswerResultItemStoreColorNo())));

        if(adapter != null){
            adapter.setThemeConfigData(styleConfigBean,themeBean,textSizeBean);
        }
    }

    @Override
    public void onUserSelectSheetQuestionNum(int questionNum, boolean isSee) {
        if(isSee){
            initArguments().putString("title","查看解析");
            initArguments().putBoolean("restart",false);
            initArguments().putString("paperType", String.valueOf(ExamPaperTypeConfig.EXAM_PAPER_ALSY));
            initArguments().putString("paperId",String.valueOf(paperValue));
            initArguments().putInt("location",questionNum);
            startFragment(CoreExamHelper.mainPage(getActivity()));
        }else {
            MessageHelper.openGeneralCentDialog(getActivity(), "提示", "您当前为试用版，若您选择继续刷题需要进行升级操作，是否继续？", "取消", "确定", false, true, new GeneralDialogListener() {
                @Override
                public void onCancel() {
                }
                @Override
                public void onConfim() {
                    startFragment(PayHelper.toPageMain(getContext()));
                    finish();
                }

                /**
                 *
                 */
                @Override
                public void onDismiss() {

                }
            });
        }
    }

    @Override
    public void onUserSelectRestartExam(boolean restart) {
        initArguments().putString("title",binding.ttbExamTitle.getCenterTextView().getText().toString());
        initArguments().putString("paperType", String.valueOf(ExamPaperTypeConfig.EXAM_PAPER_ERROR));
        initArguments().putString("paperId",String.valueOf(paperValue));
        initArguments().putBoolean("restart",true);
        startFragment(CoreExamHelper.mainPage(getActivity()));
    }

    @Override
    public void onClicked(View v, int action) {
        if(action==TitleBar.ACTION_LEFT_BUTTON){
            finish();
        }
    }
}
