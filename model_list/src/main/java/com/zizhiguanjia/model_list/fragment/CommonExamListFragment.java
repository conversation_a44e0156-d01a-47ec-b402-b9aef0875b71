package com.zizhiguanjia.model_list.fragment;

import android.os.Bundle;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_refresh_layout.api.RefreshLayout;
import com.wb.lib_refresh_layout.listener.OnRefreshListener;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.constants.ListRouterPath;
import com.zizhiguanjia.lib_base.helper.CoreExamHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.utils.DebounceUtils;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.listeners.GeneralDialogListener;
import com.zizhiguanjia.lib_base.msgconfig.AccountMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.DataMsgTypeConfig;
import com.zizhiguanjia.model_list.R;
import com.zizhiguanjia.model_list.adapter.ExamCommonListAdapter;
import com.zizhiguanjia.model_list.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_list.databinding.ListExamCommonListdataLayoutBinding;
import com.zizhiguanjia.model_list.dialog.ExamClearRecordDialog;
import com.zizhiguanjia.model_list.listener.CommonExamListCall;
import com.zizhiguanjia.model_list.listener.ExamCommonListCall;
import com.zizhiguanjia.model_list.model.CommonListDataBean;
import com.zizhiguanjia.model_list.viewmodel.CommonExamListViewModel;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

@Route(path = ListRouterPath.MAIN_EXAM_COMMONLIST)
@BindRes( statusBarStyle= BarStyle.LIGHT_CONTENT,swipeBack = SwipeStyle.NONE)
public class CommonExamListFragment extends BaseFragment implements CommonExamListCall, ExamCommonListCall, TitleBar.OnTitleBarListener, OnRefreshListener {
    private BasePopupView mClearRecordDialog;

    public static CommonExamListFragment newInstance(int paperType,boolean titleState){
        CommonExamListFragment commonExamListFragment=new CommonExamListFragment();
        commonExamListFragment.initArguments().putInt("paperType",paperType);
        commonExamListFragment.initArguments().putBoolean("titleState",titleState);
        return commonExamListFragment;
    }
    private ListExamCommonListdataLayoutBinding binding;
    @BindViewModel
    CommonExamListViewModel commonExamListViewModel;
    private ExamCommonListAdapter adapter;
    private int paperType;
    private boolean titleState;
    @Override
    public int initLayoutResId() {
        return R.layout.list_exam_common_listdata_layout;
    }
    @Override
    public void initView(Bundle savedInstanceState) {
        binding=getBinding();
        paperType=initArguments().getInt("paperType",0);
        titleState=initArguments().getBoolean("titleState",true);
        commonExamListViewModel.initParams(this);
        commonExamListViewModel.initTopTitle(paperType);
        binding.mlstvCommonListView.showLoading();
        adapter=new ExamCommonListAdapter(paperType,this);
        binding.tbContent.setClickListener(this);
        binding.icdeCommonContent.mainRefreshLayout.setEnableLoadMore(false);
        binding.icdeCommonContent.mainRefreshLayout.setOnRefreshListener(this);
        //清除记录
        ExamClearRecordDialog recordDialog = new ExamClearRecordDialog(getContext(),new View.OnClickListener(){
            @Override
            public void onClick(View v) {
                commonExamListViewModel.clearAllRecord(String.valueOf(paperType));
                mClearRecordDialog.dismiss();
            }
        });
        mClearRecordDialog = new PopupManager.Builder(getContext()).autoDismiss(false).dismissOnBackPressed(true).autoFocusEditText(false).enableDrag(
                false).dismissOnTouchOutside(true).hasShadowBg(true).asCustom(recordDialog);
        binding.tvClear.setVisibility(paperType == ExamPaperTypeConfig.EXAM_PAPER_TKLX && UserHelper.isBecomeVip()  ? View.VISIBLE : View.GONE);
        binding.tvClear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mClearRecordDialog.isShow()) {
                    mClearRecordDialog.dismiss();
                }
                mClearRecordDialog.show();
            }
        });
    }
    @Override
    public void initViewData() {
        binding.icdeCommonContent.rcycvCommonList.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.icdeCommonContent.rcycvCommonList.setAdapter(adapter);
    }

    @Override
    public void initObservable() {
        Bus.observe(this, new Observer<MsgEvent>() {
            @Override
            public void onChanged(MsgEvent msgEvent) {
                if(msgEvent.getCode()== DataMsgTypeConfig.DATA_UPLOADDATA_SUCCESS){
                    onVisible();
                }else  if(msgEvent.getCode()== AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_OUT){
                    finish();
                }else if(msgEvent.getCode()== DataMsgTypeConfig.DATA_UPLOADDATA_POP_SUCCESS2){
                    LogUtils.e("看看弹出的------>>>>>");
                    String time=msgEvent.getMsg();
                    MessageHelper.openLearningPlanDialog(getActivity(),time,1);
                }
            }
        });
    }
    @Override
    public void getCommonListData(List<CommonListDataBean.ItemsBean> lists) {
        binding.icdeCommonContent.mainRefreshLayout.finishRefresh();
        adapter.setDataItems(lists);
        binding.mlstvCommonListView.showContent();
    }

    /**
     * 加载中
     *
     * @param show
     */
    @Override
    public void showLoading(boolean show) {
        if(show){
            binding.mlstvCommonListView.showLoading();
        }else{
            binding.mlstvCommonListView.showContent();
        }
    }

    @Override
    public void showTopTitle(String title) {
        binding.tbContent.getCenterTextView().setText(title);
        binding.tbContent.setVisibility(titleState? View.VISIBLE:View.GONE);
    }

    @Override
    public void showEmptyView() {
        binding.mlstvCommonListView.showEmpty();
    }

    @Override
    public void showNoBuyVipView() {
        LogUtils.e("需要支付了----->>>>");
        String params= PayRouthConfig.PAY_EXTWEB_PAY;
        if((paperType)== ExamPaperTypeConfig.EXAM_PAPER_TKLX){
            initArguments().putString("title","题型练习");
            params=PayRouthConfig.PAY_COMMONTK_PAY;
        }else if((paperType)==ExamPaperTypeConfig.EXAM_PAPER_LNZT){
            initArguments().putString("title","历年真题");
            params=PayRouthConfig.PAY_LISTL_NZT_PAY;
        }else if((paperType)==ExamPaperTypeConfig.EXAM_PAPER_MNZT){
            initArguments().putString("title","模拟考试");
            params=PayRouthConfig.PAY_LIST_MNKS_PAY;
        }else {
            initArguments().putString("title","测试");
            params=PayRouthConfig.PAY_EXTWEB_PAY;
        }
//        new PopupManager.Builder(getContext()).maxHeight(DpUtils.dp2px(getContext(),436/825)).asCustom(new NewNoBuyDialog(getContext(), new OnConfirmListener() {
//            @Override
//            public void onConfirm() {
//                LogUtils.e("模拟考试-2222222-----购买");
//                Bus.post(new MsgEvent(PayMsgTypeConfig.PAY_MSG_SUCCESS));
//            }
//        },getActivity(),false,params)).show();
        // 防抖动：防止重复调用购买对话框
        String debounceKey = "openNoPremissBuyDialog_" + this.getClass().getSimpleName();
        if (!DebounceUtils.shouldExecute(debounceKey, 2000)) {
            LogUtils.e("购买对话框调用被防抖动拦截");
            return;
        }
        MessageHelper.openNoPremissBuyDialog(getActivity(),false,params);
//        MessageHelper.openNoPremissBuyDialog(homeFragment.getActivity(), true, PayRouthConfig.PAY_ERROR_PAY);
    }

    @Override
    public void onExamListClick(String paperType, String paperValue,boolean rest) {
        LogUtils.e("准备跳转2222");
            if(Integer.parseInt(paperType)== ExamPaperTypeConfig.EXAM_PAPER_TKLX){
                initArguments().putString("title","题型练习");
            }else if(Integer.parseInt(paperType)==ExamPaperTypeConfig.EXAM_PAPER_LNZT){
                initArguments().putString("title","历年真题");
            }else if(Integer.parseInt(paperType)==ExamPaperTypeConfig.EXAM_PAPER_MNZT){
                initArguments().putString("title","模拟考试");
            }else {
                initArguments().putString("title","测试");
            }
            initArguments().putString("paperType",paperType);
            initArguments().putBoolean("restart",rest);
            initArguments().putString("paperId",paperValue);
            if(Integer.parseInt(paperType)==ExamPaperTypeConfig.EXAM_PAPER_TKLX){
                String str= KvUtils.get("tklx","");
                if(StringUtils.isEmpty(str)){
                    startFragment(new ExamDesFragment());
                }else {
                    startFragment(CoreExamHelper.mainPage(getContext()));
                }
            }else if (Integer.parseInt(paperType)==ExamPaperTypeConfig.EXAM_PAPER_LNZT||Integer.parseInt(paperType)==ExamPaperTypeConfig.EXAM_PAPER_MNZT){
                if(rest){
                    startFragment(new ExamDesFragment());
                }else {
                    startFragment(CoreExamHelper.mainPage(getContext()));
                }

            }
    }
    private String paperValue;
    @Override
    public void onExamBoBuy(String paperType, String paperValue) {
        this.paperValue=paperValue;
        this.paperType=Integer.parseInt(paperType);
        showNoBuyVipView();
    }

    @Override
    public void onExamAgain(String paperType, String paperValue) {
        MessageHelper.openGeneralCentDialog(getActivity(), "提示", "是否继续答题?", "重新开始", "继续答题", false, true, new GeneralDialogListener() {
            @Override
            public void onCancel() {
                onExamListClick(paperType,paperValue,true);
            }
            @Override
            public void onConfim() {
                onExamListClick(paperType,paperValue,false);
            }

            /**
             *
             */
            @Override
            public void onDismiss() {

            }
        });
    }

    @Override
    public void onTopageResult(String paperType, String paperValue) {
        initArguments().putInt("paperType",Integer.parseInt(paperType));
        initArguments().putInt("paperValue",Integer.parseInt(paperValue));
        startFragment(new ExamResultFragment());
    }

    @Override
    public void onClicked(View v, int action) {
        if(action==TitleBar.ACTION_LEFT_BUTTON){
            finish();
        }
    }
    @Override
    public void onVisible() {
        commonExamListViewModel.getListData(String.valueOf(paperType));
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        LogUtils.e("刷新了----->>>>");
        commonExamListViewModel.getListData(String.valueOf(paperType));
    }
}
