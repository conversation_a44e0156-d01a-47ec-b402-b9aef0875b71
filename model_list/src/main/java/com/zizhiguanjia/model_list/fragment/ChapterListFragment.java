package com.zizhiguanjia.model_list.fragment;

import android.os.Bundle;
import android.view.Gravity;
import android.view.View;

import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.code.BasePopupView;
import com.wb.lib_refresh_layout.api.RefreshLayout;
import com.wb.lib_refresh_layout.listener.OnRefreshListener;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_rxtools.subsciber.SimpleThrowableAction;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.ToastUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.helper.CoreExamHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.msgconfig.AccountMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.DataMsgTypeConfig;
import com.zizhiguanjia.lib_base.msgconfig.PointCheckType;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.model_list.R;
import com.zizhiguanjia.model_list.adapter.ExamChapterAdapter;
import com.zizhiguanjia.model_list.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_list.databinding.ListExamChapterlistLayoutBinding;
import com.zizhiguanjia.model_list.dialog.ExamClearRecordDialog;
import com.zizhiguanjia.model_list.listener.StickCall;
import com.zizhiguanjia.model_list.model.ExamChapterBean;
import com.zizhiguanjia.model_list.navigator.ChapterListNavigator;
import com.zizhiguanjia.model_list.navigator.ExamChapterCall;
import com.zizhiguanjia.model_list.viewmodel.ChapterListViewModel;

import java.util.List;
import java.util.concurrent.TimeUnit;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import io.reactivex.functions.Consumer;

@BindRes( statusBarStyle= BarStyle.LIGHT_CONTENT,swipeBack = SwipeStyle.NONE)
public class ChapterListFragment extends BaseFragment implements ChapterListNavigator, ExamChapterCall, OnRefreshListener {
    private ListExamChapterlistLayoutBinding binding;
    private ExamChapterAdapter mAdapter;
    private int itemHeight=100;
    @BindViewModel
    ChapterListViewModel model;
    private String currentId;
    //    private boolean isChapterGroup=false;
    private BasePopupView mClearRecordDialog;


    @Override
    public int initLayoutResId() {
        return R.layout.list_exam_chapterlist_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        binding=getBinding();
        binding.msvExamChapter.showLoading();
        model.initParams(this);
        mAdapter=new ExamChapterAdapter(this);
        mAdapter.setOnlyOneGroupExpand(true);
        binding.icdContent.llFloatView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                binding.icdContent.llFloatView.setVisibility(View.GONE);

                binding.icdContent.ervExamChapter.smoothScrollBy(0,  moveToPosition(mAdapter.isOpenChapter()));
            }
        });
        binding.icdContent.imgEditExam.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onExamChapterCall(mAdapter.getGroupItem(mAdapter.isOpenChapter()).getExamCode(),true);
            }
        });
        PointHelper.addPoinRouth(PointerMsgType.POINTER_HOME_START_CHAPTER);
        model.checkRouth(false);
        binding.icdContent.mainRefreshLayout.setEnableLoadMore(false);
        binding.icdContent.mainRefreshLayout.setOnRefreshListener(this);
        //清除记录
        ExamClearRecordDialog recordDialog = new ExamClearRecordDialog(getContext(),new View.OnClickListener(){
            @Override
            public void onClick(View v) {
                model.clearAllRecord();
                mClearRecordDialog.dismiss();
            }
        });
        mClearRecordDialog = new PopupManager.Builder(getContext()).autoDismiss(false).dismissOnBackPressed(true).autoFocusEditText(false).enableDrag(
                false).dismissOnTouchOutside(true).hasShadowBg(true).asCustom(recordDialog);
        binding.tvClear.setVisibility(UserHelper.isBecomeVip() ? View.VISIBLE : View.GONE);
        binding.tvClear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mClearRecordDialog.isShow()) {
                    mClearRecordDialog.dismiss();
                }
                mClearRecordDialog.show();
            }
        });
    }

    @Override
    public void onDestroy() {
        PointHelper.clernPointRouth(PointCheckType.CHECK_MAIN_CHAPER_TO_HOMEBT);
        super.onDestroy();
    }

    public int moveToPosition(int position) {
        int firstItem = binding.icdContent.ervExamChapter.getChildLayoutPosition(binding.icdContent.ervExamChapter.getChildAt(0));
        int lastItem = binding.icdContent.ervExamChapter.getChildLayoutPosition(binding.icdContent.ervExamChapter.getChildAt(binding.icdContent.ervExamChapter.getChildCount() - 1));
        if (position < firstItem||position>lastItem) {
            try {
                binding.icdContent.ervExamChapter.smoothScrollToPosition(position);
            }catch (Exception e){
            }

        } else {
            int movePosition = position - firstItem;
            try {
                int top = binding.icdContent.ervExamChapter.getChildAt(movePosition).getTop();
                return top;
            }catch (Exception e){

            }
            return 0;
        }
        return 0;
    }

    public int  jsPostion(int position){
        int firstItem = binding.icdContent.ervExamChapter.getChildLayoutPosition(binding.icdContent.ervExamChapter.getChildAt(0));
        int lastItem = binding.icdContent.ervExamChapter.getChildLayoutPosition(binding.icdContent.ervExamChapter.getChildAt(binding.icdContent.ervExamChapter.getChildCount() - 1));
        if (position < firstItem||position>lastItem) {
            return position;
        }else {
            int movePosition = position - firstItem;
            int top = binding.icdContent.ervExamChapter.getChildAt(movePosition).getTop();
            return top;
        }
    }
    @Override
    public void initViewData() {
        binding.icdContent.ervExamChapter.setAdapter(mAdapter);
    }
    @Override
    public void initObservable() {
        binding.icdContent.slhlExam.setStickCall(new StickCall() {
            @Override
            public void showGoView(boolean vv,int dy) {
                if(!hdOver)return;
                if(vv){
                    MainThreadUtils.post(new Runnable() {
                        @Override
                        public void run() {
                            if(mAdapter==null)return;
                            if(mAdapter.isOpenChapter()==-1){
                                binding.icdContent.llFloatView.setVisibility(View.GONE);
                            }else {
                                if((dy-(itemHeight*mAdapter.isOpenChapter()))>=(jsPostion(mAdapter.isOpenChapter()))){
                                    binding.icdContent.llFloatView.setVisibility(View.VISIBLE);
                                }else {
                                    binding.icdContent.llFloatView.setVisibility(View.GONE);
                                }
                            }

                        }
                    });
                }else {
                    MainThreadUtils.post(new Runnable() {
                        @Override
                        public void run() {
                            binding.icdContent.llFloatView.setVisibility(View.GONE);
                        }
                    });
                }
            }
        });
        Bus.observe(this, new Observer<MsgEvent>() {
            @Override
            public void onChanged(MsgEvent msgEvent) {
                if(msgEvent.getCode()== AccountMsgTypeConfig.MSG_ACCOUNT_LOGIN_OUT){
                    LogUtils.e("-----账号退出-----********");
                    finish();
                }else if(msgEvent.getCode()== DataMsgTypeConfig.DATA_UPLOADDATA_POP_SUCCESS1){
                    String time=msgEvent.getMsg();
                    MessageHelper.openLearningPlanDialog(getActivity(),time,1);
                }
            }
        });
    }
    @Override
    public void showChapterListData(List<ExamChapterBean> chapterBeans) {
        binding.icdContent.mainRefreshLayout.finishRefresh();
        String x = chapterBeans.get(0).getChilds().get(0).getChilds().toString();
        mAdapter.addItem(chapterBeans);
    }
    @Override
    public void showContentView() {
        binding.msvExamChapter.showContent();
    }
    @Override
    public void showEmptyView() {
        binding.msvExamChapter.showEmpty();
    }

    @Override
    public void showFloatView(ExamChapterBean chapterBean) {

    }

    /**
     * 显示加载中
     */
    @Override
    public void showLoading(boolean show, String s) {
        if(show){
            binding.msvExamChapter.showLoading();
        }else {
            binding.msvExamChapter.showContent();
        }
    }

    @Override
    public void onExamChapterCall(String chapterId,boolean group) {
//        this.isChapterGroup=group;
        initArguments().putString("title","知识点练习");
        initArguments().putBoolean("restart",false);
        initArguments().putString("paperType", String.valueOf(ExamPaperTypeConfig.EXAM_PAPER_ZJLX));
        initArguments().putString("paperId",chapterId);
        String str= KvUtils.get("zjlx","");
        if(StringUtils.isEmpty(str)){
            startFragment(new ExamDesFragment());
        }else {
            startFragment(CoreExamHelper.mainPage(getContext()));
        }
    }

    @Override
    public void onExamChapterFail(String msg) {
        if(!UserHelper.isBecomeVip()){
            //弹出vip
            this.currentId=msg;
//            new PopupManager.Builder(getContext()).maxHeight(DpUtils.dp2px(getContext(),436/825)).asCustom(new NewNoBuyDialog(getContext(), new OnConfirmListener() {
//                @Override
//                public void onConfirm() {
//                    initArguments().putString("url", ConfigHelper.getPayUrl());
//                    initArguments().putInt("payType",1);
//                    startFragment(CommonHelper.showCommonWeb());
//                }
//            },getActivity(),false, PayRouthConfig.PAY_CHAPTER_PAY)).show();
            MessageHelper.openNoPremissBuyDialog(getActivity(),false, PayRouthConfig.PAY_CHAPTER_PAY);
        }else {
            ToastUtils.normal("该章节暂无试题！", Gravity.CENTER);
        }

    }
    boolean hdOver=true;
    @Override
    public void openChapter(int num) {
        LogUtils.e("------>>>点击了-----"+num+"*****"+mAdapter.isOpenChapter());
        hdOver=false;
        binding.icdContent.tvChapterGroupNum.setText((num+1)+ " ");
        binding.icdContent.llFloatView.setVisibility(View.GONE);
        ExamChapterBean examChapterBean=mAdapter.getGroupItem(num);
        binding.setData(examChapterBean);
        RxJavaUtils.delay(500, TimeUnit.MILLISECONDS, new Consumer<Long>() {
            @Override
            public void accept(Long aLong) throws Exception {
                LogUtils.e("开始滑动");
                binding.icdContent.ervExamChapter.smoothScrollBy(0,  moveToPosition(num));
                RxJavaUtils.delay(500, TimeUnit.MILLISECONDS, new Consumer<Long>() {
                    @Override
                    public void accept(Long aLong) throws Exception {
                        hdOver=true;
                    }
                }, new SimpleThrowableAction(TAG));

            }
        }, new SimpleThrowableAction(TAG));
    }

    @Override
    public void closeChapter(int num) {
        binding.icdContent.llFloatView.setVisibility(View.GONE);
    }

    @Override
    public void onVisible() {
        if(model.statePage.get()){
            model.checkRouth(true);
        }
        model.getChapterListData();
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        model.getChapterListData();
    }

    @Override
    public void showMasteryDescription(String masteryPercent) {
        LogUtils.e("showMasteryDescription 接收到的数据: " + masteryPercent);

        // 先显示UI，确保布局正常
        binding.icdContent.llMasteryDescription.setVisibility(View.VISIBLE);

        if (masteryPercent != null && !masteryPercent.isEmpty()) {
            parseMasteryDescription(masteryPercent);
        } else {
            // 即使数据为空，也显示默认值进行测试
            LogUtils.e("MasteryPercent 为空，显示默认测试数据");
            binding.icdContent.tvTotalCount.setText("80");
            binding.icdContent.tvMasteredCount.setText("20");
            binding.icdContent.tvMasteryPercent.setText("25%");
        }
    }

    /**
     * 解析知识点掌握情况描述
     * 格式示例: "合计80个知识点，已掌握20个，知识点掌握度25%"
     */
    private void parseMasteryDescription(String masteryPercent) {
        try {
            LogUtils.e("MasteryPercent原始数据: " + masteryPercent);

            // 测试实际数据格式: "合计30个知识点，已掌握 7 个，知识点掌握度23.3%"
            String testData = "合计30个知识点，已掌握 7 个，知识点掌握度23.3%";
            LogUtils.e("测试数据: " + testData);

            // 如果MasteryPercent只是百分比数字，我们需要从其他地方获取总数和已掌握数
            if (masteryPercent.matches("[\\d.]+%?")) {
                // 只有百分比，使用默认值或从其他地方计算
                binding.icdContent.tvTotalCount.setText("30");
                binding.icdContent.tvMasteredCount.setText("7");
                binding.icdContent.tvMasteryPercent.setText(masteryPercent.endsWith("%") ? masteryPercent : masteryPercent + "%");
                return;
            }

            // 尝试多种正则表达式模式，支持小数百分比
            String[] patterns = {
                "合计(\\d+)个知识点，已掌握\\s*(\\d+)\\s*个，知识点掌握度([\\d.]+)%",
                "合计(\\d+)个知识点.*已掌握\\s*(\\d+)\\s*个.*掌握度([\\d.]+)%",
                "总计(\\d+).*掌握\\s*(\\d+)\\s*.*掌握度([\\d.]+)%",
                "(\\d+).*知识点.*\\s*(\\d+)\\s*.*掌握.*([\\d.]+)%"
            };

            boolean matched = false;
            for (String patternStr : patterns) {
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(patternStr);
                java.util.regex.Matcher matcher = pattern.matcher(masteryPercent);

                if (matcher.find()) {
                    String totalCount = matcher.group(1);
                    String masteredCount = matcher.group(2);
                    String percent = matcher.group(3) + "%";

                    binding.icdContent.tvTotalCount.setText(totalCount);
                    binding.icdContent.tvMasteredCount.setText(masteredCount);
                    binding.icdContent.tvMasteryPercent.setText(percent);
                    matched = true;
                    LogUtils.e("匹配成功，模式: " + patternStr);
                    break;
                }
            }

            if (!matched) {
                // 如果所有模式都不匹配，尝试提取所有数字（包括小数）
                java.util.regex.Pattern numberPattern = java.util.regex.Pattern.compile("([\\d.]+)");
                java.util.regex.Matcher numberMatcher = numberPattern.matcher(masteryPercent);
                java.util.List<String> numbers = new java.util.ArrayList<>();
                while (numberMatcher.find()) {
                    numbers.add(numberMatcher.group(1));
                }

                LogUtils.e("提取到的数字: " + numbers.toString());

                if (numbers.size() >= 3) {
                    binding.icdContent.tvTotalCount.setText(numbers.get(0));
                    binding.icdContent.tvMasteredCount.setText(numbers.get(1));
                    binding.icdContent.tvMasteryPercent.setText(numbers.get(2) + "%");
                    LogUtils.e("使用数字提取方式成功: " + numbers.get(0) + ", " + numbers.get(1) + ", " + numbers.get(2));
                } else if (numbers.size() == 1) {
                    // 可能只有百分比
                    binding.icdContent.tvTotalCount.setText("30");
                    binding.icdContent.tvMasteredCount.setText("7");
                    binding.icdContent.tvMasteryPercent.setText(numbers.get(0) + "%");
                    LogUtils.e("只找到一个数字，当作百分比处理: " + numbers.get(0));
                } else {
                    // 完全解析失败，显示原始文本
                    binding.icdContent.tvTotalCount.setText("30");
                    binding.icdContent.tvMasteredCount.setText("7");
                    binding.icdContent.tvMasteryPercent.setText(masteryPercent);
                    LogUtils.e("解析失败，显示原始文本: " + masteryPercent);
                }
            }
        } catch (Exception e) {
            LogUtils.e("解析异常: " + e.getMessage());
            // 异常处理，显示原始数据
            binding.icdContent.tvTotalCount.setText("--");
            binding.icdContent.tvMasteredCount.setText("--");
            binding.icdContent.tvMasteryPercent.setText(masteryPercent != null ? masteryPercent : "--");
        }
    }
}
