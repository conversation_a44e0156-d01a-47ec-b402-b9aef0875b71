package com.zizhiguanjia.model_list.fragment;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.annotations.BindViewModel;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.constants.ListRouterPath;
import com.zizhiguanjia.model_list.R;
import com.zizhiguanjia.model_list.adapter.DocMajorsAdapter;
import com.zizhiguanjia.model_list.databinding.ListExamDocMajorsLayoutBinding;
import com.zizhiguanjia.model_list.viewmodel.DocMajorsViewModel;

import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;

/**
 * 功能作用：学习资料页面
 * 初始注释时间： 2023/12/18 16:44
 * 创建人：王亮（Loren）
 * 思路：
 * 方法：
 * 注意：
 * 修改人：
 * 修改时间：
 * 备注：
 *
 * <AUTHOR>
 */
@Route(path = ListRouterPath.MAIN_EXAM_DOC_MAJORS)
public class DocMajorsFragment extends BaseFragment {

    private ListExamDocMajorsLayoutBinding mBinding;
    @BindViewModel
    DocMajorsViewModel mViewModel;
    /**
     * 列表适配器
     */
    private DocMajorsAdapter mAdapter;

    @Override
    public int initLayoutResId() {
        return R.layout.list_exam_doc_majors_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        mBinding = getBinding();
        mViewModel.initParams(this);
        mBinding.tbTopTitle.getLeftImageButton().setOnClickListener(v -> finish());
        mAdapter = new DocMajorsAdapter(mViewModel);
        mBinding.rvList.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rvList.setAdapter(mAdapter);
        DividerItemDecoration itemDecoration = new DividerItemDecoration(requireContext(), DividerItemDecoration.VERTICAL);
        itemDecoration.setDrawable(new ColorDrawable(Color.parseColor("#F4F4F4")));
        mBinding.rvList.addItemDecoration(itemDecoration);
        mViewModel.refreshData();
    }

    @Override
    public void initObservable() {
        super.initObservable();
        mViewModel.getShowDataBean().observe(this, docMajorsBean -> {
            if (docMajorsBean != null) {
                if(docMajorsBean.getMajors().isEmpty()){
                    mBinding.mlstvCommonListView.showEmpty();
                }else {
                    mBinding.mlstvCommonListView.showContent();
                    mAdapter.setDataItems(docMajorsBean.getMajors());
                }
            }
        });
        mViewModel.getShowLoading().observe(this, state -> {
            if (state) {
                mBinding.mlstvCommonListView.showLoading();
            } else {
                if (mViewModel.getShowDataBean().getValue() != null) {
                    mBinding.mlstvCommonListView.showContent();
                } else {
                    mBinding.mlstvCommonListView.showEmpty();
                }
            }
        });
    }
}
