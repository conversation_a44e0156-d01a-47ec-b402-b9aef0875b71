package com.zizhiguanjia.model_list.fragment;

import static com.zizhiguanjia.lib_base.msgconfig.PayMsgTypeConfig.PAY_MSG_FAIL;

import android.os.Bundle;
import android.view.View;

import androidx.core.view.KeyEventDispatcher;
import androidx.lifecycle.Observer;
import androidx.viewpager.widget.ViewPager;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.binioter.guideview.Component;
import com.binioter.guideview.Guide;
import com.binioter.guideview.GuideBuilder;
import com.wb.lib_arch.common.Bus;
import com.wb.lib_arch.common.MsgEvent;
import com.wb.lib_rxtools.RxJavaUtils;
import com.wb.lib_utils.utils.DpUtils;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.wb.lib_arch.annotations.BarStyle;
import com.wb.lib_arch.annotations.BindRes;
import com.wb.lib_arch.annotations.SwipeStyle;
import com.wb.lib_utils.utils.log.LogUtils;
import com.wb.lib_weiget.titlebar.TitleBar;
import com.zizhiguanjia.lib_base.cases.KvUtils;
import com.zizhiguanjia.lib_base.constants.ListRouterPath;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.model_list.R;
import com.zizhiguanjia.model_list.adapter.ExamChapterMainAdapter;
import com.zizhiguanjia.model_list.databinding.ListExamChaptermainLayoutBinding;
import com.zizhiguanjia.model_list.view.CustomGuideView3;

import io.reactivex.functions.Consumer;

@Route(path = ListRouterPath.MAIN_EXAM_LIST)
@BindRes( statusBarStyle= BarStyle.LIGHT_CONTENT,statusBarTranslucent=false,swipeBack = SwipeStyle.NONE)
public class ExamChapterMainFragment extends BaseFragment implements TitleBar.OnTitleBarListener, ViewPager.OnPageChangeListener {
    private ListExamChaptermainLayoutBinding examChaptermainLayoutBinding;
    private ExamChapterMainAdapter mAdapter;
    private int index=1;
    private boolean chapter=true;
    @Override
    public int initLayoutResId() {
        return R.layout.list_exam_chaptermain_layout;
    }

    @Override
    public void initView(Bundle savedInstanceState) {
        examChaptermainLayoutBinding=getBinding();
        mAdapter=new ExamChapterMainAdapter(getChildFragmentManager());
        examChaptermainLayoutBinding.cvpChapterMain.setAdapter(mAdapter);
        examChaptermainLayoutBinding.tbExamChapter.setClickListener(this);
        index=initArguments().getInt("index",1);
        chapter=initArguments().getBoolean("chapter",true);
        examChaptermainLayoutBinding.cvpChapterMain.setCurrentItem(index);
        examChaptermainLayoutBinding.cvpChapterMain.setScroll(false);
        examChaptermainLayoutBinding.cvpChapterMain.setOnPageChangeListener(this);
        examChaptermainLayoutBinding.tbExamChapter.setClickListener(this);
        examChaptermainLayoutBinding.switchImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(index==1){
                    index=0;
                }else {
                    index=1;
                }
                examChaptermainLayoutBinding.cvpChapterMain.setCurrentItem(index);
            }
        });



    }

    @Override
    public void initViewData() {
        LogUtils.e("chapterchapter"+chapter);
//        examChaptermainLayoutBinding.tbExamChapter.getRightImageButton().setImageResource(chapter?R.drawable.list_exam_qh :0);
        examChaptermainLayoutBinding.switchImg.setImageResource(chapter?R.drawable.list_exam_qh :0);
        examChaptermainLayoutBinding.tbExamChapter.getCenterTextView().setText(index==0?"题型练习":"知识点练习");
        if(chapter){
            boolean save=KvUtils.get("list_guilder",false);
            if(!save){
                examChaptermainLayoutBinding.switchImg.post(new Runnable() {
                    @Override
                    public void run() {
                        showGuilde();
                    }
                });
            }
        }
    }

    @Override
    public void initObservable() {
        Bus.observe(this, new Observer<MsgEvent>() {
            @Override
            public void onChanged(MsgEvent msgEvent) {
                if(msgEvent.getCode()==PAY_MSG_FAIL){
                    MainThreadUtils.post(new Runnable() {
                        @Override
                        public void run() {
//                            payFail();
                        }
                    });
                }
            }
        });
    }
    private void payFail(){
        MessageHelper.openPayFailServer(getActivity(),this);
    }
    @Override
    public void onClicked(View v, int action) {
            try {
                if(action==TitleBar.ACTION_RIGHT_BUTTON){

                }else if(action==TitleBar.ACTION_LEFT_BUTTON){
                    finish();
                }
            }catch (Exception e){
            }


    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {
        examChaptermainLayoutBinding.tbExamChapter.getCenterTextView().setText(position==0?"题型练习":"知识点练习");
    }

    @Override
    public void onPageScrollStateChanged(int state) {
    }
    private void showGuilde(){
        final GuideBuilder builder1 = new GuideBuilder();
        builder1.setTargetView(examChaptermainLayoutBinding.switchImg)
                .setAlpha(150)
                .setHighTargetCorner(DpUtils.dp2px(getContext(),13))
                .setHighTargetPaddingBottom(DpUtils.dp2px(getContext(),6))
                .setHighTargetPaddingRight(DpUtils.dp2px(getContext(),9))
                .setHighTargetPaddingTop(DpUtils.dp2px(getContext(),6))
                .setHighTargetPaddingLeft(DpUtils.dp2px(getContext(),9))
                .setHighTargetGraphStyle(Component.ROUNDRECT);
        builder1.setOnVisibilityChangedListener(new GuideBuilder.OnVisibilityChangedListener() {
            @Override
            public void onShown() {
            }

            @Override
            public void onDismiss() {
                KvUtils.save("list_guilder",true);
            }
        });

        builder1.addComponent(new CustomGuideView3(getActivity()));
        Guide guide = builder1.createGuide();
        guide.show(getActivity());
    }
}
