package com.zizhiguanjia.model_list.fragment;

import android.os.Bundle;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.wb.lib_arch.annotations.BindViewModel;
import com.wb.lib_pop.PopupManager;
import com.wb.lib_pop.impl.LoadingPopupView;
import com.wb.lib_utils.utils.StringUtils;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.lib_base.base.BaseFragment;
import com.zizhiguanjia.lib_base.config.PayRouthConfig;
import com.zizhiguanjia.lib_base.constants.ListRouterPath;
import com.zizhiguanjia.lib_base.helper.CoreExamHelper;
import com.zizhiguanjia.lib_base.helper.HomeHelper;
import com.zizhiguanjia.lib_base.helper.MessageHelper;
import com.zizhiguanjia.lib_base.utils.DebounceUtils;
import com.zizhiguanjia.lib_base.helper.PointHelper;
import com.zizhiguanjia.lib_base.helper.UmengHelper;
import com.zizhiguanjia.lib_base.helper.UserHelper;
import com.zizhiguanjia.lib_base.listeners.OnChoiceTimeListenter;
import com.zizhiguanjia.lib_base.msgconfig.PointerMsgType;
import com.zizhiguanjia.model_list.R;
import com.zizhiguanjia.model_list.config.ExamPaperTypeConfig;
import com.zizhiguanjia.model_list.databinding.ListExamAutoTestLayoutBinding;
import com.zizhiguanjia.model_list.model.ExamDesBean;
import com.zizhiguanjia.model_list.navigator.AutoTestExamNavigator;
import com.zizhiguanjia.model_list.viewmodel.AutoTestExamViewMode;

@Route(path = ListRouterPath.MAIN_EXAM_AUTO)
public class AutoTestExamFragment extends BaseFragment implements AutoTestExamNavigator {
    @BindViewModel
    AutoTestExamViewMode autoTestExamViewMode;
    private ListExamAutoTestLayoutBinding examAutoTestLayoutBinding;
    private LoadingPopupView loading;
    private int mPageType;
    @Override
    public int initLayoutResId() {
        return R.layout.list_exam_auto_test_layout;
    }
    @Override
    public void initView(Bundle savedInstanceState) {
        mPageType=getArguments().getInt("mPageType",0);
        examAutoTestLayoutBinding=getBinding();
        examAutoTestLayoutBinding.setModel(autoTestExamViewMode);
        String pagerType = getArguments().getString("pagerType");
        autoTestExamViewMode.initPaprams(this,mPageType,pagerType);
        boolean dialog=initArguments().getBoolean("autoDialog",true);
       if(dialog){
           openTimeSelect();
       }
//       examAutoTestLayoutBinding.mainS.setOnTouchListener(new View.OnTouchListener() {
//           @Override
//           public boolean onTouch(View v, MotionEvent event) {
//               return true;
//           }
//       });

    }
    @Override
    public void initViewData() {
    }
    @Override
    public void initObservable() {
    }
    @Override
    public void showNoBuyVipView() {
        UmengHelper.onEventObject(getContext(), PointerMsgType.POINTER_A_MONI_TRIGGERBOUNCED,null);
        PointHelper.joinPointData(PointerMsgType.POINTER_A_MONI_TRIGGERBOUNCED,true);

        // 防抖动：防止重复调用购买对话框
        String debounceKey = "openNoPremissBuyDialog_" + this.getClass().getSimpleName();
        if (!DebounceUtils.shouldExecute(debounceKey, 2000)) {
            LogUtils.e("购买对话框调用被防抖动拦截");
            return;
        }
        MessageHelper.openNoPremissBuyDialog(getActivity(),true,PayRouthConfig.PAY_AOUTH_PAY);
    }

    @Override
    public void startAutoTestExam() {
        initArguments().putString("title","模拟考试");
        initArguments().putBoolean("restart",true);
        initArguments().putBoolean("isBack",false);
//        initArguments().putString("paperType", String.valueOf(ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM));
        initArguments().putString("paperType",
                getArguments() != null && getArguments().containsKey("pagerType") ? getArguments().getString("pagerType") :
                        String.valueOf(ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM));
        startFragment(CoreExamHelper.mainPage(getActivity()));
        finish();
    }

    @Override
    public void onDestroy() {
        PointHelper.unRegistPoinRouth(PointerMsgType.POINTER_A_MONI_TRIGGERBOUNCED,false);
        super.onDestroy();
    }

    @Override
    public void closeView() {
        finish();
    }

    @Override
    public void initLoadConfig() {
        loading = new PopupManager.Builder(getContext()).asLoading("请稍后...",R.layout.popup_center_impl_loading);
    }

    @Override
    public void showLoading(boolean show) {
        if (loading == null){
            return;
        }
        if (show) {
            if (loading.isShow()) {
                return;
            }
            loading.show();
        } else {
            if (loading.isShow()) {
                loading.dismiss();
            }
        }
    }

    @Override
    public void toGoGrade() {
        initArguments().putString("routh","results");
        startFragment(HomeHelper.mainPage());
        finish();
    }

    @Override
    public void updateExamTimeUpdate() {
        finish();
        if(UserHelper.isBecomeVip()){
            initArguments().putString("title","模拟考试");
            initArguments().putBoolean("restart",true);
            initArguments().putBoolean("isBack",false);
//            initArguments().putString("paperType", String.valueOf(ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM));
            initArguments().putString("paperType",
                    getArguments() != null && getArguments().containsKey("pagerType") ? getArguments().getString("pagerType") :
                            String.valueOf(ExamPaperTypeConfig.EXAM_PAPER_AUTOEXAM));
            startFragment(CoreExamHelper.mainPage(getActivity()));
        }else {
            initArguments().putString("flutterRoute","/testdate");
            initArguments().putBoolean("autoDialog",false);
            startFragment(HomeHelper.startMessageFragment());
        }

    }

    @Override
    public void openTimeSelect() {
        MessageHelper.openTimeChoiceDialog(getActivity(), new OnChoiceTimeListenter() {
            @Override
            public void getChoiceTime(String year, String mouther, String day) {
                LogUtils.e("选择了---->>>"+year+""+mouther+""+day);
                String time=year+"-"+mouther+"-"+day;
                autoTestExamViewMode.updataExamTime(time);
            }
        });
    }

    @Override
    public void showOtherAutoView(ExamDesBean.PaperTipBean paperTipBean) {
        examAutoTestLayoutBinding.otherAuto.tvAutoExamTest.setVisibility(View.VISIBLE);
        examAutoTestLayoutBinding.otherAuto.ortherTitleTv.setText("科目："+paperTipBean.getExamSubject());
        examAutoTestLayoutBinding.otherAuto.ortherTimeTv.setText(paperTipBean.getExamTimes());
        examAutoTestLayoutBinding.otherAuto.ortherBzTv.setText(paperTipBean.getPassCriteria());
        examAutoTestLayoutBinding.otherAuto.ortherRemarkTv.setText(paperTipBean.getSubjectRemarks());
        examAutoTestLayoutBinding.otherAuto.ortherRemarkTv.setVisibility(StringUtils.isEmpty(paperTipBean.getSubjectRemarks())? View.GONE:View.VISIBLE);
        autoTestExamViewMode.reshImage(getActivity(),paperTipBean.getScoreDistribution(),examAutoTestLayoutBinding.otherAuto.ortherImage1);
        autoTestExamViewMode.reshImage(getActivity(),paperTipBean.getScoreStandard(),examAutoTestLayoutBinding.otherAuto.ortherImage2);
        examAutoTestLayoutBinding.otherAuto.radialTaskTv.setVisibility(View.VISIBLE);
    }

}
