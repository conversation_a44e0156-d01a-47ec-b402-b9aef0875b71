package com.zizhiguanjia.model_list.example;

import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_list.model.ExamChapterBean;
import com.zizhiguanjia.model_list.model.ExamSubChapterBean;
import com.zizhiguanjia.model_list.utils.ExamChapterDataConverter;

import java.util.List;

/**
 * 考试章节模型使用示例
 * 展示如何处理 List<Object> Childs 数据
 */
public class ExamChapterUsageExample {
    
    private static final String TAG = "ExamChapterUsageExample";
    
    /**
     * 处理考试章节数据的示例方法
     * 
     * @param examChapterBean 包含 Childs 数据的章节对象
     */
    public static void processExamChapterData(ExamChapterBean examChapterBean) {
        if (examChapterBean == null) {
            LogUtils.e(TAG + " - examChapterBean 为空");
            return;
        }
        
        LogUtils.e(TAG + " - 处理章节: " + examChapterBean.getTitle());
        
        // 获取子章节列表
        List<ExamChapterBean.ChildsBean> childsList = examChapterBean.getChilds();
        
        if (childsList != null && !childsList.isEmpty()) {
            LogUtils.e(TAG + " - 找到 " + childsList.size() + " 个子章节");
            
            for (ExamChapterBean.ChildsBean child : childsList) {
                processChildChapter(child);
            }
        } else {
            LogUtils.e(TAG + " - 没有子章节数据");
        }
    }
    
    /**
     * 处理单个子章节
     * 
     * @param childBean 子章节对象
     */
    private static void processChildChapter(ExamChapterBean.ChildsBean childBean) {
        if (childBean == null) {
            return;
        }
        
        LogUtils.e(TAG + " - 子章节: " + childBean.getTitle());
        LogUtils.e(TAG + " - 考试代码: " + childBean.getExamCode());
        LogUtils.e(TAG + " - 完成数量: " + childBean.getDoneExamCount() + "/" + childBean.getExamCount());
        LogUtils.e(TAG + " - 正确率: " + childBean.getRightRate());
        LogUtils.e(TAG + " - 是否可进入: " + childBean.isCanGo());
        
        // 如果子章节还有更深层的 Childs，可以递归处理
        List<ExamChapterBean.ChildsBean> subChilds = childBean.getChilds();
        if (subChilds != null && !subChilds.isEmpty()) {
            LogUtils.e(TAG + " - 该子章节还有 " + subChilds.size() + " 个更深层的子项");
            for (ExamChapterBean.ChildsBean subChild : subChilds) {
                processChildChapter(subChild); // 递归处理
            }
        }
    }
    
    /**
     * 使用新的 ExamSubChapterBean 处理数据的示例
     * 
     * @param objectList 原始的 Object 列表数据
     */
    public static void processWithNewModel(List<Object> objectList) {
        LogUtils.e(TAG + " - 使用新模型处理数据");
        
        // 转换数据
        List<ExamSubChapterBean> subChapterList = ExamChapterDataConverter.convertToExamSubChapterList(objectList);
        
        if (subChapterList.isEmpty()) {
            LogUtils.e(TAG + " - 转换后的列表为空");
            return;
        }
        
        LogUtils.e(TAG + " - 成功转换 " + subChapterList.size() + " 个子章节");
        
        // 处理每个子章节
        for (ExamSubChapterBean subChapter : subChapterList) {
            processExamSubChapter(subChapter);
        }
        
        // 打印调试信息
        ExamChapterDataConverter.printDebugInfo(subChapterList);
    }
    
    /**
     * 处理单个 ExamSubChapterBean
     * 
     * @param subChapter 子章节对象
     */
    private static void processExamSubChapter(ExamSubChapterBean subChapter) {
        if (subChapter == null) {
            return;
        }
        
        LogUtils.e(TAG + " - === 处理子章节 ===");
        LogUtils.e(TAG + " - ID: " + subChapter.getId());
        LogUtils.e(TAG + " - 标题: " + subChapter.getTitle());
        LogUtils.e(TAG + " - 考试代码: " + subChapter.getExamCode());
        LogUtils.e(TAG + " - 完成数量: " + subChapter.getDoneExamCount() + "/" + subChapter.getExamCount());
        LogUtils.e(TAG + " - 等级: " + subChapter.getLv() + "/" + subChapter.getTotalLv());
        LogUtils.e(TAG + " - 正确数量: " + subChapter.getRightCount());
        LogUtils.e(TAG + " - 正确率: " + subChapter.getRightRate());
        LogUtils.e(TAG + " - 是否开放: " + subChapter.getIsOpen());
        LogUtils.e(TAG + " - 是否可进入: " + subChapter.getCanGo());
        LogUtils.e(TAG + " - 排序: " + subChapter.getSort());
        LogUtils.e(TAG + " - 全部完成数量: " + subChapter.getAllDoneExamCount());
        LogUtils.e(TAG + " - 父级代码: " + subChapter.getParentCode());
        LogUtils.e(TAG + " - 备注: " + subChapter.getRemark());
        
        // 使用便利方法获取 int 值
        LogUtils.e(TAG + " - ID (int): " + subChapter.getIdAsInt());
        LogUtils.e(TAG + " - 完成数量 (int): " + subChapter.getDoneExamCountAsInt());
        LogUtils.e(TAG + " - 是否开放 (boolean): " + subChapter.isOpenAsBoolean());
        LogUtils.e(TAG + " - 是否可进入 (boolean): " + subChapter.canGoAsBoolean());
        
        // 处理嵌套的子章节
        List<ExamSubChapterBean> nestedChilds = subChapter.getChilds();
        if (nestedChilds != null && !nestedChilds.isEmpty()) {
            LogUtils.e(TAG + " - 该子章节包含 " + nestedChilds.size() + " 个嵌套子项");
            for (ExamSubChapterBean nestedChild : nestedChilds) {
                processExamSubChapter(nestedChild); // 递归处理
            }
        }
    }
    
    /**
     * 模拟数据处理的完整流程
     */
    public static void simulateDataProcessing() {
        LogUtils.e(TAG + " - === 开始模拟数据处理 ===");
        
        // 模拟您提供的数据格式
        String sampleJson = "[" +
                "{\"Id\":31711.0,\"ExamCode\":\"bj-aqy1752925274\",\"Title\":\"吊索具水平夹角\"," +
                "\"DoneExamCount\":0.0,\"ExamCount\":1.0,\"Lv\":3.0,\"IsOpen\":false," +
                "\"Childs\":null,\"ParentCode\":\"bj-aqy1752925273\",\"TotalLv\":3.0," +
                "\"RightCount\":0.0,\"Remark\":\"开始新的刷题之旅吧\",\"RightRate\":\"0.00%\"," +
                "\"CanGo\":true,\"Sort\":1.0,\"AllDoneExamCount\":0.0}," +
                "{\"Id\":31712.0,\"ExamCode\":\"bj-aqy1752925275\",\"Title\":\"起升钢丝绳垂直夹角\"," +
                "\"DoneExamCount\":0.0,\"ExamCount\":1.0,\"Lv\":3.0,\"IsOpen\":false," +
                "\"Childs\":null,\"ParentCode\":\"bj-aqy1752925273\",\"TotalLv\":3.0," +
                "\"RightCount\":0.0,\"Remark\":\"开始新的刷题之旅吧\",\"RightRate\":\"0.00%\"," +
                "\"CanGo\":true,\"Sort\":2.0,\"AllDoneExamCount\":0.0}" +
                "]";
        
        // 使用新模型解析数据
        List<ExamSubChapterBean> parsedData = ExamChapterDataConverter.convertFromJson(sampleJson);
        
        LogUtils.e(TAG + " - 解析结果: " + parsedData.size() + " 个项目");
        
        // 处理解析后的数据
        for (ExamSubChapterBean item : parsedData) {
            processExamSubChapter(item);
        }
        
        // 转换回 JSON 进行验证
        String convertedJson = ExamChapterDataConverter.convertToJson(parsedData);
        LogUtils.e(TAG + " - 转换回的 JSON: " + convertedJson);
        
        LogUtils.e(TAG + " - === 模拟数据处理完成 ===");
    }
}
