package com.zizhiguanjia.model_list.repository;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wb.lib_utils.utils.MainThreadUtils;
import com.wb.lib_utils.utils.StringUtils;
import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.helper.DataHelper;
import com.zizhiguanjia.lib_base.utils.EncryptDecryptOptions;
import com.zizhiguanjia.model_list.helper.ExamHelper;
import com.zizhiguanjia.model_list.listener.ExamSheetDataListener;
import com.zizhiguanjia.model_list.listener.IExamDataList;
import com.zizhiguanjia.model_list.model.ExamAnswerSheetBean;

import java.io.IOException;
import java.lang.ref.WeakReference;
import java.lang.reflect.Type;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.Response;

public class ExamDataListRepository  implements IExamDataList {
    private EncryptDecryptOptions encryDecrySkey = new EncryptDecryptOptions();
    private WeakReference<ExamSheetDataListener> dataListenerWeakReferences;
    @Override
    public void init(ExamSheetDataListener dataListenerWeakReference) {
        if (dataListenerWeakReference != null) {
            dataListenerWeakReferences=new WeakReference<>(dataListenerWeakReference);
        }
    }
    @Override
    public void getExamSheetListData(String paperType, String paperValue,boolean newStart,int location) {
        MultipartBody.Builder multiBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        multiBuilder.addFormDataPart("paperType", paperType);
        if(!StringUtils.isEmpty(paperValue)){
            if(!paperValue.equals("-1")){
                multiBuilder.addFormDataPart("paperValue", paperValue);
            }
        }
        if(location!=-1){
            multiBuilder.addFormDataPart("location", String.valueOf(location));
        }
        multiBuilder.addFormDataPart("newStart", String.valueOf(newStart));
        RequestBody multiBody=multiBuilder.build();
        ExamHelper.getInstance().getExamSheetData(BaseAPI.VERSION_DES+"/API/Exam/GetExamAnswerSheet", multiBody, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                MainThreadUtils.post(new Runnable() {
                    @Override
                    public void run() {
                        if(getListener()==null)return;
                        getListener().getExamSheetDataListener(null);
                    }
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if(response.code()==200){
                    MainThreadUtils.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                String json=response.body().string();
                                String strJson=DataHelper.getExamSheetDataList(json);
                                Type jsonType = new TypeToken<BaseData<ExamAnswerSheetBean>>() {}.getType();
                                BaseData<ExamAnswerSheetBean> commonExamBeanBaseData= new Gson().fromJson(strJson,jsonType);
                                if(commonExamBeanBaseData.Code.equals("200")){
                                    //有效
                                    if(getListener()==null)return;
                                    getListener().getExamSheetDataListener(commonExamBeanBaseData.Data);
                                }else {
                                    if(getListener()==null)return;
                                    getListener().getExamSheetDataListener(null);
                                }
                            } catch (IOException e) {
                                e.printStackTrace();
                                if(getListener()==null)return;
                                getListener().getExamSheetDataListener(null);
                            }
                        }
                    });
                }

            }
        });
    }

    private ExamSheetDataListener getListener() {
        if(dataListenerWeakReferences==null)return null;
        return dataListenerWeakReferences.get();
    }
}
