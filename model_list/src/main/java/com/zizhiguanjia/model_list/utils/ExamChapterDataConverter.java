package com.zizhiguanjia.model_list.utils;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.reflect.TypeToken;
import com.wb.lib_utils.utils.log.LogUtils;
import com.zizhiguanjia.model_list.model.ExamSubChapterBean;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 考试章节数据转换工具类
 * 用于将 List<Object> 转换为具体的模型对象
 */
public class ExamChapterDataConverter {
    
    private static final String TAG = "ExamChapterDataConverter";
    private static final Gson gson = new Gson();
    
    /**
     * 将 List<Object> 转换为 List<ExamSubChapterBean>
     * 
     * @param objectList 原始的 Object 列表
     * @return 转换后的 ExamSubChapterBean 列表
     */
    public static List<ExamSubChapterBean> convertToExamSubChapterList(List<Object> objectList) {
        List<ExamSubChapterBean> result = new ArrayList<>();
        
        if (objectList == null || objectList.isEmpty()) {
            LogUtils.e(TAG + " - convertToExamSubChapterList: 输入列表为空");
            return result;
        }
        
        try {
            for (Object obj : objectList) {
                ExamSubChapterBean subChapter = convertObjectToExamSubChapter(obj);
                if (subChapter != null) {
                    result.add(subChapter);
                }
            }
            
            LogUtils.e(TAG + " - 成功转换 " + result.size() + " 个子章节");
            
        } catch (Exception e) {
            LogUtils.e(TAG + " - 转换过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }
    
    /**
     * 将单个 Object 转换为 ExamSubChapterBean
     * 
     * @param obj 原始对象
     * @return 转换后的 ExamSubChapterBean，转换失败返回 null
     */
    public static ExamSubChapterBean convertObjectToExamSubChapter(Object obj) {
        if (obj == null) {
            return null;
        }
        
        try {
            // 方法1：如果对象已经是 Map 类型
            if (obj instanceof Map) {
                return convertMapToExamSubChapter((Map<String, Object>) obj);
            }
            
            // 方法2：通过 Gson 进行转换
            JsonElement jsonElement = gson.toJsonTree(obj);
            return gson.fromJson(jsonElement, ExamSubChapterBean.class);
            
        } catch (Exception e) {
            LogUtils.e(TAG + " - 转换单个对象失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 将 Map 对象转换为 ExamSubChapterBean
     * 
     * @param map 包含数据的 Map
     * @return 转换后的 ExamSubChapterBean
     */
    private static ExamSubChapterBean convertMapToExamSubChapter(Map<String, Object> map) {
        ExamSubChapterBean bean = new ExamSubChapterBean();
        
        try {
            // 安全地获取和设置各个字段
            bean.setId(getDoubleValue(map, "Id"));
            bean.setExamCode(getStringValue(map, "ExamCode"));
            bean.setTitle(getStringValue(map, "Title"));
            bean.setDoneExamCount(getDoubleValue(map, "DoneExamCount"));
            bean.setExamCount(getDoubleValue(map, "ExamCount"));
            bean.setLv(getDoubleValue(map, "Lv"));
            bean.setIsOpen(getBooleanValue(map, "IsOpen"));
            bean.setParentCode(getStringValue(map, "ParentCode"));
            bean.setTotalLv(getDoubleValue(map, "TotalLv"));
            bean.setRightCount(getDoubleValue(map, "RightCount"));
            bean.setRemark(getStringValue(map, "Remark"));
            bean.setRightRate(getStringValue(map, "RightRate"));
            bean.setCanGo(getBooleanValue(map, "CanGo"));
            bean.setSort(getDoubleValue(map, "Sort"));
            bean.setAllDoneExamCount(getDoubleValue(map, "AllDoneExamCount"));
            
            // 处理嵌套的 Childs 列表
            Object childsObj = map.get("Childs");
            if (childsObj instanceof List) {
                List<ExamSubChapterBean> childsList = convertToExamSubChapterList((List<Object>) childsObj);
                bean.setChilds(childsList);
            }
            
        } catch (Exception e) {
            LogUtils.e(TAG + " - 转换 Map 对象失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return bean;
    }
    
    /**
     * 安全地从 Map 中获取 String 值
     */
    private static String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }
    
    /**
     * 安全地从 Map 中获取 Double 值
     */
    private static Double getDoubleValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        
        try {
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            } else if (value instanceof String) {
                return Double.parseDouble((String) value);
            }
        } catch (NumberFormatException e) {
            LogUtils.e(TAG + " - 无法转换为 Double: " + key + " = " + value);
        }
        
        return null;
    }
    
    /**
     * 安全地从 Map 中获取 Boolean 值
     */
    private static Boolean getBooleanValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        
        return null;
    }
    
    /**
     * 使用 Gson 的 TypeToken 进行类型安全的转换
     * 
     * @param json JSON 字符串
     * @return 转换后的列表
     */
    public static List<ExamSubChapterBean> convertFromJson(String json) {
        try {
            Type listType = new TypeToken<List<ExamSubChapterBean>>(){}.getType();
            return gson.fromJson(json, listType);
        } catch (Exception e) {
            LogUtils.e(TAG + " - JSON 转换失败: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
    
    /**
     * 将 ExamSubChapterBean 列表转换为 JSON 字符串
     * 
     * @param list ExamSubChapterBean 列表
     * @return JSON 字符串
     */
    public static String convertToJson(List<ExamSubChapterBean> list) {
        try {
            return gson.toJson(list);
        } catch (Exception e) {
            LogUtils.e(TAG + " - 转换为 JSON 失败: " + e.getMessage());
            e.printStackTrace();
            return "[]";
        }
    }
    
    /**
     * 打印调试信息
     * 
     * @param list ExamSubChapterBean 列表
     */
    public static void printDebugInfo(List<ExamSubChapterBean> list) {
        if (list == null || list.isEmpty()) {
            LogUtils.e(TAG + " - 列表为空");
            return;
        }
        
        LogUtils.e(TAG + " - 列表包含 " + list.size() + " 个项目:");
        for (int i = 0; i < list.size(); i++) {
            ExamSubChapterBean item = list.get(i);
            LogUtils.e(TAG + " - [" + i + "] " + item.toString());
        }
    }
}
