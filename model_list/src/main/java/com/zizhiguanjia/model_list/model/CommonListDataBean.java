package com.zizhiguanjia.model_list.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

public class CommonListDataBean implements Parcelable {

        /**
     * Items : [{"PaperId":1,"Title":"单选题","IsSubmit":false,"ExamCount":635,"DoneCount":0,"Sort":0,"CanGo":true},{"PaperId":2,"Title":"多选题","IsSubmit":false,"ExamCount":398,"DoneCount":0,"Sort":0,"CanGo":true},{"PaperId":3,"Title":"案例分析","IsSubmit":false,"ExamCount":9,"DoneCount":1,"Sort":0,"CanGo":true}]
     * PaperType : 4
     */
    private int PaperType;
    private List<ItemsBean> Items;

    public int getPaperType() {
        return PaperType;
    }

    public void setPaperType(int PaperType) {
        this.PaperType = PaperType;
    }

    public List<ItemsBean> getItems() {
        return Items;
    }

    public void setItems(List<ItemsBean> Items) {
        this.Items = Items;
    }

    public static class ItemsBean implements Parcelable {

        /**
         * PaperId : 1
         * Title : 单选题
         * IsSubmit : false
         * ExamCount : 635
         * DoneCount : 0
         * Sort : 0
         * CanGo : true
         */
        private int PaperId;
        private String Title;
        private boolean IsSubmit;
        private int ExamCount;
        private int DoneCount;
        private int Sort;
        private boolean CanGo;
        private String Score;
        private String RightRate;

        public boolean isSubmit() {
            return IsSubmit;
        }

        public void setSubmit(boolean submit) {
            IsSubmit = submit;
        }

        public String getRightRate() {
            return RightRate;
        }

        public void setRightRate(String rightRate) {
            RightRate = rightRate;
        }

        public String getScore() {
            return Score;
        }

        public void setScore(String score) {
            Score = score;
        }

        public int getPaperId() {
            return PaperId;
        }

        public void setPaperId(int PaperId) {
            this.PaperId = PaperId;
        }

        public String getTitle() {
            return Title;
        }

        public void setTitle(String Title) {
            this.Title = Title;
        }

        public boolean isIsSubmit() {
            return IsSubmit;
        }

        public void setIsSubmit(boolean IsSubmit) {
            this.IsSubmit = IsSubmit;
        }

        public int getExamCount() {
            return ExamCount;
        }

        public void setExamCount(int ExamCount) {
            this.ExamCount = ExamCount;
        }

        public int getDoneCount() {
            return DoneCount;
        }

        public void setDoneCount(int DoneCount) {
            this.DoneCount = DoneCount;
        }

        public int getSort() {
            return Sort;
        }

        public void setSort(int Sort) {
            this.Sort = Sort;
        }

        public boolean isCanGo() {
            return CanGo;
        }

        public void setCanGo(boolean CanGo) {
            this.CanGo = CanGo;
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeInt(this.PaperId);
            dest.writeString(this.Title);
            dest.writeByte(this.IsSubmit ? (byte) 1 : (byte) 0);
            dest.writeInt(this.ExamCount);
            dest.writeInt(this.DoneCount);
            dest.writeInt(this.Sort);
            dest.writeByte(this.CanGo ? (byte) 1 : (byte) 0);
        }

        public ItemsBean() {
        }

        protected ItemsBean(Parcel in) {
            this.PaperId = in.readInt();
            this.Title = in.readString();
            this.IsSubmit = in.readByte() != 0;
            this.ExamCount = in.readInt();
            this.DoneCount = in.readInt();
            this.Sort = in.readInt();
            this.CanGo = in.readByte() != 0;
        }

        public static final Creator<ItemsBean> CREATOR = new Creator<ItemsBean>() {
            @Override
            public ItemsBean createFromParcel(Parcel source) {
                return new ItemsBean(source);
            }

            @Override
            public ItemsBean[] newArray(int size) {
                return new ItemsBean[size];
            }
        };
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.PaperType);
        dest.writeList(this.Items);
    }

    public CommonListDataBean() {
    }

    protected CommonListDataBean(Parcel in) {
        this.PaperType = in.readInt();
        this.Items = new ArrayList<ItemsBean>();
        in.readList(this.Items, ItemsBean.class.getClassLoader());
    }

    public static final Parcelable.Creator<CommonListDataBean> CREATOR = new Parcelable.Creator<CommonListDataBean>() {
        @Override
        public CommonListDataBean createFromParcel(Parcel source) {
            return new CommonListDataBean(source);
        }

        @Override
        public CommonListDataBean[] newArray(int size) {
            return new CommonListDataBean[size];
        }
    };
}
