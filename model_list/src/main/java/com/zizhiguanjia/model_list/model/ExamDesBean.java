package com.zizhiguanjia.model_list.model;

public class ExamDesBean {

    private String titleTip;
    private String updateTip;
    private PaperTipBean paperTip;
    private TestTipBean testTip;

    public void setUpdateTip(String updateTip) {
        this.updateTip = updateTip;
    }

    public String getUpdateTip() {
        return updateTip;
    }

    public String getTitleTip() {
        return titleTip;
    }

    public void setTitleTip(String titleTip) {
        this.titleTip = titleTip;
    }
    public String getUpdateTipSplitByIndex(int index) {
        return updateTip != null ? updateTip.split("<br>").length > index ? updateTip.split("<br>")[index] : "" : "";
    }
    public PaperTipBean getPaperTip() {
        return paperTip;
    }

    public void setPaperTip(PaperTipBean paperTip) {
        this.paperTip = paperTip;
    }

    public TestTipBean getTestTip() {
        return testTip;
    }

    public void setTestTip(TestTipBean testTip) {
        this.testTip = testTip;
    }

    public static class PaperTipBean {
        /**
         * title : 试卷介绍
         * tip : 1、单选题（每题10分，共20题）
         2、多选题（每题20分，共10题）
         3、案例题（不计入分值）
         * standard : 考试标准：满分48分，72分及格
         * times : 考试时间：180分钟
         * "errorProneTotal": "易错题：0道",
         * "doCount": "已做：0道",
         * "remainingCount": "剩余：0道"
         */
        private String isCanDoPaper;
        private String examSubject;
        private String examTimes;
        private String passCriteria;
        private String subjectRemarks;
        private String scoreDistribution;
        private String scoreStandard;
        private String paperProportion;
        private String errorProneTotal;
        private String doCount;
        private String remainingCount;
        private Integer remainingNum;
        private boolean isCanGo;

        public boolean isCanGo() {
            return isCanGo;
        }

        public void setCanGo(boolean canGo) {
            isCanGo = canGo;
        }
        public String getErrorProneTotal() {
            return errorProneTotal;
        }


        public Integer getRemainingNum() {
            return remainingNum;
        }

        public void setRemainingNum(Integer remainingNum) {
            this.remainingNum = remainingNum;
        }
        public void setErrorProneTotal(String errorProneTotal) {
            this.errorProneTotal = errorProneTotal;
        }
        /**
         * 是否剩余0道
         */
        public String getOptionsText(){
            return remainingNum != null && remainingNum == 100 ?  "查看全部易错题" : "练习剩余易错题";
        }

        public String getDoCount() {
            return doCount;
        }

        public void setDoCount(String doCount) {
            this.doCount = doCount;
        }

        public String getRemainingCount() {
            return remainingCount;
        }
        public void setScoreStandard(String scoreStandard) {
            this.scoreStandard = scoreStandard;
        }

        public String getScoreStandard() {
            return scoreStandard;
        }

        public String getIsCanDoPaper() {
            return isCanDoPaper;
        }

        public void setIsCanDoPaper(String isCanDoPaper) {
            this.isCanDoPaper = isCanDoPaper;
        }

        public String getExamSubject() {
            return examSubject;
        }

        public void setExamSubject(String examSubject) {
            this.examSubject = examSubject;
        }

        public String getExamTimes() {
            return examTimes;
        }

        public void setExamTimes(String examTimes) {
            this.examTimes = examTimes;
        }

        public String getPassCriteria() {
            return passCriteria;
        }

        public void setPassCriteria(String passCriteria) {
            this.passCriteria = passCriteria;
        }

        public String getSubjectRemarks() {
            return subjectRemarks;
        }

        public void setSubjectRemarks(String subjectRemarks) {
            this.subjectRemarks = subjectRemarks;
        }

        public String getScoreDistribution() {
            return scoreDistribution;
        }

        public void setScoreDistribution(String scoreDistribution) {
            this.scoreDistribution = scoreDistribution;
        }

        public String getPaperProportion() {
            return paperProportion;
        }

        public void setPaperProportion(String paperProportion) {
            this.paperProportion = paperProportion;
        }

        private String title;
        private String tip;
        private String standard;
        private String times;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getTip() {
            return tip;
        }

        public void setTip(String tip) {
            this.tip = tip;
        }

        public String getStandard() {
            return standard;
        }

        public void setStandard(String standard) {
            this.standard = standard;
        }

        public String getTimes() {
            return times;
        }

        public void setTimes(String times) {
            this.times = times;
        }
    }

    public static class TestTipBean {
        /**
         * title : 考前必读
         * tip : 当完成考试交卷后才可查看答案哦～未交卷钱可修改答案
         */

        private String title;
        private String tip;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getTip() {
            return tip;
        }

        public void setTip(String tip) {
            this.tip = tip;
        }
    }
}
