package com.zizhiguanjia.model_list.model;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;


import java.util.List;

public class AnswerGroupBean implements Parcelable {
    private int pageState;
    private int questionNum;
    private int questionType;
    private boolean isConfim;
    private int indexs;

    @Override
    public String toString() {
        return "AnswerGroupBean{" +
                "pageState=" + pageState +
                ", questionNum=" + questionNum +
                ", questionType=" + questionType +
                ", isConfim=" + isConfim +
                ", indexs=" + indexs +
                ", answerSheetOptionBean=" + answerSheetOptionBean +
                '}';
    }

    public int getIndexs() {
        return indexs;
    }
    public void setIndexs(int indexs) {
        this.indexs = indexs;
    }
    private ExamPackAccessDataBean.RecordsBean answerSheetOptionBean;

    public int getQuestionType() {
        return questionType;
    }

    public void setQuestionType(int questionType) {
        this.questionType = questionType;
    }

    public int getQuestionNum() {
        return questionNum;
    }

    public void setQuestionNum(int questionNum) {
        this.questionNum = questionNum;
    }

    public int getPageState() {
        return pageState;
    }

    public void setPageState(int pageState) {
        this.pageState = pageState;
    }

    public ExamPackAccessDataBean.RecordsBean getAnswerSheetOptionBean() {
        return answerSheetOptionBean;
    }

    public void setAnswerSheetOptionBean(ExamPackAccessDataBean.RecordsBean answerSheetOptionBean) {
        this.answerSheetOptionBean = answerSheetOptionBean;
    }

    public boolean isConfim() {
        return isConfim;
    }

    public void setConfim(boolean confim) {
        isConfim = confim;
    }

    public AnswerGroupBean() {
    }

    //    @Override
//    public int hashCode() {
//        if(pageState== PageTypeConfig.PAGE_STATE_LOADING){
//            return questionNum;
//        }else {
//            return (pageState+""+PageTypeConfig.PAGE_STATE_SUCCESS).hashCode();
//        }
//    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.pageState);
        dest.writeInt(this.questionNum);
        dest.writeInt(this.questionType);
        dest.writeByte(this.isConfim ? (byte) 1 : (byte) 0);
        dest.writeInt(this.indexs);
        dest.writeParcelable(this.answerSheetOptionBean, flags);
    }

    protected AnswerGroupBean(Parcel in) {
        this.pageState = in.readInt();
        this.questionNum = in.readInt();
        this.questionType = in.readInt();
        this.isConfim = in.readByte() != 0;
        this.indexs = in.readInt();
        this.answerSheetOptionBean = in.readParcelable(ExamPackAccessDataBean.RecordsBean.class.getClassLoader());
    }

    public static final Creator<AnswerGroupBean> CREATOR = new Creator<AnswerGroupBean>() {
        @Override
        public AnswerGroupBean createFromParcel(Parcel source) {
            return new AnswerGroupBean(source);
        }

        @Override
        public AnswerGroupBean[] newArray(int size) {
            return new AnswerGroupBean[size];
        }
    };
}
