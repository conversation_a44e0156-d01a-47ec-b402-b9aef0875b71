package com.zizhiguanjia.model_list.model;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 考试子章节数据模型
 * 用于处理 List<Object> Childs 中的数据
 */
public class ExamSubChapterBean implements Parcelable {
    
    @SerializedName("Id")
    private Double Id;
    
    @SerializedName("ExamCode")
    private String ExamCode;
    
    @SerializedName("Title")
    private String Title;
    
    @SerializedName("DoneExamCount")
    private Double DoneExamCount;
    
    @SerializedName("ExamCount")
    private Double ExamCount;
    
    @SerializedName("Lv")
    private Double Lv;
    
    @SerializedName("IsOpen")
    private Boolean IsOpen;
    
    @SerializedName("Childs")
    private List<ExamSubChapterBean> Childs;
    
    @SerializedName("ParentCode")
    private String ParentCode;
    
    @SerializedName("TotalLv")
    private Double TotalLv;
    
    @SerializedName("RightCount")
    private Double RightCount;
    
    @SerializedName("Remark")
    private String Remark;
    
    @SerializedName("RightRate")
    private String RightRate;
    
    @SerializedName("CanGo")
    private Boolean CanGo;
    
    @SerializedName("Sort")
    private Double Sort;
    
    @SerializedName("AllDoneExamCount")
    private Double AllDoneExamCount;

    // Getter and Setter methods
    public Double getId() {
        return Id;
    }

    public void setId(Double id) {
        Id = id;
    }

    public String getExamCode() {
        return ExamCode;
    }

    public void setExamCode(String examCode) {
        ExamCode = examCode;
    }

    public String getTitle() {
        return Title;
    }

    public void setTitle(String title) {
        Title = title;
    }

    public Double getDoneExamCount() {
        return DoneExamCount;
    }

    public void setDoneExamCount(Double doneExamCount) {
        DoneExamCount = doneExamCount;
    }

    public Double getExamCount() {
        return ExamCount;
    }

    public void setExamCount(Double examCount) {
        ExamCount = examCount;
    }

    public Double getLv() {
        return Lv;
    }

    public void setLv(Double lv) {
        Lv = lv;
    }

    public Boolean getIsOpen() {
        return IsOpen;
    }

    public void setIsOpen(Boolean isOpen) {
        IsOpen = isOpen;
    }

    public List<ExamSubChapterBean> getChilds() {
        return Childs;
    }

    public void setChilds(List<ExamSubChapterBean> childs) {
        Childs = childs;
    }

    public String getParentCode() {
        return ParentCode;
    }

    public void setParentCode(String parentCode) {
        ParentCode = parentCode;
    }

    public Double getTotalLv() {
        return TotalLv;
    }

    public void setTotalLv(Double totalLv) {
        TotalLv = totalLv;
    }

    public Double getRightCount() {
        return RightCount;
    }

    public void setRightCount(Double rightCount) {
        RightCount = rightCount;
    }

    public String getRemark() {
        return Remark;
    }

    public void setRemark(String remark) {
        Remark = remark;
    }

    public String getRightRate() {
        return RightRate;
    }

    public void setRightRate(String rightRate) {
        RightRate = rightRate;
    }

    public Boolean getCanGo() {
        return CanGo;
    }

    public void setCanGo(Boolean canGo) {
        CanGo = canGo;
    }

    public Double getSort() {
        return Sort;
    }

    public void setSort(Double sort) {
        Sort = sort;
    }

    public Double getAllDoneExamCount() {
        return AllDoneExamCount;
    }

    public void setAllDoneExamCount(Double allDoneExamCount) {
        AllDoneExamCount = allDoneExamCount;
    }

    // 便利方法：转换为 int 类型
    public int getIdAsInt() {
        return Id != null ? Id.intValue() : 0;
    }

    public int getDoneExamCountAsInt() {
        return DoneExamCount != null ? DoneExamCount.intValue() : 0;
    }

    public int getExamCountAsInt() {
        return ExamCount != null ? ExamCount.intValue() : 0;
    }

    public int getLvAsInt() {
        return Lv != null ? Lv.intValue() : 0;
    }

    public int getTotalLvAsInt() {
        return TotalLv != null ? TotalLv.intValue() : 0;
    }

    public int getRightCountAsInt() {
        return RightCount != null ? RightCount.intValue() : 0;
    }

    public int getSortAsInt() {
        return Sort != null ? Sort.intValue() : 0;
    }

    public int getAllDoneExamCountAsInt() {
        return AllDoneExamCount != null ? AllDoneExamCount.intValue() : 0;
    }

    public boolean isOpenAsBoolean() {
        return IsOpen != null ? IsOpen : false;
    }

    public boolean canGoAsBoolean() {
        return CanGo != null ? CanGo : false;
    }

    // Parcelable implementation
    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeValue(this.Id);
        dest.writeString(this.ExamCode);
        dest.writeString(this.Title);
        dest.writeValue(this.DoneExamCount);
        dest.writeValue(this.ExamCount);
        dest.writeValue(this.Lv);
        dest.writeValue(this.IsOpen);
        dest.writeTypedList(this.Childs);
        dest.writeString(this.ParentCode);
        dest.writeValue(this.TotalLv);
        dest.writeValue(this.RightCount);
        dest.writeString(this.Remark);
        dest.writeString(this.RightRate);
        dest.writeValue(this.CanGo);
        dest.writeValue(this.Sort);
        dest.writeValue(this.AllDoneExamCount);
    }

    public ExamSubChapterBean() {
    }

    protected ExamSubChapterBean(Parcel in) {
        this.Id = (Double) in.readValue(Double.class.getClassLoader());
        this.ExamCode = in.readString();
        this.Title = in.readString();
        this.DoneExamCount = (Double) in.readValue(Double.class.getClassLoader());
        this.ExamCount = (Double) in.readValue(Double.class.getClassLoader());
        this.Lv = (Double) in.readValue(Double.class.getClassLoader());
        this.IsOpen = (Boolean) in.readValue(Boolean.class.getClassLoader());
        this.Childs = in.createTypedArrayList(ExamSubChapterBean.CREATOR);
        this.ParentCode = in.readString();
        this.TotalLv = (Double) in.readValue(Double.class.getClassLoader());
        this.RightCount = (Double) in.readValue(Double.class.getClassLoader());
        this.Remark = in.readString();
        this.RightRate = in.readString();
        this.CanGo = (Boolean) in.readValue(Boolean.class.getClassLoader());
        this.Sort = (Double) in.readValue(Double.class.getClassLoader());
        this.AllDoneExamCount = (Double) in.readValue(Double.class.getClassLoader());
    }

    public static final Creator<ExamSubChapterBean> CREATOR = new Creator<ExamSubChapterBean>() {
        @Override
        public ExamSubChapterBean createFromParcel(Parcel source) {
            return new ExamSubChapterBean(source);
        }

        @Override
        public ExamSubChapterBean[] newArray(int size) {
            return new ExamSubChapterBean[size];
        }
    };

    @Override
    public String toString() {
        return "ExamSubChapterBean{" +
                "Id=" + Id +
                ", ExamCode='" + ExamCode + '\'' +
                ", Title='" + Title + '\'' +
                ", DoneExamCount=" + DoneExamCount +
                ", ExamCount=" + ExamCount +
                ", Lv=" + Lv +
                ", IsOpen=" + IsOpen +
                ", ParentCode='" + ParentCode + '\'' +
                ", TotalLv=" + TotalLv +
                ", RightCount=" + RightCount +
                ", Remark='" + Remark + '\'' +
                ", RightRate='" + RightRate + '\'' +
                ", CanGo=" + CanGo +
                ", Sort=" + Sort +
                ", AllDoneExamCount=" + AllDoneExamCount +
                ", Childs=" + (Childs != null ? Childs.size() + " items" : "null") +
                '}';
    }
}
