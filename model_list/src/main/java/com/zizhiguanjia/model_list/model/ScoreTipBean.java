package com.zizhiguanjia.model_list.model;

import android.os.Parcel;
import android.os.Parcelable;

public class ScoreTipBean implements Parcelable {
    private String txt;
    private String image;

    public String getTxt() {
        return txt;
    }

    public void setTxt(String txt) {
        this.txt = txt;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.txt);
        dest.writeString(this.image);
    }

    public ScoreTipBean() {
    }

    protected ScoreTipBean(Parcel in) {
        this.txt = in.readString();
        this.image = in.readString();
    }

    public static final Parcelable.Creator<ScoreTipBean> CREATOR = new Parcelable.Creator<ScoreTipBean>() {
        @Override
        public ScoreTipBean createFromParcel(Parcel source) {
            return new ScoreTipBean(source);
        }

        @Override
        public ScoreTipBean[] newArray(int size) {
            return new ScoreTipBean[size];
        }
    };
}
