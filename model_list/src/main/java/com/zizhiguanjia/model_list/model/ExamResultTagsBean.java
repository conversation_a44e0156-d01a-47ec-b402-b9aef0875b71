package com.zizhiguanjia.model_list.model;

import android.os.Parcel;
import android.os.Parcelable;

public class ExamResultTagsBean implements Parcelable {

    /**
     * allCount : 0
     * rightCount : 0
     * wrongCount : 0
     * noWriteCount : 0
     * source : 0
     * scoreTip : null
     * titleTip : 案例题只计入题目数量，不判断对错，不计入总分哦
     * allScore : 0
     */
    private int allCount;
    private int rightCount;
    private int wrongCount;
    private int noWriteCount;
    private String source;
    private ScoreTipBean scoreTip;
    private String titleTip;
    private String allScore;
    private String MorePercent;
    private boolean IsPopupScoreFrame;

    public String getMorePercent() {
        return MorePercent;
    }

    public void setMorePercent(String morePercent) {
        MorePercent = morePercent;
    }

    public boolean isPopupScoreFrame() {
        return IsPopupScoreFrame;
    }

    public void setPopupScoreFrame(boolean popupScoreFrame) {
        IsPopupScoreFrame = popupScoreFrame;
    }

    public int getAllCount() {
        return allCount;
    }

    public void setAllCount(int allCount) {
        this.allCount = allCount;
    }

    public int getRightCount() {
        return rightCount;
    }

    public void setRightCount(int rightCount) {
        this.rightCount = rightCount;
    }

    public int getWrongCount() {
        return wrongCount;
    }

    public void setWrongCount(int wrongCount) {
        this.wrongCount = wrongCount;
    }

    public int getNoWriteCount() {
        return noWriteCount;
    }

    public void setNoWriteCount(int noWriteCount) {
        this.noWriteCount = noWriteCount;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }


    public String getTitleTip() {
        return titleTip;
    }

    public void setTitleTip(String titleTip) {
        this.titleTip = titleTip;
    }

    public String getAllScore() {
        return allScore;
    }

    public void setAllScore(String allScore) {
        this.allScore = allScore;
    }

    public ExamResultTagsBean() {
    }

    public ScoreTipBean getScoreTip() {
        return scoreTip;
    }

    public void setScoreTip(ScoreTipBean scoreTip) {
        this.scoreTip = scoreTip;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.allCount);
        dest.writeInt(this.rightCount);
        dest.writeInt(this.wrongCount);
        dest.writeInt(this.noWriteCount);
        dest.writeString(this.source);
        dest.writeParcelable(this.scoreTip, flags);
        dest.writeString(this.titleTip);
        dest.writeString(this.allScore);
    }

    protected ExamResultTagsBean(Parcel in) {
        this.allCount = in.readInt();
        this.rightCount = in.readInt();
        this.wrongCount = in.readInt();
        this.noWriteCount = in.readInt();
        this.source = in.readString();
        this.scoreTip = in.readParcelable(ScoreTipBean.class.getClassLoader());
        this.titleTip = in.readString();
        this.allScore = in.readString();
    }

    public static final Creator<ExamResultTagsBean> CREATOR = new Creator<ExamResultTagsBean>() {
        @Override
        public ExamResultTagsBean createFromParcel(Parcel source) {
            return new ExamResultTagsBean(source);
        }

        @Override
        public ExamResultTagsBean[] newArray(int size) {
            return new ExamResultTagsBean[size];
        }
    };
}
