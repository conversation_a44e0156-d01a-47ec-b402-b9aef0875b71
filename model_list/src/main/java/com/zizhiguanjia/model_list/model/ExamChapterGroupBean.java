package com.zizhiguanjia.model_list.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class ExamChapterGroupBean implements Parcelable {
    private List<ExamChapterBean> list;
    private String MasteryPercent;

    public String getMasteryPercent() {
        return MasteryPercent;
    }

    public void setMasteryPercent(String masteryPercent) {
        MasteryPercent = masteryPercent;
    }

    public List<ExamChapterBean> getList() {
        return list;
    }

    public void setList(List<ExamChapterBean> list) {
        this.list = list;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeTypedList(this.list);
        dest.writeString(this.MasteryPercent);
    }

    public ExamChapterGroupBean() {
    }

    protected ExamChapterGroupBean(Parcel in) {
        this.list = in.createTypedArrayList(ExamChapterBean.CREATOR);
        this.MasteryPercent = in.readString();
    }

    public static final Parcelable.Creator<ExamChapterGroupBean> CREATOR = new Parcelable.Creator<ExamChapterGroupBean>() {
        @Override
        public ExamChapterGroupBean createFromParcel(Parcel source) {
            return new ExamChapterGroupBean(source);
        }

        @Override
        public ExamChapterGroupBean[] newArray(int size) {
            return new ExamChapterGroupBean[size];
        }
    };
}
