package com.zizhiguanjia.model_list.model;

import android.os.Parcel;
import android.os.Parcelable;
import java.util.ArrayList;
import java.util.List;
public class ExamChapterBean implements Parcelable {
    private int Id;
    private String ExamCode;
    private String Title;
    private int DoneExamCount;
    private int ExamCount;
    private int Lv;
    private boolean IsOpen;
    private String ParentCode;
    private int TotalLv;
    private int RightCount;
    private String Remark;
    private String RightRate;
    private boolean CanGo;
    private List<ChildsBean> Childs;
    public int getId() {
        return Id;
    }

    public void setId(int Id) {
        this.Id = Id;
    }

    public String getExamCode() {
        return ExamCode;
    }

    public void setExamCode(String ExamCode) {
        this.ExamCode = ExamCode;
    }

    public String getTitle() {
        return Title;
    }

    public void setTitle(String Title) {
        this.Title = Title;
    }

    public int getDoneExamCount() {
        return DoneExamCount;
    }

    public void setDoneExamCount(int DoneExamCount) {
        this.DoneExamCount = DoneExamCount;
    }

    public int getExamCount() {
        return ExamCount;
    }

    public void setExamCount(int ExamCount) {
        this.ExamCount = ExamCount;
    }

    public int getLv() {
        return Lv;
    }

    public void setLv(int Lv) {
        this.Lv = Lv;
    }

    public boolean isIsOpen() {
        return IsOpen;
    }

    public void setIsOpen(boolean IsOpen) {
        this.IsOpen = IsOpen;
    }

    public String getParentCode() {
        return ParentCode;
    }

    public void setParentCode(String ParentCode) {
        this.ParentCode = ParentCode;
    }

    public int getTotalLv() {
        return TotalLv;
    }

    public void setTotalLv(int TotalLv) {
        this.TotalLv = TotalLv;
    }

    public int getRightCount() {
        return RightCount;
    }

    public void setRightCount(int RightCount) {
        this.RightCount = RightCount;
    }

    public String getRemark() {
        return Remark;
    }

    public void setRemark(String Remark) {
        this.Remark = Remark;
    }

    public String getRightRate() {
        return RightRate;
    }

    public void setRightRate(String RightRate) {
        this.RightRate = RightRate;
    }

    public boolean isCanGo() {
        return CanGo;
    }

    public void setCanGo(boolean CanGo) {
        this.CanGo = CanGo;
    }

    public List<ChildsBean> getChilds() {
        return Childs;
    }

    public void setChilds(List<ChildsBean> Childs) {
        this.Childs = Childs;
    }

    public static class ChildsBean implements Parcelable {

        private int Id;
        private String ExamCode;
        private String Title;
        private int DoneExamCount;
        private int ExamCount;
        private int Lv;
        private boolean IsOpen;
        private String ParentCode;
        private int TotalLv;
        private int RightCount;
        private String Remark;
        private String RightRate;
        private boolean CanGo;
        private int Sort;                    // 新增：排序字段
        private int AllDoneExamCount;        // 新增：全部完成考试数量
        private List<ChildsBean> Childs;     // 修改：使用具体类型而不是Object

        public List<ChildsBean> getChilds() {
            return Childs;
        }

        public void setChilds(List<ChildsBean> childs) {
            Childs = childs;
        }
        public int getId() {
            return Id;
        }

        public void setId(int Id) {
            this.Id = Id;
        }

        public String getExamCode() {
            return ExamCode;
        }

        public void setExamCode(String ExamCode) {
            this.ExamCode = ExamCode;
        }

        public String getTitle() {
            return Title;
        }

        public void setTitle(String Title) {
            this.Title = Title;
        }

        public int getDoneExamCount() {
            return DoneExamCount;
        }

        public void setDoneExamCount(int DoneExamCount) {
            this.DoneExamCount = DoneExamCount;
        }

        public int getExamCount() {
            return ExamCount;
        }

        public void setExamCount(int ExamCount) {
            this.ExamCount = ExamCount;
        }

        public int getLv() {
            return Lv;
        }

        public void setLv(int Lv) {
            this.Lv = Lv;
        }

        public boolean isIsOpen() {
            return IsOpen;
        }

        public void setIsOpen(boolean IsOpen) {
            this.IsOpen = IsOpen;
        }

        public String getParentCode() {
            return ParentCode;
        }

        public void setParentCode(String ParentCode) {
            this.ParentCode = ParentCode;
        }

        public int getTotalLv() {
            return TotalLv;
        }

        public void setTotalLv(int TotalLv) {
            this.TotalLv = TotalLv;
        }

        public int getRightCount() {
            return RightCount;
        }

        public void setRightCount(int RightCount) {
            this.RightCount = RightCount;
        }

        public String getRemark() {
            return Remark;
        }

        public void setRemark(String Remark) {
            this.Remark = Remark;
        }

        public String getRightRate() {
            return RightRate;
        }

        public void setRightRate(String RightRate) {
            this.RightRate = RightRate;
        }

        public boolean isCanGo() {
            return CanGo;
        }

        public void setCanGo(boolean CanGo) {
            this.CanGo = CanGo;
        }

        public int getSort() {
            return Sort;
        }

        public void setSort(int Sort) {
            this.Sort = Sort;
        }

        public int getAllDoneExamCount() {
            return AllDoneExamCount;
        }

        public void setAllDoneExamCount(int AllDoneExamCount) {
            this.AllDoneExamCount = AllDoneExamCount;
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeInt(this.Id);
            dest.writeString(this.ExamCode);
            dest.writeString(this.Title);
            dest.writeInt(this.DoneExamCount);
            dest.writeInt(this.ExamCount);
            dest.writeInt(this.Lv);
            dest.writeByte(this.IsOpen ? (byte) 1 : (byte) 0);
            dest.writeString(this.ParentCode);
            dest.writeInt(this.TotalLv);
            dest.writeInt(this.RightCount);
            dest.writeString(this.Remark);
            dest.writeString(this.RightRate);
            dest.writeByte(this.CanGo ? (byte) 1 : (byte) 0);
            dest.writeInt(this.Sort);
            dest.writeInt(this.AllDoneExamCount);
            dest.writeTypedList(this.Childs);
        }

        public ChildsBean() {
        }

        protected ChildsBean(Parcel in) {
            this.Id = in.readInt();
            this.ExamCode = in.readString();
            this.Title = in.readString();
            this.DoneExamCount = in.readInt();
            this.ExamCount = in.readInt();
            this.Lv = in.readInt();
            this.IsOpen = in.readByte() != 0;
            this.ParentCode = in.readString();
            this.TotalLv = in.readInt();
            this.RightCount = in.readInt();
            this.Remark = in.readString();
            this.RightRate = in.readString();
            this.CanGo = in.readByte() != 0;
            this.Sort = in.readInt();
            this.AllDoneExamCount = in.readInt();
            this.Childs = in.createTypedArrayList(ChildsBean.CREATOR);
        }

        public static final Creator<ChildsBean> CREATOR = new Creator<ChildsBean>() {
            @Override
            public ChildsBean createFromParcel(Parcel source) {
                return new ChildsBean(source);
            }

            @Override
            public ChildsBean[] newArray(int size) {
                return new ChildsBean[size];
            }
        };
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.Id);
        dest.writeString(this.ExamCode);
        dest.writeString(this.Title);
        dest.writeInt(this.DoneExamCount);
        dest.writeInt(this.ExamCount);
        dest.writeInt(this.Lv);
        dest.writeByte(this.IsOpen ? (byte) 1 : (byte) 0);
        dest.writeString(this.ParentCode);
        dest.writeInt(this.TotalLv);
        dest.writeInt(this.RightCount);
        dest.writeString(this.Remark);
        dest.writeString(this.RightRate);
        dest.writeByte(this.CanGo ? (byte) 1 : (byte) 0);
        dest.writeList(this.Childs);
    }

    public ExamChapterBean() {
    }

    protected ExamChapterBean(Parcel in) {
        this.Id = in.readInt();
        this.ExamCode = in.readString();
        this.Title = in.readString();
        this.DoneExamCount = in.readInt();
        this.ExamCount = in.readInt();
        this.Lv = in.readInt();
        this.IsOpen = in.readByte() != 0;
        this.ParentCode = in.readString();
        this.TotalLv = in.readInt();
        this.RightCount = in.readInt();
        this.Remark = in.readString();
        this.RightRate = in.readString();
        this.CanGo = in.readByte() != 0;
        this.Childs = new ArrayList<ChildsBean>();
        in.readList(this.Childs, ChildsBean.class.getClassLoader());
    }

    public static final Parcelable.Creator<ExamChapterBean> CREATOR = new Parcelable.Creator<ExamChapterBean>() {
        @Override
        public ExamChapterBean createFromParcel(Parcel source) {
            return new ExamChapterBean(source);
        }

        @Override
        public ExamChapterBean[] newArray(int size) {
            return new ExamChapterBean[size];
        }
    };
}
