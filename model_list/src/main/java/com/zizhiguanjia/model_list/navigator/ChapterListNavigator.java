package com.zizhiguanjia.model_list.navigator;

import com.zizhiguanjia.model_list.model.ExamChapterBean;

import java.util.List;
public interface ChapterListNavigator {
    void showChapterListData(List<ExamChapterBean> chapterBeans);
    void showContentView();
    void showEmptyView();
    void showFloatView(ExamChapterBean chapterBean);

    /**
     * 显示加载中
     */
    void showLoading(boolean b, String s);

    /**
     * 显示知识点掌握情况描述
     */
    void showMasteryDescription(String masteryPercent);
}
