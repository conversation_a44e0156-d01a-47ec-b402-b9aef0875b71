package com.zizhiguanjia.model_list.navigator;


import com.zizhiguanjia.lib_base.bean.theme.ExamCoreStyleConfigBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigTextSizeBean;
import com.zizhiguanjia.lib_base.bean.theme.ExamCoreThemeConfigThemeBean;
import com.zizhiguanjia.model_list.model.ExamChaptersBean;

import java.util.List;

public interface ExamResultNavigator {
    void initSheetData(List<ExamChaptersBean> examChaptersBeans);
    void setScoreImageTags(String url);
    void showContentView();
    void showErrorAsyView();
    void showTopTitleView(String msg);
    void showRestExam();
    void showZdJd(int p);
    void showCurrntScoreP(String p);
    void showCurrntScoreC(String p);
    void goToLearing(String time);

    void changeShowThemeData(ExamCoreStyleConfigBean styleConfigBean, ExamCoreThemeConfigThemeBean examCoreThemeConfigThemeBean, ExamCoreThemeConfigTextSizeBean textSizeBean);
}
