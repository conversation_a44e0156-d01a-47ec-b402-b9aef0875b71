package com.zizhiguanjia.model_list.view;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import com.binioter.guideview.Component;
import com.wb.lib_utils.utils.DpUtils;
import com.zizhiguanjia.model_list.R;

public class CustomGuideView3 implements Component{
    private Context mContext;

    public CustomGuideView3(Context mContext) {
        this.mContext = mContext;
    }

    @Override
    public View getView(LayoutInflater inflater) {
        LinearLayout ll = (LinearLayout) inflater.inflate(R.layout.list_guide_two_layout, null);
        return ll;
    }

    @Override
    public int getAnchor() {
        return Component.ANCHOR_BOTTOM;
    }

    @Override
    public int getFitPosition() {
        return Component.FIT_END;
    }

    @Override
    public int getXOffset() {
        return -DpUtils.dp2px(mContext,3);
    }

    @Override
    public int getYOffset() {
        return DpUtils.dp2px(mContext,5);
    }
}
