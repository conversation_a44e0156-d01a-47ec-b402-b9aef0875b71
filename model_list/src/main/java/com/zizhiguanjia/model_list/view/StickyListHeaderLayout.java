package com.zizhiguanjia.model_list.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import java.lang.reflect.Method;
import androidx.annotation.AttrRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.zizhiguanjia.lib_base.holder.BaseViewHolder;
import com.zizhiguanjia.model_list.listener.StickCall;

public class StickyListHeaderLayout extends FrameLayout {
    private StickCall stickCall;
    private Context mContext;
    private RecyclerView mRecyclerView;
    //保存吸顶布局的缓存池。它以列表组头的viewType为key,ViewHolder为value对吸顶布局进行保存和回收复用。
    private final SparseArray<BaseViewHolder> mStickyViews = new SparseArray<>();
    //用于在吸顶布局中保存viewType的key。
    private final int VIEW_TAG_TYPE = -101;

    //用于在吸顶布局中保存ViewHolder的key。
    private final int VIEW_TAG_HOLDER = -102;

    //记录当前吸顶的组。
    private int mCurrentStickyGroup = -1;

    //是否吸顶。
    private boolean isSticky = true;

    //是否已经注册了adapter刷新监听
    private boolean isRegisterDataObserver = false;

    public void setStickCall(StickCall stickCall) {
        this.stickCall = stickCall;
    }

    public StickyListHeaderLayout(@NonNull Context context) {
        super(context);
        mContext = context;
    }

    public StickyListHeaderLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
    }

    public StickyListHeaderLayout(@NonNull Context context, @Nullable AttributeSet attrs, @AttrRes int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
    }

    @Override
    public void addView(View child, int index, ViewGroup.LayoutParams params) {
        if (getChildCount() > 0 || !(child instanceof RecyclerView)) {
            //外界只能向StickyHeaderLayout添加一个RecyclerView,而且只能添加RecyclerView。
            throw new IllegalArgumentException("StickyHeaderLayout can host only one direct child --> RecyclerView");
        }
        super.addView(child, index, params);
        mRecyclerView = (RecyclerView) child;
        addOnScrollListener();
//        addStickyLayout();
    }

    /**
     * 添加滚动监听
     */
    int totalScrollY=0;
    private void addOnScrollListener() {
        mRecyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                // 在滚动的时候，需要不断的更新吸顶布局。
//                    int offsetY = recyclerView.computeVerticalScrollOffset();
                if(mRecyclerView.canScrollVertically(-1)){
                }else {
                    totalScrollY=0;
                }
                totalScrollY += dy;
                    if(totalScrollY<=10){
                        if(stickCall!=null){
                            stickCall.showGoView(false,totalScrollY);
                        }
                    }else {
                        if(stickCall!=null){
                            stickCall.showGoView(true,totalScrollY);
                        }
                    }
            }
        });
    }

    /**
     * 添加吸顶容器
     */
//    private void addStickyLayout() {
//        View mView=LayoutInflater.from(getContext()).inflate(R.layout.test,null);//mView.findViewById(R.id.main_ll); //
//        mStickyLayout =new FrameLayout(mContext);
////        mStickyLayout.setVisibility(GONE);
////        mStickyLayout.setBackgroundColor(Color.parseColor("#ffcc22"));
//        mStickyLayout.addView(mView);
//        LayoutParams lp = new LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT,
//                FrameLayout.LayoutParams.WRAP_CONTENT);
//        mStickyLayout.setLayoutParams(lp);
//        super.addView(mStickyLayout, 1, lp);
//    }


    /**
     * 判断是否需要先回收吸顶布局，如果要回收，则回收吸顶布局并返回null。
     * 如果不回收，则返回吸顶布局的ViewHolder。
     * 这样做可以避免频繁的添加和移除吸顶布局。
     *
     * @param viewType
     * @return
     */
//    private BaseViewHolder recycleStickyView(int viewType) {
//        if (mStickyLayout.getChildCount() > 0) {
//            View view = mStickyLayout.getChildAt(0);
//            int type = (int) view.getTag(VIEW_TAG_TYPE);
//            if (type == viewType) {
//                return (BaseViewHolder) view.getTag(VIEW_TAG_HOLDER);
//            } else {
//                recycle();
//            }
//        }
//        return null;
//    }

    /**
     * 回收并移除吸顶布局
     */
//    private void recycle() {
//        mCurrentStickyGroup = -1;
//        if (mStickyLayout.getChildCount() > 0) {
//            View view = mStickyLayout.getChildAt(0);
//            mStickyViews.put((int) (view.getTag(VIEW_TAG_TYPE)),
//                    (BaseViewHolder) (view.getTag(VIEW_TAG_HOLDER)));
//            mStickyLayout.removeAllViews();
//        }
//    }

    /**
     * 从缓存池中获取吸顶布局
     *
     * @param viewType 吸顶布局的viewType
     * @return
     */
    private BaseViewHolder getStickyViewByType(int viewType) {
        return mStickyViews.get(viewType);
    }

    /**
     * 获取当前第一个显示的item .
     */
    private int getFirstVisibleItem() {
        int firstVisibleItem = -1;
        RecyclerView.LayoutManager layout = mRecyclerView.getLayoutManager();
        if (layout != null) {
            if (layout instanceof GridLayoutManager) {
                firstVisibleItem = ((GridLayoutManager) layout).findFirstVisibleItemPosition();
            } else if (layout instanceof LinearLayoutManager) {
                firstVisibleItem = ((LinearLayoutManager) layout).findFirstVisibleItemPosition();
            } else if (layout instanceof StaggeredGridLayoutManager) {
                int[] firstPositions = new int[((StaggeredGridLayoutManager) layout).getSpanCount()];
                ((StaggeredGridLayoutManager) layout).findFirstVisibleItemPositions(firstPositions);
                firstVisibleItem = getMin(firstPositions);
            }
        }
        return firstVisibleItem;
    }

    private int getMin(int[] arr) {
        int min = arr[0];
        for (int x = 1; x < arr.length; x++) {
            if (arr[x] < min)
                min = arr[x];
        }
        return min;
    }

    /**
     * 是否吸顶
     *
     * @return
     */
    public boolean isSticky() {
        return isSticky;
    }

    /**
     * 设置是否吸顶。
     *
     * @param sticky
     */
    public void setSticky(boolean sticky) {
        if (isSticky != sticky) {
            isSticky = sticky;
//            if (mStickyLayout != null) {
//                if (isSticky) {
//                    mStickyLayout.setVisibility(VISIBLE);
//                    updateStickyView(false);
//                } else {
//                    recycle();
//                    mStickyLayout.setVisibility(GONE);
//                }
//            }
        }
    }

    @Override
    protected int computeVerticalScrollOffset() {
        if (mRecyclerView != null) {
            try {
                Method method = View.class.getDeclaredMethod("computeVerticalScrollOffset");
                method.setAccessible(true);
                return (int) method.invoke(mRecyclerView);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return super.computeVerticalScrollOffset();
    }


    @Override
    protected int computeVerticalScrollRange() {
        if (mRecyclerView != null) {
            try {
                Method method = View.class.getDeclaredMethod("computeVerticalScrollRange");
                method.setAccessible(true);
                return (int) method.invoke(mRecyclerView);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return super.computeVerticalScrollRange();
    }

    @Override
    protected int computeVerticalScrollExtent() {
        if (mRecyclerView != null) {
            try {
                Method method = View.class.getDeclaredMethod("computeVerticalScrollExtent");
                method.setAccessible(true);
                return (int) method.invoke(mRecyclerView);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return super.computeVerticalScrollExtent();
    }

    @Override
    public void scrollBy(int x, int y) {
        if (mRecyclerView != null) {
            mRecyclerView.scrollBy(x, y);
        } else {
            super.scrollBy(x, y);
        }
    }

    @Override
    public void scrollTo(int x, int y) {
        if (mRecyclerView != null) {
            mRecyclerView.scrollTo(x, y);
        } else {
            super.scrollTo(x, y);
        }
    }
}
