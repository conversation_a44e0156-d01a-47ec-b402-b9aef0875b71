package com.zizhiguanjia.model_list.api;

import com.zizhiguanjia.lib_base.bean.BaseData;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.model_list.model.CommonListDataBean;
import com.zizhiguanjia.model_list.model.DocMajorsBean;
import com.zizhiguanjia.model_list.model.ExamChapterGroupBean;
import com.zizhiguanjia.model_list.model.ExamDesBean;
import com.zizhiguanjia.model_list.model.ExamResultTagsBean;

import java.util.Map;

import io.reactivex.Observable;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.POST;

public interface ExamListServiceApi {
    @POST(BaseAPI.VERSION_DES+"/API/Exam/GetExamListByMajorId")
    @FormUrlEncoded
    Observable<BaseData<ExamChapterGroupBean>> getExamChapterListData(@FieldMap Map<String,String> paeams);

    @POST(BaseAPI.VERSION_DES+"/API/Exam/GetPaperList")
    @FormUrlEncoded
    Observable<BaseData<CommonListDataBean>> getCommonListData(@FieldMap Map<String,String> paeams);

    @POST(BaseAPI.VERSION_DES+"/API/Exam/GetPaperInfo")
    @FormUrlEncoded
    Observable<BaseData<ExamDesBean>> getExamDes(@FieldMap Map<String,String> paeams);

    @POST(BaseAPI.VERSION_DES+"/API/Exam/GetPaperScore")
    @FormUrlEncoded
    Observable<BaseData<ExamResultTagsBean>> getExamResultTags(@FieldMap Map<String,String> paeams);

    @POST(BaseAPI.VERSION_DES+"/API/Exam/IsCanDoPaper")
    @FormUrlEncoded
    Observable<BaseData<String>> getAutoTextExam(@FieldMap Map<String,String> paeams);

    @POST(BaseAPI.VERSION_DES+"/API/User/UpdateUserExamDate")
    @FormUrlEncoded
    Observable<BaseData> updateExamTime(@FieldMap Map<String,String> paeams);
    @FormUrlEncoded
    @POST(BaseAPI.VERSION_DES+"/API/Exam/DeleteWrongOrCollectionRecore")
    Observable<BaseData> clearErrorOrSaveData(@FieldMap Map<String,String> params);

    @GET(BaseAPI.VERSION_DES+"/API/ExamDoc/GetExamDocMajors")
    Observable<BaseData<DocMajorsBean>> getExamDocMajors();
}
