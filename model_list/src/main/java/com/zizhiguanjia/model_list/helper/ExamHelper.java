package com.zizhiguanjia.model_list.helper;

import com.wb.lib_network.AbstractHttp;
import com.zizhiguanjia.lib_base.config.BaseAPI;
import com.zizhiguanjia.lib_base.config.BaseUrlInterceptor;
import com.zizhiguanjia.lib_base.config.ResponInterceptor;

import java.util.ArrayList;
import java.util.List;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.RequestBody;

public class ExamHelper extends AbstractHttp {
    private static ExamHelper helper;
    public static ExamHelper getInstance(){
        if(helper==null){
            synchronized (ExamHelper.class){
                return helper=new ExamHelper();
            }
        }
        return helper;
    }

    @Override
    protected String baseUrl() {
        return BaseAPI.Base_Url_Api;
    }
    @Override
    protected Iterable<Interceptor> interceptors() {
        final List<Interceptor> interceptorList = new ArrayList<>();
        interceptorList.add(new BaseUrlInterceptor());
        interceptorList.add(new ResponInterceptor());
        return interceptorList;

    }
    public void getExamSheetData(String url, RequestBody requestBody, Callback callback){
        Request request = new Request.Builder()
                .url(BaseAPI.Base_Url_Api+url)
                .post(requestBody)
                .build();
        doAsync(request, callback);
    }
    private void doAsync(Request request, Callback callback) {
        //创建请求会话
        Call call = okHttpClient().newCall(request);
        //异步执行会话请求
        call.enqueue(callback);
    }
}
